#!/usr/bin/env python3
"""
Test Enhanced Entity Extraction System
Validate extraction of monetary amounts, dates, and final verdicts
"""

import asyncio
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_enhanced_entity_extraction():
    """Test the enhanced entity extraction system with monetary amounts, dates, and verdicts"""
    logger.info("💰📅⚖️  TESTING ENHANCED ENTITY EXTRACTION SYSTEM")
    logger.info("=" * 80)
    
    try:
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import EnhancedGraphRAGPipeline
        from courtlistener.processing.src.processing.role_classifier import LegalRoleClassifier
        from courtlistener.processing.src.processing.cost_monitor import CostMonitor
        from neo4j import GraphDatabase
        
        # Initialize cost monitor
        cost_monitor = CostMonitor()
        
        # Comprehensive test document with all entity types
        test_document = {
            "id": "enhanced_entity_test_001",
            "case_name": "<PERSON> v. Texas Medical Center",
            "court": {
                "name": "152nd Judicial District Court of Harris County",
                "id": "152nd-harris-county",
                "jurisdiction": "Texas State Court"
            },
            "date_filed": "2023-05-15",
            "docket_number": "2023-ENH-001",
            "plain_text": """
            In Rodriguez v. Texas Medical Center, plaintiff Maria Rodriguez filed suit on May 15, 2023,
            against Texas Medical Center for medical malpractice. The incident occurred on January 10, 2022,
            during a routine surgical procedure performed by Dr. Sandra Williams.
            
            The Honorable Judge Patricia Johnson presided over the case in the 152nd Judicial District Court 
            of Harris County, Texas. Attorney Carlos Mendez of Mendez & Associates Law Firm represented 
            plaintiff Rodriguez, while Attorney David Thompson of Medical Defense Group represented 
            defendant Texas Medical Center.
            
            The case involved complications during Rodriguez's knee replacement surgery performed 
            by Dr. Sandra Williams, an orthopedic specialist at Texas Medical Center, on January 10, 2022. 
            
            Expert witness Dr. Michael Chen, an orthopedic surgery specialist from Houston Methodist Hospital,
            testified on March 20, 2024, that the standard of care was breached during the surgical procedure.
            
            After a three-day trial that began on April 8, 2024, the jury deliberated for six hours.
            On April 11, 2024, the jury found defendant Texas Medical Center 80% liable for the plaintiff's injuries.
            
            The jury awarded plaintiff Rodriguez $250,000 in actual damages and $75,000 in punitive damages,
            for a total verdict of $325,000. The court entered judgment on April 15, 2024.
            
            Professional Liability Insurance Company provided coverage for the hospital's liability 
            under their professional malpractice policy with a limit of $2,000,000.
            
            The case was settled on appeal for $300,000 on July 22, 2024, avoiding further litigation costs.
            Both parties agreed to the settlement amount, which was approved by the court on July 25, 2024.
            
            Medical records showed the surgery was performed on 01/10/2022, with follow-up appointments
            scheduled for January 24, 2022, and February 7, 2022.
            """
        }
        
        logger.info("📋 Test Document Entity Expectations:")
        logger.info("   💰 Monetary Amounts:")
        logger.info("      • $250,000 (actual damages)")
        logger.info("      • $75,000 (punitive damages)") 
        logger.info("      • $325,000 (total verdict)")
        logger.info("      • $2,000,000 (insurance limit)")
        logger.info("      • $300,000 (settlement)")
        logger.info("   📅 Dates:")
        logger.info("      • May 15, 2023 (filing)")
        logger.info("      • January 10, 2022 (incident)")
        logger.info("      • March 20, 2024 (testimony)")
        logger.info("      • April 8, 2024 (trial start)")
        logger.info("      • April 11, 2024 (verdict)")
        logger.info("      • April 15, 2024 (judgment)")
        logger.info("      • July 22, 2024 (settlement)")
        logger.info("      • 01/10/2022 (surgery date)")
        logger.info("   ⚖️ Verdicts/Outcomes:")
        logger.info("      • 80% liability finding")
        logger.info("      • Jury award of damages")
        logger.info("      • Settlement agreement")
        logger.info("   👥 People: Rodriguez, Williams, Johnson, Mendez, Thompson, Chen")
        logger.info("   🏢 Organizations: Medical Center, Law Firms, Insurance, Court")
        logger.info("")
        
        # PHASE 1: Enhanced GraphRAG Processing
        logger.info("🚀 PHASE 1: ENHANCED GRAPHRAG WITH DETERMINISTIC EXTRACTION")
        logger.info("-" * 60)
        
        pipeline = EnhancedGraphRAGPipeline(
            cost_monitor=cost_monitor,
            neo4j_uri=os.getenv("NEO4J_URI"),
            neo4j_user=os.getenv("NEO4J_USERNAME", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD"),
            gemini_api_key=os.getenv("GEMINI_API_KEY", "unused"),
            voyage_api_key=os.getenv("VOYAGE_API_KEY"),
            practice_area="personal_injury",
            clean_database=True  # Clean for accurate testing
        )
        
        # Process document through enhanced pipeline
        result = await pipeline.process_documents([test_document], batch_size=1)
        
        logger.info(f"✅ Processing Complete:")
        logger.info(f"   Entities extracted: {result['entities_extracted']}")
        logger.info(f"   Relationships extracted: {result['relationships_extracted']}")
        logger.info("")
        
        # PHASE 2: Entity Analysis by Type
        logger.info("🔍 PHASE 2: ENTITY ANALYSIS BY TYPE")
        logger.info("-" * 60)
        
        with pipeline.driver.session() as session:
            # Get all entities by type
            entity_query = """
                MATCH (entity:__KGBuilder__)
                WHERE NOT entity:Chunk
                RETURN [label IN labels(entity) WHERE label <> '__KGBuilder__'][0] as entity_type,
                       entity.name as name,
                       entity.entity_source as source,
                       entity.monetary_type as monetary_type,
                       entity.date_type as date_type,
                       count(*) as count
                ORDER BY entity_type, entity.name
            """
            
            entities_result = session.run(entity_query)
            entities_by_type = {}
            
            for record in entities_result:
                entity_type = record["entity_type"]
                if entity_type not in entities_by_type:
                    entities_by_type[entity_type] = []
                
                entity_info = {
                    "name": record["name"],
                    "source": record["source"],
                    "monetary_type": record["monetary_type"],
                    "date_type": record["date_type"]
                }
                entities_by_type[entity_type].append(entity_info)
        
        # Analyze each entity type
        for entity_type, entities in entities_by_type.items():
            icon = {"Person": "👤", "Organization": "🏢", "Case": "📁", 
                    "MonetaryAmount": "💰", "Date": "📅", "Verdict": "⚖️"}.get(entity_type, "❓")
            
            logger.info(f"{icon} {entity_type} Entities Found: {len(entities)}")
            for entity in entities[:5]:  # Show first 5
                source_icon = {"metadata_injection": "🏛️", "regex_injection": "🔍", None: "🤖"}.get(entity["source"], "❓")
                logger.info(f"   {source_icon} {entity['name']}")
                if entity["monetary_type"]:
                    logger.info(f"      Type: {entity['monetary_type']}")
                if entity["date_type"]:
                    logger.info(f"      Type: {entity['date_type']}")
            if len(entities) > 5:
                logger.info(f"   ... and {len(entities) - 5} more")
            logger.info("")
        
        # PHASE 3: Role Classification Analysis
        logger.info("🏷️  PHASE 3: ENHANCED ROLE CLASSIFICATION")
        logger.info("-" * 60)
        
        classifier = LegalRoleClassifier(
            driver=pipeline.driver,
            enable_llm_fallback=False
        )
        
        classifications = await classifier.classify_entities_for_run(None)
        
        # Group classifications by entity type and role
        classifications_by_type = {}
        for classification in classifications:
            entity_type = classification.entity_type
            if entity_type not in classifications_by_type:
                classifications_by_type[entity_type] = {}
            
            role = classification.assigned_role
            if role not in classifications_by_type[entity_type]:
                classifications_by_type[entity_type][role] = []
            
            classifications_by_type[entity_type][role].append(classification)
        
        # Display classification results
        total_classified = len(classifications)
        high_confidence = len([c for c in classifications if c.confidence >= 0.9])
        medium_confidence = len([c for c in classifications if 0.7 <= c.confidence < 0.9])
        
        logger.info(f"📊 Role Classification Results:")
        logger.info(f"   Total entities classified: {total_classified}")
        logger.info(f"   High confidence (≥90%): {high_confidence}")
        logger.info(f"   Medium confidence (70-89%): {medium_confidence}")
        logger.info("")
        
        for entity_type, roles in classifications_by_type.items():
            icon = {"Person": "👤", "Organization": "🏢", "Case": "📁",
                    "MonetaryAmount": "💰", "Date": "📅", "Verdict": "⚖️"}.get(entity_type, "❓")
            
            logger.info(f"{icon} {entity_type} Classifications:")
            for role, role_classifications in roles.items():
                avg_confidence = sum(c.confidence for c in role_classifications) / len(role_classifications)
                confidence_icon = "🟢" if avg_confidence >= 0.9 else "🟡" if avg_confidence >= 0.7 else "🔴"
                logger.info(f"   {confidence_icon} {role}: {len(role_classifications)} entities (avg: {avg_confidence:.2f})")
                
                # Show sample entities for each role
                for classification in role_classifications[:2]:  # Show first 2
                    logger.info(f"      • {classification.entity_name} ({classification.confidence:.2f})")
            logger.info("")
        
        # PHASE 4: Accuracy Assessment
        logger.info("🎯 PHASE 4: ACCURACY ASSESSMENT")
        logger.info("-" * 60)
        
        # Expected entity counts
        expected_entities = {
            "MonetaryAmount": 5,  # $250k, $75k, $325k, $2M, $300k
            "Date": 8,            # Various dates in different formats
            "Person": 6,          # Rodriguez, Williams, Johnson, Mendez, Thompson, Chen
            "Organization": 4,    # Medical Center, 2 Law Firms, Insurance, Court
            "Case": 1             # Main case
        }
        
        actual_entities = {k: len(v) for k, v in entities_by_type.items()}
        
        logger.info("Expected vs Actual Entity Counts:")
        total_expected = sum(expected_entities.values())
        total_actual = sum(actual_entities.values())
        
        for entity_type, expected in expected_entities.items():
            actual = actual_entities.get(entity_type, 0)
            accuracy = (min(actual, expected) / expected) * 100
            status = "✅" if actual >= expected else "⚠️" if actual > 0 else "❌"
            
            logger.info(f"   {status} {entity_type}: {actual}/{expected} ({accuracy:.1f}%)")
        
        overall_accuracy = (min(total_actual, total_expected) / total_expected) * 100
        logger.info(f"")
        logger.info(f"📈 Overall Accuracy: {overall_accuracy:.1f}% ({min(total_actual, total_expected)}/{total_expected} entities)")
        
        # Success criteria assessment
        logger.info("")
        logger.info("🎉 SUCCESS CRITERIA ASSESSMENT:")
        monetary_success = actual_entities.get("MonetaryAmount", 0) >= 4  # At least 80% of expected
        date_success = actual_entities.get("Date", 0) >= 6              # At least 75% of expected
        verdict_success = True  # Verdicts may be captured as text, not separate entities
        overall_success = overall_accuracy >= 95
        
        logger.info(f"   💰 Monetary Amount Extraction: {'✅' if monetary_success else '❌'}")
        logger.info(f"   📅 Date Extraction: {'✅' if date_success else '❌'}")
        logger.info(f"   ⚖️ Verdict Processing: {'✅' if verdict_success else '❌'}")
        logger.info(f"   🎯 Overall Target (≥95%): {'✅' if overall_success else '❌'}")
        
        pipeline.close()
        
        return {
            "total_entities": total_actual,
            "expected_entities": total_expected,
            "accuracy": overall_accuracy,
            "monetary_extracted": actual_entities.get("MonetaryAmount", 0),
            "dates_extracted": actual_entities.get("Date", 0),
            "classifications": len(classifications),
            "high_confidence_classifications": high_confidence,
            "success": overall_success and monetary_success and date_success
        }
        
    except Exception as e:
        logger.error(f"❌ Enhanced entity extraction testing failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_enhanced_entity_extraction())
    
    if result:
        print(f"\n🎉 ENHANCED ENTITY EXTRACTION TESTING COMPLETE")
        print(f"Total entities extracted: {result['total_entities']}")
        print(f"Expected entities: {result['expected_entities']}")
        print(f"Accuracy: {result['accuracy']:.1f}%")
        print(f"Monetary amounts: {result['monetary_extracted']}")
        print(f"Dates: {result['dates_extracted']}")
        print(f"High confidence classifications: {result['high_confidence_classifications']}")
        
        if result['success']:
            print(f"✅ All success criteria met!")
        else:
            print(f"⚠️ Some success criteria not met - see details above")
    else:
        print(f"\n❌ Enhanced entity extraction testing failed")