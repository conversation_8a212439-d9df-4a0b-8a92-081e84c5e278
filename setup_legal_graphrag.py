#!/usr/bin/env python3
"""
Setup Legal GraphRAG Pipeline with Neo4j SDK
Enhanced for Texas legal domain with precision validation
"""

import os
import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

from neo4j import GraphDatabase
from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
from neo4j_graphrag.llm import VertexAILLM
import vertexai
from vertexai.generative_models import GenerationConfig
from neo4j_graphrag.embeddings.base import Embedder
from neo4j_graphrag.experimental.components.text_splitters.langchain import LangChainTextSplitterAdapter
from langchain_text_splitters import RecursiveCharacterTextSplitter
import voyageai
import google.generativeai as genai

# Import our enhanced contextual embedder
from voyage_contextual_embedder import VoyageContextualEmbedder, ContextAwareLegalChunker, ContextualChunk, EmbeddedChunk
from enhanced_gcs_client import EnhancedGCSClient, LegalDocument

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

@dataclass
class LegalEntitySchema:
    """Legal domain entity and relationship schema for Texas courts"""
    
    # Core legal entities
    entity_types = [
        "Case", "Judge", "Court", "Attorney", "Plaintiff", "Defendant",
        "Statute", "Regulation", "Citation", "Precedent", "Holding",
        "Injury", "Damages", "Settlement", "Expert", "Witness",
        "Insurance", "Verdict", "Appeal"
    ]
    
    # Legal relationship patterns
    relationship_types = [
        "PRESIDED_OVER", "REPRESENTED", "FILED_IN", "AWARDED",
        "TESTIFIED_IN", "CAUSED_BY", "RESULTED_IN", "APPEALED_TO",
        "COVERED_BY", "OPPOSED", "CITED", "OVERRULED", "FOLLOWED",
        "DISTINGUISHED", "REVERSED", "AFFIRMED", "REMANDED"
    ]
    
    # Texas-specific extraction patterns
    extraction_patterns = {
        "texas_courts": [
            r"Texas Supreme Court",
            r"Court of Criminal Appeals",
            r"(\d+)\w+ Court of Appeals",
            r"District Court.*(?:Harris|Dallas|Tarrant|Bexar|Travis|Collin|Denton|Fort Bend).*County",
            r"County Court.*(?:Harris|Dallas|Tarrant|Bexar|Travis|Collin|Denton|Fort Bend)",
            r"Justice of the Peace.*Precinct"
        ],
        "case_citations": [
            r"\d+\s+S\.W\.\d+d\s+\d+",  # SW2d, SW3d
            r"\d+\s+Tex\.\s+\d+",       # Texas Reports
            r"\d+\s+Tex\.App\.\s+\d+",  # Texas Appeals
            r"\d+\s+F\.\d+d\s+\d+",     # Federal
            r"\d+\s+U\.S\.\s+\d+"       # US Reports
        ],
        "damages": [
            r"\$[\d,]+(?:\.\d{2})?(?:\s+(?:million|thousand|billion))?",
            r"(?:actual|punitive|exemplary|nominal)\s+damages",
            r"(?:jury|court)\s+(?:verdict|award)",
            r"settlement.*\$[\d,]+",
            r"judgment.*\$[\d,]+"
        ]
    }

# JSON Schema for GraphRAG structured output
GRAPHRAG_RESPONSE_SCHEMA = {
    "type": "object",
    "properties": {
        "nodes": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "label": {"type": "string"},
                    "properties": {"type": "object"}
                },
                "required": ["id", "label", "properties"]
            }
        },
        "relationships": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "type": {"type": "string"},
                    "start_node_id": {"type": "string"},
                    "end_node_id": {"type": "string"},
                    "properties": {"type": "object"}
                },
                "required": ["type", "start_node_id", "end_node_id"]
            }
        }
    },
    "required": ["nodes", "relationships"]
}

# Use our enhanced contextual embedder instead of the basic one
# The VoyageContextualEmbedder from voyage_contextual_embedder.py already implements 
# the Neo4j GraphRAG Embedder interface with proper contextual embeddings

class LegalGraphRAGPipeline:
    """Enhanced GraphRAG pipeline specifically designed for Texas legal documents with contextual chunking"""
    
    def __init__(self):
        # Initialize connections
        self.neo4j_uri = os.getenv("NEO4J_URI")
        self.neo4j_user = os.getenv("NEO4J_USERNAME", "neo4j")
        self.neo4j_password = os.getenv("NEO4J_PASSWORD")
        self.voyage_api_key = os.getenv("VOYAGE_API_KEY")
        
        # Vertex AI configuration
        self.vertex_project_id = os.getenv("VERTEX_PROJECT_ID")
        self.vertex_location = os.getenv("VERTEX_LOCATION", "us-central1")
        self.vertex_model = os.getenv("VERTEX_MODEL", "gemini-2.5-pro")
        
        if not all([self.neo4j_uri, self.neo4j_password, self.vertex_project_id, self.voyage_api_key]):
            raise ValueError("Missing required environment variables")
        
        # Initialize components
        self.driver = GraphDatabase.driver(
            self.neo4j_uri,
            auth=(self.neo4j_user, self.neo4j_password)
        )
        
        # Initialize Vertex AI
        vertexai.init(project=self.vertex_project_id, location=self.vertex_location)
        
        # Configure Vertex AI Gemini without structured output (let Neo4j SDK handle prompting)
        generation_config = GenerationConfig(
            temperature=0.0  # Use 0 for maximum consistency
        )
        
        self.llm = VertexAILLM(
            model_name=self.vertex_model,
            generation_config=generation_config
        )
        
        # Initialize enhanced contextual embeddings and chunking
        self.embeddings = VoyageContextualEmbedder(
            model="voyage-context-3",
            api_key=self.voyage_api_key,
            output_dimension=1024
        )
        
        # Initialize contextual legal chunker
        self.legal_chunker = ContextAwareLegalChunker(
            chunk_size=2000,
            overlap=200
        )
        
        # Configure legal-aware text splitter (for GraphRAG pipeline compatibility)
        self.text_splitter = self._create_legal_text_splitter()
        
        # Initialize schema
        self.schema = LegalEntitySchema()
        
        # Initialize GCS client for document loading
        self.gcs_client = EnhancedGCSClient()
        
        # Create legal-specific extraction prompt template
        legal_prompt_template = """You extract entities and relationships from legal documents to build a knowledge graph.

Use the graph schema below to constrain allowed labels, relationship types, and properties.
Emit ONE JSON object with keys "nodes" and "relationships" and nothing else.

Legal Entity Types (use these as labels):
- Case: Legal cases and proceedings
- Judge: Presiding judges
- Court: Courts and jurisdictions  
- Attorney: Legal representatives
- Plaintiff: Plaintiffs and petitioners
- Defendant: Defendants and respondents
- Statute: Legal statutes and codes
- Citation: Case citations and precedents
- Damages: Monetary awards and damages
- Verdict: Court decisions and rulings

Legal Relationship Types (use these as relationship types):
- PRESIDED_OVER: Judge presided over case
- REPRESENTED: Attorney represented party
- FILED_IN: Case filed in court
- AWARDED: Damages awarded
- CITED: Citation referenced
- OPPOSED: Parties in opposition
- APPEALED_TO: Case appealed to higher court
- DECIDED: Court decided case

Graph schema:
{schema}

Examples:
{examples}

Input text:
{text}"""
        
        # Create legal graph schema
        from neo4j_graphrag.experimental.components.schema import (
            GraphSchema,
            NodeType,
            RelationshipType
        )
        
        # Define legal node types
        node_types = [
            NodeType(label="Case", description="Legal cases and proceedings"),
            NodeType(label="Judge", description="Presiding judges"),
            NodeType(label="Court", description="Courts and jurisdictions"),
            NodeType(label="Attorney", description="Legal representatives"),
            NodeType(label="Plaintiff", description="Plaintiffs and petitioners"),
            NodeType(label="Defendant", description="Defendants and respondents"),
            NodeType(label="Statute", description="Legal statutes and codes"),
            NodeType(label="Citation", description="Case citations and precedents"),
            NodeType(label="Damages", description="Monetary awards and damages"),
            NodeType(label="Verdict", description="Court decisions and rulings")
        ]
        
        # Define legal relationship types
        relationship_types = [
            RelationshipType(label="PRESIDED_OVER", description="Judge presided over case"),
            RelationshipType(label="REPRESENTED", description="Attorney represented party"),
            RelationshipType(label="FILED_IN", description="Case filed in court"),
            RelationshipType(label="AWARDED", description="Damages awarded"),
            RelationshipType(label="CITED", description="Citation referenced"),
            RelationshipType(label="OPPOSED", description="Parties in opposition"),
            RelationshipType(label="APPEALED_TO", description="Case appealed to higher court"),
            RelationshipType(label="DECIDED", description="Court decided case")
        ]
        
        legal_schema = GraphSchema(
            node_types=tuple(node_types),
            relationship_types=tuple(relationship_types),
            patterns=()  # Empty tuple for patterns
        )
        
        # Create GraphRAG pipeline with Neo4j default prompt (more reliable)
        self.kg_pipeline = SimpleKGPipeline(
            llm=self.llm,
            embedder=self.embeddings,
            driver=self.driver,
            text_splitter=self.text_splitter,
            perform_entity_resolution=True,
            from_pdf=False,  # We're working with text, not PDFs
            schema=legal_schema
        )
        
        logger.info("Legal GraphRAG pipeline initialized successfully")
    
    def _create_legal_text_splitter(self):
        """Create legal document-aware text splitter"""
        # Legal document separators in order of preference
        legal_separators = [
            "\n\n\n",          # Major section breaks
            "\nHELD:",         # Holdings
            "\nCONCLUSION:",   # Conclusions
            "\nDISPOSITION:",  # Court dispositions
            "\n\n",            # Paragraph breaks
            ". ",              # Sentence endings
            "; ",              # Clause breaks
            ", ",              # Comma breaks
            " ",               # Word breaks
            ""                 # Character breaks
        ]
        
        recursive_splitter = RecursiveCharacterTextSplitter(
            chunk_size=2000,
            chunk_overlap=200,
            length_function=len,
            separators=legal_separators,
            keep_separator=True
        )
        
        return LangChainTextSplitterAdapter(text_splitter=recursive_splitter)
    
    def create_legal_extraction_prompt(self, practice_area: str = "personal_injury") -> str:
        """Create legal entity extraction prompt for specific practice area"""
        
        base_prompt = f"""
        You are a Texas legal expert specializing in {practice_area.replace('_', ' ')} law.
        Extract entities and relationships from this legal document text with maximum precision.
        
        ENTITY TYPES TO EXTRACT:
        {', '.join(self.schema.entity_types)}
        
        RELATIONSHIP TYPES TO IDENTIFY:
        {', '.join(self.schema.relationship_types)}
        
        TEXAS-SPECIFIC REQUIREMENTS:
        1. Identify all Texas courts by their proper names and jurisdictions
        2. Extract case citations in Texas format (e.g., 123 S.W.3d 456)
        3. Identify all judges, attorneys, and parties with their roles
        4. Extract damage amounts, settlements, and verdicts with precision
        5. Identify legal precedents and their authority relationships
        
        EXTRACTION RULES:
        - Use exact text from the document for entity names
        - Preserve legal citation formats exactly
        - Include confidence scores for each extraction
        - Maintain case-sensitive proper nouns
        - Extract relationships with source/target entity precision
        
        REQUIRED OUTPUT FORMAT:
        For each entity: name, type, properties, confidence_score
        For each relationship: source_entity, relationship_type, target_entity, confidence_score
        
        Focus on legal accuracy over quantity. Extract only entities you are confident about.
        """
        
        return base_prompt
    
    async def process_document_with_contextual_chunking(self, 
                                                      document: Dict[str, Any],
                                                      practice_area: str = "personal_injury") -> Dict[str, Any]:
        """
        Process a legal document using contextual chunking approach with voyage-context-3
        This is the enhanced method that leverages full document context
        """
        try:
            # Step 1: Create contextual chunks from the document
            logger.info(f"Creating contextual chunks for document {document.get('id')}")
            contextual_chunks = await self.legal_chunker.create_contextual_chunks(document)
            
            if not contextual_chunks:
                logger.warning(f"No valid chunks created for document {document.get('id')}")
                return {"error": "No valid chunks created", "document_id": document.get("id")}
            
            # Step 2: Generate contextual embeddings for all chunks together
            logger.info(f"Generating contextual embeddings for {len(contextual_chunks)} chunks")
            embedded_chunks = await self.embeddings.embed_document_with_full_context(contextual_chunks)
            
            # Step 3: Process each embedded chunk through GraphRAG with context awareness
            all_entities = []
            all_relationships = []
            
            for embedded_chunk in embedded_chunks:
                try:
                    # Extract entities from each chunk using the GraphRAG pipeline
                    # Pass the chunk with its contextual embedding
                    chunk_result = await self.kg_pipeline.run_async(
                        file_path=None, 
                        text=embedded_chunk.text
                    )
                    
                    # Extract entities and relationships from Neo4j
                    entities, relationships = await self._extract_from_neo4j_for_chunk(embedded_chunk.id)
                    
                    # Enhance entities with contextual information
                    enhanced_entities = self._enhance_entities_with_context(entities, embedded_chunk)
                    enhanced_relationships = self._enhance_relationships_with_context(relationships, embedded_chunk)
                    
                    all_entities.extend(enhanced_entities)
                    all_relationships.extend(enhanced_relationships)
                    
                except Exception as e:
                    logger.error(f"Error processing chunk {embedded_chunk.id}: {e}")
                    continue
            
            # Step 4: Create enhanced result with contextual information
            enhanced_result = {
                "document_id": document.get("id"),
                "practice_area": practice_area,
                "processing_method": "contextual_chunking_voyage3",
                "text_length": sum(len(chunk.text) for chunk in contextual_chunks),
                "chunks_created": len(contextual_chunks),
                "embedded_chunks": len(embedded_chunks),
                "entities_extracted": len(all_entities),
                "relationships_extracted": len(all_relationships),
                "processing_timestamp": datetime.utcnow().isoformat(),
                "entities": all_entities,
                "relationships": all_relationships,
                "contextual_chunks": [
                    {
                        "id": chunk.id,
                        "legal_section_type": chunk.legal_section_type,
                        "word_count": chunk.word_count,
                        "section_index": chunk.section_index,
                        "chunk_index": chunk.chunk_index
                    } for chunk in contextual_chunks
                ],
                "quality_metrics": self._calculate_contextual_quality_metrics(
                    all_entities, all_relationships, contextual_chunks
                )
            }
            
            logger.info(f"Successfully processed document {document.get('id')} with contextual chunking: "
                       f"{enhanced_result['entities_extracted']} entities, "
                       f"{enhanced_result['relationships_extracted']} relationships from "
                       f"{enhanced_result['chunks_created']} contextual chunks")
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error in contextual chunking processing for document {document.get('id')}: {str(e)}")
            return {
                "error": str(e),
                "document_id": document.get("id"),
                "processing_method": "contextual_chunking_voyage3",
                "processing_timestamp": datetime.utcnow().isoformat()
            }
    
    async def process_gcs_document_with_contextual_chunking(self, 
                                                          gcs_path: str,
                                                          practice_area: str = "personal_injury") -> Dict[str, Any]:
        """
        Load a document from GCS and process it with contextual chunking
        """
        try:
            # Load document from GCS
            logger.info(f"Loading document from GCS: {gcs_path}")
            legal_document = await self.gcs_client.read_document(gcs_path)
            
            if not legal_document:
                return {"error": "Failed to load document from GCS", "gcs_path": gcs_path}
            
            if not legal_document.is_processable:
                return {"error": "Document not processable", "gcs_path": gcs_path, "word_count": legal_document.word_count}
            
            # Convert LegalDocument to dict format for processing
            document_dict = {
                "id": legal_document.id,
                "case_name": legal_document.case_name,
                "court_name": legal_document.metadata.get("court_type", "Unknown Court"),
                "jurisdiction": legal_document.metadata.get("jurisdiction", "Unknown"),
                "practice_area": practice_area,
                "content": legal_document.content,
                "gcs_path": gcs_path,
                "word_count": legal_document.word_count,
                "file_format": legal_document.file_format
            }
            
            # Process with contextual chunking
            result = await self.process_document_with_contextual_chunking(document_dict, practice_area)
            
            # Add GCS-specific metadata
            if "error" not in result:
                result["gcs_metadata"] = {
                    "gcs_path": gcs_path,
                    "file_format": legal_document.file_format,
                    "original_word_count": legal_document.word_count,
                    "processing_timestamp": legal_document.processing_timestamp.isoformat()
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing GCS document {gcs_path}: {str(e)}")
            return {
                "error": str(e),
                "gcs_path": gcs_path,
                "processing_method": "contextual_chunking_voyage3",
                "processing_timestamp": datetime.utcnow().isoformat()
            }
    
    async def process_legal_document(self, document: Dict[str, Any], 
                                   practice_area: str = "personal_injury") -> Dict[str, Any]:
        """Process a single legal document through the enhanced pipeline"""
        
        try:
            # Extract text content
            text = self._extract_document_text(document)
            if not text or len(text.strip()) < 100:
                logger.warning(f"Document {document.get('id')} has insufficient text content")
                return {"error": "Insufficient text content", "document_id": document.get("id")}
            
            # Set extraction prompt for legal domain
            extraction_prompt = self.create_legal_extraction_prompt(practice_area)
            
            # Process through GraphRAG pipeline
            logger.info(f"Processing document {document.get('id')} through GraphRAG pipeline")
            result = await self.kg_pipeline.run_async(file_path=None, text=text)
            
            # Debug: Log what the pipeline actually returns
            logger.info(f"Pipeline result type: {type(result)}")
            logger.info(f"Pipeline result dict: {result.__dict__}")
            
            # Extract entities and relationships from Neo4j database directly
            # since the pipeline result doesn't contain the extracted entities
            entities, relationships = await self._extract_from_neo4j(document.get('id'))
            
            logger.info(f"Extracted from Neo4j: {len(entities)} entities, {len(relationships)} relationships")
            
            # Enhanced result with legal validation
            enhanced_result = {
                "document_id": document.get("id"),
                "practice_area": practice_area,
                "text_length": len(text),
                "chunks_created": 1,  # Single document processed
                "entities_extracted": len(entities),
                "relationships_extracted": len(relationships),
                "processing_timestamp": datetime.utcnow().isoformat(),
                "entities": entities,
                "relationships": relationships,
                "quality_metrics": self._calculate_quality_metrics_from_counts(len(entities), len(relationships), text)
            }
            
            logger.info(f"Successfully processed document {document.get('id')}: "
                       f"{enhanced_result['entities_extracted']} entities, "
                       f"{enhanced_result['relationships_extracted']} relationships")
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error processing document {document.get('id')}: {str(e)}")
            return {
                "error": str(e),
                "document_id": document.get("id"),
                "processing_timestamp": datetime.utcnow().isoformat()
            }
    
    def _extract_document_text(self, document: Dict[str, Any]) -> str:
        """Extract the best available text from document"""
        # Priority order for text extraction
        text_fields = [
            'plain_text',
            'html_with_citations', 
            'html_lawbox',
            'html',
            'xml_harvard'
        ]
        
        for field in text_fields:
            if field in document and document[field]:
                return str(document[field])
        
        return ""
    
    def _validate_legal_entities(self, entities: List[Any]) -> List[Dict[str, Any]]:
        """Validate and enhance extracted legal entities"""
        validated_entities = []
        
        for entity in entities:
            # Extract entity properties - try multiple field names
            entity_data = {
                "name": getattr(entity, 'name', None) or getattr(entity, 'text', None) or getattr(entity, 'id', str(entity)),
                "type": getattr(entity, 'type', None) or getattr(entity, 'label', 'Unknown'),
                "properties": getattr(entity, 'properties', {}),
                "confidence": getattr(entity, 'confidence', 1.0),  # Default to 1.0 instead of 0.0
                "text": getattr(entity, 'text', None) or getattr(entity, 'name', str(entity))  # Add text field
            }
            
            # Legal domain validation
            if self._is_valid_legal_entity(entity_data):
                validated_entities.append(entity_data)
            else:
                logger.debug(f"Filtered out invalid entity: {entity_data}")
        
        return validated_entities
    
    def _validate_legal_relationships(self, relationships: List[Any]) -> List[Dict[str, Any]]:
        """Validate and enhance extracted legal relationships"""
        validated_relationships = []
        
        for rel in relationships:
            rel_data = {
                "source": getattr(rel, 'source', None) or getattr(rel, 'start_node_id', None) or getattr(rel, 'from', ''),
                "type": getattr(rel, 'type', None) or getattr(rel, 'label', ''),
                "target": getattr(rel, 'target', None) or getattr(rel, 'end_node_id', None) or getattr(rel, 'to', ''),
                "properties": getattr(rel, 'properties', {}),
                "confidence": getattr(rel, 'confidence', 1.0),  # Default to 1.0 instead of 0.0
                "start_node_id": getattr(rel, 'start_node_id', None) or getattr(rel, 'source', ''),
                "end_node_id": getattr(rel, 'end_node_id', None) or getattr(rel, 'target', '')
            }
            
            # Legal domain validation
            if self._is_valid_legal_relationship(rel_data):
                validated_relationships.append(rel_data)
            else:
                logger.debug(f"Filtered out invalid relationship: {rel_data}")
        
        return validated_relationships
    
    def _is_valid_legal_entity(self, entity: Dict[str, Any]) -> bool:
        """Validate entity against legal domain criteria"""
        # More lenient confidence threshold (many entities don't have confidence scores)
        confidence = entity.get('confidence', 1.0)  # Default to 1.0 if no confidence
        if confidence < 0.3:
            return False
        
        # Get entity type (try multiple fields)
        entity_type = entity.get('type') or entity.get('label') or 'Unknown'
        
        # More lenient type validation - allow common legal types
        valid_types = {'Case', 'Judge', 'Court', 'Attorney', 'Plaintiff', 'Defendant', 'Damages', 'Verdict', 'Statute', 'Citation'}
        if entity_type not in valid_types:
            # Allow if it's a reasonable type
            if entity_type in ['Person', 'Organization', 'Date', 'Money', 'Location']:
                pass  # Allow these common types
            else:
                return False
        
        # Name validation (try multiple fields)
        name = entity.get('name') or entity.get('text') or entity.get('id') or ''
        name = str(name).strip()
        if len(name) < 1:  # More lenient - just require non-empty
            return False
        
        return True
    
    def _is_valid_legal_relationship(self, relationship: Dict[str, Any]) -> bool:
        """Validate relationship against legal domain criteria"""
        # Check required fields (try multiple field names)
        source = relationship.get('source') or relationship.get('start_node_id') or relationship.get('from')
        target = relationship.get('target') or relationship.get('end_node_id') or relationship.get('to')
        rel_type = relationship.get('type') or relationship.get('label')
        
        if not all([source, target, rel_type]):
            return False
        
        # More lenient confidence threshold
        confidence = relationship.get('confidence', 1.0)  # Default to 1.0 if no confidence
        if confidence < 0.3:
            return False
        
        # More lenient relationship type validation
        valid_rel_types = {'PRESIDED_OVER', 'REPRESENTED', 'FILED_IN', 'AWARDED', 'CITED', 'OPPOSED', 'APPEALED_TO', 'DECIDED'}
        if rel_type not in valid_rel_types:
            # Allow common relationship types
            if rel_type not in ['WORKS_FOR', 'LOCATED_IN', 'INVOLVED_IN', 'RELATED_TO']:
                return False
        
        return True
    
    def _enhance_entities_with_context(self, entities: List[Dict], embedded_chunk: EmbeddedChunk) -> List[Dict]:
        """Enhance entities with contextual information from embedded chunk"""
        enhanced_entities = []
        
        for entity in entities:
            enhanced_entity = entity.copy()
            enhanced_entity.update({
                "chunk_id": embedded_chunk.id,
                "legal_section_type": embedded_chunk.legal_section_type,
                "document_context": embedded_chunk.document_context,
                "embedding_model": embedded_chunk.embedding_model,
                "contextual_embedding_available": True,
                "chunk_metadata": {
                    "word_count": len(embedded_chunk.text.split()),
                    "text_length": len(embedded_chunk.text),
                    "processing_timestamp": embedded_chunk.processing_timestamp.isoformat()
                }
            })
            enhanced_entities.append(enhanced_entity)
        
        return enhanced_entities
    
    def _enhance_relationships_with_context(self, relationships: List[Dict], embedded_chunk: EmbeddedChunk) -> List[Dict]:
        """Enhance relationships with contextual information from embedded chunk"""
        enhanced_relationships = []
        
        for rel in relationships:
            enhanced_rel = rel.copy()
            enhanced_rel.update({
                "chunk_id": embedded_chunk.id,
                "legal_section_type": embedded_chunk.legal_section_type,
                "document_context": embedded_chunk.document_context,
                "embedding_model": embedded_chunk.embedding_model,
                "contextual_embedding_available": True,
                "chunk_metadata": {
                    "word_count": len(embedded_chunk.text.split()),
                    "text_length": len(embedded_chunk.text),
                    "processing_timestamp": embedded_chunk.processing_timestamp.isoformat()
                }
            })
            enhanced_relationships.append(enhanced_rel)
        
        return enhanced_relationships
    
    def _calculate_contextual_quality_metrics(self, entities: List[Dict], relationships: List[Dict], chunks: List[ContextualChunk]) -> Dict[str, float]:
        """Calculate quality metrics for contextual chunking approach"""
        total_words = sum(chunk.word_count for chunk in chunks)
        total_sections = len(set(chunk.legal_section_type for chunk in chunks))
        
        return {
            "entity_density": len(entities) / max(total_words / 100, 1),
            "relationship_density": len(relationships) / max(total_words / 100, 1),
            "entity_relationship_ratio": len(relationships) / max(len(entities), 1),
            "contextual_coverage": len(entities) / max(len(chunks), 1),
            "legal_section_diversity": total_sections / max(len(chunks), 1),
            "average_chunk_size": total_words / max(len(chunks), 1),
            "entities_per_chunk": len(entities) / max(len(chunks), 1),
            "relationships_per_chunk": len(relationships) / max(len(chunks), 1)
        }
    
    async def _extract_from_neo4j_for_chunk(self, chunk_id: str) -> tuple:
        """Extract entities and relationships from Neo4j for a specific chunk"""
        # For now, use the same extraction as the main method
        # In a production system, you might want to track chunk-specific entities
        return await self._extract_from_neo4j(chunk_id)
    
    async def _extract_from_neo4j(self, document_id: str) -> tuple:
        """Extract entities and relationships from Neo4j after pipeline processing"""
        entities = []
        relationships = []
        
        try:
            with self.driver.session() as session:
                # Query for legal entity nodes with __Entity__ label (Neo4j GraphRAG format)
                entity_query = """
                MATCH (n:__Entity__)
                WHERE (n:Case OR n:Judge OR n:Attorney OR n:Plaintiff OR n:Defendant OR 
                       n:Damages OR n:Court OR n:Statute OR n:Citation OR n:Verdict)
                RETURN labels(n) as labels, properties(n) as properties
                LIMIT 50
                """
                
                entity_result = session.run(entity_query)
                for record in entity_result:
                    # Find the legal entity label (not system labels)
                    legal_labels = [label for label in record["labels"] if label not in ["__KGBuilder__", "__Entity__"]]
                    entity_type = legal_labels[0] if legal_labels else "Unknown"
                    
                    # Extract entity name from properties
                    props = record["properties"] or {}
                    entity_name = props.get("name") or props.get("id") or "unknown"
                    
                    entity = {
                        "name": entity_name,
                        "text": entity_name, 
                        "type": entity_type,
                        "properties": props,
                        "confidence": 1.0
                    }
                    entities.append(entity)
                
                # Query for relationships between legal entities
                rel_query = """
                MATCH (a:__Entity__)-[r]->(b:__Entity__)
                WHERE (a:Case OR a:Judge OR a:Attorney OR a:Plaintiff OR a:Defendant OR 
                       a:Damages OR a:Court OR a:Statute OR a:Citation OR a:Verdict)
                  AND (b:Case OR b:Judge OR b:Attorney OR b:Plaintiff OR b:Defendant OR 
                       b:Damages OR b:Court OR b:Statute OR b:Citation OR b:Verdict)
                RETURN properties(a) as source_props, type(r) as rel_type, properties(b) as target_props, properties(r) as rel_properties
                LIMIT 50
                """
                
                rel_result = session.run(rel_query)
                for record in rel_result:
                    source_name = record["source_props"].get("name", "unknown") if record["source_props"] else "unknown"
                    target_name = record["target_props"].get("name", "unknown") if record["target_props"] else "unknown"
                    
                    relationship = {
                        "source": source_name,
                        "target": target_name,
                        "start_node_id": source_name,
                        "end_node_id": target_name, 
                        "type": record["rel_type"] or "RELATED",
                        "properties": record["rel_properties"] or {},
                        "confidence": 1.0
                    }
                    relationships.append(relationship)
                    
        except Exception as e:
            logger.error(f"Error extracting from Neo4j: {e}")
            
        return entities, relationships
    
    def _calculate_quality_metrics_from_counts(self, entity_count: int, relationship_count: int, text: str) -> Dict[str, float]:
        """Calculate quality metrics from entity and relationship counts"""
        text_length = len(text)
        return {
            "entity_density": entity_count / max(text_length / 100, 1),  # entities per 100 chars
            "relationship_density": relationship_count / max(text_length / 100, 1), # relationships per 100 chars
            "entity_relationship_ratio": relationship_count / max(entity_count, 1),
            "text_coverage": min((entity_count + relationship_count) / max(text_length / 50, 1), 1.0)
        }
    
    def _calculate_quality_metrics(self, result: Any, original_text: str) -> Dict[str, float]:
        """Calculate quality metrics for the extraction"""
        
        entities_count = len(result.nodes) if hasattr(result, 'nodes') else 0
        relationships_count = len(result.relationships) if hasattr(result, 'relationships') else 0
        text_length = len(original_text)
        
        metrics = {
            "entity_density": entities_count / max(text_length / 1000, 1),  # Entities per 1k chars
            "relationship_density": relationships_count / max(text_length / 1000, 1),
            "entity_relationship_ratio": relationships_count / max(entities_count, 1),
            "text_coverage": min(1.0, (entities_count + relationships_count) / max(text_length / 500, 1))
        }
        
        return metrics
    
    def close(self):
        """Clean up connections"""
        if hasattr(self, 'driver'):
            self.driver.close()
        logger.info("Legal GraphRAG pipeline closed")

# Test setup functions
async def test_contextual_chunking_graphrag():
    """Test the enhanced GraphRAG setup with contextual chunking"""
    
    sample_document = {
        "id": "test_contextual_001",
        "case_name": "Regalado v. Rodriguez", 
        "court_name": "13th Court of Appeals",
        "jurisdiction": "TX",
        "practice_area": "family_law",
        "content": """
        NUMBER 13-25-00295-CV
        
        COURT OF APPEALS
        THIRTEENTH DISTRICT OF TEXAS
        CORPUS CHRISTI – EDINBURG
        
        IN THE MATTER OF THE MARRIAGE OF
        SANDRA REGALADO AND SERGIO RODRIGUEZ
        
        On Appeal from the 430th Judicial District Court
        Hidalgo County, Texas
        
        MEMORANDUM OPINION
        
        This is a divorce case involving the division of community property. Sandra Regalado, the appellee, 
        filed for divorce from Sergio Rodriguez, the appellant. The trial court granted the divorce and 
        divided the community property between the parties. Judge Maria Gonzalez presided over the case.
        
        The main issue on appeal involves the trial court's valuation and division of the marital home 
        located at 123 Main Street, McAllen, Texas, which was valued at $250,000. Rodriguez challenges 
        the court's decision to award the home to Regalado.
        
        HELD: The trial court did not abuse its discretion in dividing the community property. 
        The evidence supports the trial court's finding that the property was community property acquired 
        during the marriage. The valuation of $250,000 for the marital home was supported by expert 
        testimony from certified appraiser John Martinez.
        
        DISPOSITION: The judgment of the trial court is AFFIRMED.
        
        THE HONORABLE JUDGE MARY SMITH PRESIDING
        Attorney for Appellant: John Davis, State Bar No. 12345
        Attorney for Appellee: Sarah Wilson, State Bar No. 67890
        """
    }
    
    try:
        print("=== Testing Enhanced GraphRAG with Contextual Chunking ===\n")
        
        pipeline = LegalGraphRAGPipeline()
        
        # Test the new contextual chunking method
        print("1. Testing contextual chunking processing...")
        result = await pipeline.process_document_with_contextual_chunking(sample_document, "family_law")
        
        print("=== Contextual Chunking Results ===")
        print(f"Document ID: {result['document_id']}")
        print(f"Processing Method: {result.get('processing_method', 'N/A')}")
        print(f"Chunks Created: {result.get('chunks_created', 0)}")
        print(f"Embedded Chunks: {result.get('embedded_chunks', 0)}")
        print(f"Entities Extracted: {result.get('entities_extracted', 0)}")
        print(f"Relationships Extracted: {result.get('relationships_extracted', 0)}")
        
        # Show contextual chunks information
        if 'contextual_chunks' in result:
            print(f"\n=== Contextual Chunks ===")
            for chunk_info in result['contextual_chunks']:
                print(f"- Chunk {chunk_info['id']}: {chunk_info['legal_section_type']} "
                      f"({chunk_info['word_count']} words)")
        
        # Show quality metrics
        if 'quality_metrics' in result:
            print(f"\n=== Quality Metrics ===")
            for metric, value in result['quality_metrics'].items():
                print(f"- {metric}: {value:.3f}")
        
        # Show sample entities with context
        if result.get('entities_extracted', 0) > 0:
            print("\n=== Sample Entities (with Context) ===")
            for entity in result['entities'][:3]:  # Show first 3
                print(f"- {entity['name']} ({entity['type']})")
                print(f"  Chunk: {entity.get('chunk_id', 'N/A')}")
                print(f"  Section: {entity.get('legal_section_type', 'N/A')}")
                print(f"  Court: {entity.get('document_context', {}).get('court', 'N/A')}")
                print()
        
        # Show sample relationships with context
        if result.get('relationships_extracted', 0) > 0:
            print("=== Sample Relationships (with Context) ===")
            for rel in result['relationships'][:3]:  # Show first 3
                print(f"- {rel['source']} --{rel['type']}--> {rel['target']}")
                print(f"  Chunk: {rel.get('chunk_id', 'N/A')}")
                print(f"  Section: {rel.get('legal_section_type', 'N/A')}")
                print()
        
        pipeline.close()
        print("✅ Contextual chunking test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Contextual chunking test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_legal_graphrag_setup():
    """Test the legal GraphRAG setup with a sample document (original method)"""
    
    sample_document = {
        "id": "test_case_001",
        "case_name": "Smith v. Jones",
        "court": "Harris County District Court",
        "plain_text": """
        This is a personal injury case where Plaintiff John Smith sued Defendant Mary Jones 
        for damages resulting from a motor vehicle accident. Judge William Brown presided over 
        the case. The jury awarded $50,000 in actual damages and $25,000 in punitive damages. 
        The case was decided on March 15, 2023, in the 55th Judicial District Court of Harris County, Texas.
        Attorney Sarah Wilson represented the plaintiff, while Attorney Robert Davis represented the defendant.
        The court cited precedent from Doe v. Roe, 123 S.W.3d 456 (Tex. 2020).
        """
    }
    
    try:
        pipeline = LegalGraphRAGPipeline()
        result = await pipeline.process_legal_document(sample_document, "personal_injury")
        
        print("=== Legal GraphRAG Test Results (Original Method) ===")
        print(f"Document ID: {result['document_id']}")
        print(f"Entities extracted: {result['entities_extracted']}")
        print(f"Relationships extracted: {result['relationships_extracted']}")
        print(f"Quality metrics: {result['quality_metrics']}")
        
        if result['entities_extracted'] > 0:
            print("\n=== Sample Entities ===")
            for entity in result['entities'][:5]:  # Show first 5
                print(f"- {entity['name']} ({entity['type']}) - Confidence: {entity['confidence']:.2f}")
        
        if result['relationships_extracted'] > 0:
            print("\n=== Sample Relationships ===")
            for rel in result['relationships'][:5]:  # Show first 5
                print(f"- {rel['source']} --{rel['type']}--> {rel['target']} - Confidence: {rel['confidence']:.2f}")
        
        pipeline.close()
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

if __name__ == "__main__":
    # Run the enhanced contextual chunking test
    asyncio.run(test_contextual_chunking_graphrag())