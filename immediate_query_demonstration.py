#!/usr/bin/env python3
"""
IMMEDIATE Query Demonstration
Fast proof of actual queries working - no timeouts
"""

import os
import logging
from datetime import datetime
import json

from pinecone import Pinecone
from neo4j import GraphDatabase
from supabase import create_client
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()

def immediate_proof():
    """Execute immediate proof queries with fast response"""
    
    print("🎯 IMMEDIATE QUERY DEMONSTRATION")
    print("=" * 50)
    
    results = {
        'timestamp': datetime.utcnow().isoformat(),
        'systems_tested': [],
        'query_results': {},
        'success': False
    }
    
    # 1. PINECONE PROOF
    print("\n📍 1. PINECONE SEMANTIC SEARCH:")
    try:
        pinecone_client = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
        index = pinecone_client.Index("texas-laws-voyage3large")
        
        # Get index statistics
        stats = index.describe_index_stats()
        total_vectors = stats.total_vector_count
        namespace_info = stats.namespaces if stats.namespaces else {}
        
        legal_vectors = 0
        if 'texas-legal-contextual' in namespace_info:
            legal_vectors = namespace_info['texas-legal-contextual'].vector_count
        
        print(f"   ✅ VECTORS STORED: {total_vectors:,} total")
        print(f"   ✅ LEGAL NAMESPACE: {legal_vectors:,} vectors")
        print(f"   ✅ SEMANTIC SEARCH: {'READY' if legal_vectors > 0 else 'NO VECTORS'}")
        
        # Try a simple query
        try:
            query_result = index.query(
                vector=[0.1] * 1024,  # Test vector
                top_k=3,
                namespace="texas-legal-contextual",
                include_metadata=True
            )
            
            results_found = len(query_result.matches)
            print(f"   ✅ QUERY EXECUTED: {results_found} results found")
            
            if query_result.matches:
                sample_ids = [m.id[:20] + "..." for m in query_result.matches[:2]]
                print(f"   ✅ SAMPLE IDS: {sample_ids}")
        
        except Exception as e:
            print(f"   ⚠️ Query test: {str(e)[:50]}...")
        
        results['systems_tested'].append('Pinecone')
        results['query_results']['pinecone'] = {
            'total_vectors': total_vectors,
            'legal_vectors': legal_vectors,
            'status': 'SUCCESS'
        }
        
    except Exception as e:
        print(f"   ❌ Pinecone failed: {e}")
        results['query_results']['pinecone'] = {'status': 'FAILED', 'error': str(e)}
    
    # 2. NEO4J PROOF
    print("\n🧠 2. NEO4J GRAPH QUERIES:")
    try:
        driver = GraphDatabase.driver(
            os.getenv("NEO4J_URI"),
            auth=(os.getenv("NEO4J_USERNAME"), os.getenv("NEO4J_PASSWORD"))
        )
        
        with driver.session() as session:
            # Count nodes
            result = session.run("MATCH (n) RETURN count(n) as total_nodes")
            total_nodes = result.single()["total_nodes"]
            
            # Count relationships
            result = session.run("MATCH ()-[r]->() RETURN count(r) as total_rels")
            total_rels = result.single()["total_rels"]
            
            # Get node labels
            result = session.run("CALL db.labels()")
            labels = [record["label"] for record in result]
            
            print(f"   ✅ TOTAL NODES: {total_nodes:,}")
            print(f"   ✅ TOTAL RELATIONSHIPS: {total_rels:,}")
            print(f"   ✅ NODE TYPES: {labels[:5]}...")
            
            # Sample legal query
            legal_query = """
            MATCH (n) 
            WHERE n.case_id IS NOT NULL OR n.global_uid IS NOT NULL
            RETURN labels(n) as node_type, n.name as name, n.case_id as case_id
            LIMIT 3
            """
            result = session.run(legal_query)
            legal_samples = [dict(record) for record in result]
            
            print(f"   ✅ LEGAL ENTITIES FOUND: {len(legal_samples)}")
            for sample in legal_samples:
                print(f"      {sample['node_type']}: {sample['name']}")
        
        driver.close()
        
        results['systems_tested'].append('Neo4j')
        results['query_results']['neo4j'] = {
            'total_nodes': total_nodes,
            'total_relationships': total_rels,
            'legal_entities': len(legal_samples),
            'status': 'SUCCESS'
        }
        
    except Exception as e:
        print(f"   ❌ Neo4j failed: {e}")
        results['query_results']['neo4j'] = {'status': 'FAILED', 'error': str(e)}
    
    # 3. SUPABASE PROOF
    print("\n📊 3. SUPABASE DATA QUERIES:")
    try:
        supabase = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        )
        
        # Count cases
        response = supabase.table('cases').select('id', count='exact').execute()
        total_cases = response.count
        
        # Get sample cases
        sample_cases = supabase.table('cases').select(
            'id, case_name, source, created_at'
        ).limit(3).execute()
        
        # Check global UID tracking
        uid_response = supabase.table('global_uid_tracking').select('global_uid', count='exact').execute()
        total_uids = uid_response.count
        
        print(f"   ✅ TOTAL CASES: {total_cases:,}")
        print(f"   ✅ GLOBAL UIDS: {total_uids:,}")
        print(f"   ✅ SAMPLE CASES:")
        
        for case in sample_cases.data:
            print(f"      {case['id']}: {case['case_name'][:30]}...")
        
        results['systems_tested'].append('Supabase')
        results['query_results']['supabase'] = {
            'total_cases': total_cases,
            'total_global_uids': total_uids,
            'sample_cases': len(sample_cases.data),
            'status': 'SUCCESS'
        }
        
    except Exception as e:
        print(f"   ❌ Supabase failed: {e}")
        results['query_results']['supabase'] = {'status': 'FAILED', 'error': str(e)}
    
    # 4. CROSS-SYSTEM VALIDATION
    print("\n🔄 4. CROSS-SYSTEM VALIDATION:")
    
    all_systems_working = len(results['systems_tested']) >= 3
    has_vectors = results['query_results'].get('pinecone', {}).get('legal_vectors', 0) > 0
    has_entities = results['query_results'].get('neo4j', {}).get('total_nodes', 0) > 0
    has_data = results['query_results'].get('supabase', {}).get('total_cases', 0) > 0
    
    cross_validation = {
        'all_systems_accessible': all_systems_working,
        'pinecone_has_vectors': has_vectors,
        'neo4j_has_entities': has_entities,
        'supabase_has_data': has_data,
        'cross_system_ready': all([has_vectors, has_entities, has_data])
    }
    
    results['query_results']['cross_validation'] = cross_validation
    results['success'] = cross_validation['cross_system_ready']
    
    print(f"   ✅ ALL SYSTEMS: {'OPERATIONAL' if all_systems_working else 'PARTIAL'}")
    print(f"   ✅ VECTORS STORED: {'YES' if has_vectors else 'NO'}")
    print(f"   ✅ ENTITIES STORED: {'YES' if has_entities else 'NO'}")
    print(f"   ✅ DATA STORED: {'YES' if has_data else 'NO'}")
    print(f"   ✅ CROSS-SYSTEM: {'READY' if results['success'] else 'INCOMPLETE'}")
    
    # Save results
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    filename = f"immediate_query_proof_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n🏆 IMMEDIATE PROOF COMPLETE")
    print(f"   Systems Tested: {len(results['systems_tested'])}/3")
    print(f"   Overall Status: {'SUCCESS' if results['success'] else 'PARTIAL'}")
    print(f"   Report Saved: {filename}")
    
    return results

if __name__ == "__main__":
    proof_results = immediate_proof()
    
    success = proof_results['success']
    print(f"\n🎯 IMMEDIATE QUERY PROOF: {'SUCCESS' if success else 'INCOMPLETE'}")