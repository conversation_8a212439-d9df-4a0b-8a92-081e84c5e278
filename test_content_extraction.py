#!/usr/bin/env python3
"""
Test content extraction specifically to see what's happening
"""

import asyncio
from enhanced_gcs_client import EnhancedGCSClient

async def test_content_extraction():
    try:
        client = EnhancedGCSClient()
        
        # Test with specific file
        gcs_path = "TX/clusters/10646628.json.gz"
        print(f"🔍 Testing content extraction: {gcs_path}")
        
        document = await client.read_document(gcs_path)
        
        if document:
            print(f"✅ Document loaded successfully:")
            print(f"   Case Name: {document.case_name}")
            print(f"   Word Count: {document.word_count}")
            print(f"   Content Length: {len(document.content)}")
            print(f"   Content Type Check:")
            print(f"      Is URL? {'http' in document.content}")
            print(f"      Content: '{document.content}'")
            print(f"   Metadata extract:")
            for key in ['court', 'date_filed', 'judges', 'nature_of_suit']:
                if key in document.metadata:
                    print(f"      {key}: {document.metadata[key]}")
        else:
            print(f"❌ Failed to read document")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_content_extraction())