#!/usr/bin/env python3
"""
Hourly Progress Monitor
Provides hard evidence of progress for both bulk loader and classification worker.
"""

import os
import time
import json
from datetime import datetime, timedelta
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment
load_dotenv()

class ProgressMonitor:
    def __init__(self):
        self.supabase = self._init_supabase()
        self.log_file = "progress_monitor.log"
        self.json_file = "progress_history.json"
        self.start_time = datetime.now()
        
    def _init_supabase(self) -> Client:
        """Initialize Supabase client."""
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        return create_client(supabase_url, supabase_key)
    
    def get_database_stats(self) -> dict:
        """Get current database statistics."""
        try:
            # Total TX cases
            total_response = self.supabase.table('cases').select('id', count='exact').eq('jurisdiction', 'TX').execute()
            total_cases = total_response.count
            
            # Classified cases
            classified_response = self.supabase.table('cases').select('id', count='exact').eq('jurisdiction', 'TX').not_.is_('primary_practice_area', 'null').execute()
            classified_cases = classified_response.count
            
            # Unclassified cases
            unclassified_cases = total_cases - classified_cases
            
            # Practice area breakdown
            pi_response = self.supabase.table('cases').select('id', count='exact').eq('jurisdiction', 'TX').eq('primary_practice_area', 'Personal Injury').execute()
            pi_cases = pi_response.count
            
            mm_response = self.supabase.table('cases').select('id', count='exact').eq('jurisdiction', 'TX').eq('primary_practice_area', 'Medical Malpractice').execute()
            mm_cases = mm_response.count
            
            other_response = self.supabase.table('cases').select('id', count='exact').eq('jurisdiction', 'TX').eq('primary_practice_area', 'Other').execute()
            other_cases = other_response.count
            
            # Recent activity (last hour)
            recent_response = self.supabase.table('cases').select('id', count='exact').eq('jurisdiction', 'TX').gte('created_at', (datetime.now().replace(microsecond=0) - timedelta(hours=1)).isoformat()).execute()
            recent_cases = recent_response.count
            
            recent_classified_response = self.supabase.table('cases').select('id', count='exact').eq('jurisdiction', 'TX').gte('practice_area_checked_at', (datetime.now().replace(microsecond=0) - timedelta(hours=1)).isoformat()).execute()
            recent_classified = recent_classified_response.count
            
            return {
                'total_cases': total_cases,
                'classified_cases': classified_cases,
                'unclassified_cases': unclassified_cases,
                'classification_percentage': (classified_cases / total_cases * 100) if total_cases > 0 else 0,
                'personal_injury_cases': pi_cases,
                'medical_malpractice_cases': mm_cases,
                'other_cases': other_cases,
                'recent_new_cases': recent_cases,
                'recent_classified_cases': recent_classified
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def check_processes(self) -> dict:
        """Check if background processes are still running."""
        try:
            # Check screen sessions
            screen_output = os.popen('screen -list 2>&1').read()
            
            bulk_loader_running = 'bulk_loader' in screen_output
            classification_worker_running = 'classification_worker' in screen_output
            
            # Check for state files
            state_files = []
            if os.path.exists('bulk_csv/opinions-2025-07-02.csv.bz2.state'):
                state_files.append('bulk_csv/opinions-2025-07-02.csv.bz2.state')
                
            return {
                'bulk_loader_running': bulk_loader_running,
                'classification_worker_running': classification_worker_running,
                'state_files': state_files,
                'screen_output': screen_output.strip()
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_file_stats(self) -> dict:
        """Get file system evidence of progress."""
        try:
            stats = {}
            
            # State file info
            state_file = 'bulk_csv/opinions-2025-07-02.csv.bz2.state'
            if os.path.exists(state_file):
                with open(state_file, 'r') as f:
                    state_data = json.load(f)
                stats['state_file'] = {
                    'exists': True,
                    'last_modified': datetime.fromtimestamp(os.path.getmtime(state_file)).isoformat(),
                    'data': state_data
                }
            else:
                stats['state_file'] = {'exists': False}
            
            # Log files
            log_files = ['progress_monitor.log', 'enhanced_bulk_loader.log']
            stats['log_files'] = {}
            
            for log_file in log_files:
                if os.path.exists(log_file):
                    stats['log_files'][log_file] = {
                        'exists': True,
                        'size': os.path.getsize(log_file),
                        'last_modified': datetime.fromtimestamp(os.path.getmtime(log_file)).isoformat()
                    }
                else:
                    stats['log_files'][log_file] = {'exists': False}
            
            return stats
            
        except Exception as e:
            return {'error': str(e)}
    
    def generate_report(self) -> dict:
        """Generate comprehensive progress report."""
        timestamp = datetime.now()
        
        report = {
            'timestamp': timestamp.isoformat(),
            'elapsed_time': str(timestamp - self.start_time),
            'database_stats': self.get_database_stats(),
            'process_status': self.check_processes(),
            'file_stats': self.get_file_stats()
        }
        
        return report
    
    def log_progress(self, report: dict):
        """Log progress to file and console."""
        timestamp = report['timestamp']
        db_stats = report['database_stats']
        
        # Console output
        print(f"\n🕐 HOURLY PROGRESS REPORT - {timestamp}")
        print("=" * 60)
        
        if 'error' not in db_stats:
            print(f"📊 TEXAS CASES STATUS:")
            print(f"   Total Cases: {db_stats['total_cases']:,}")
            print(f"   Classified: {db_stats['classified_cases']:,} ({db_stats['classification_percentage']:.1f}%)")
            print(f"   Unclassified: {db_stats['unclassified_cases']:,}")
            print(f"   Personal Injury: {db_stats['personal_injury_cases']:,}")
            print(f"   Medical Malpractice: {db_stats['medical_malpractice_cases']:,}")
            print(f"   Other: {db_stats['other_cases']:,}")
            print(f"   New Cases (last hour): {db_stats['recent_new_cases']:,}")
            print(f"   Classified (last hour): {db_stats['recent_classified_cases']:,}")
        
        process_status = report['process_status']
        print(f"\n🔄 PROCESS STATUS:")
        print(f"   Bulk Loader: {'✅ Running' if process_status.get('bulk_loader_running') else '❌ Stopped'}")
        print(f"   Classification Worker: {'✅ Running' if process_status.get('classification_worker_running') else '❌ Stopped'}")
        
        if process_status.get('state_files'):
            print(f"   State Files: {len(process_status['state_files'])} found")
        
        # File to log
        with open(self.log_file, 'a') as f:
            f.write(f"\n{timestamp} - Progress Report\n")
            f.write(f"Total: {db_stats.get('total_cases', 'N/A')}, ")
            f.write(f"Classified: {db_stats.get('classified_cases', 'N/A')}, ")
            f.write(f"Recent: +{db_stats.get('recent_new_cases', 'N/A')} cases, ")
            f.write(f"+{db_stats.get('recent_classified_cases', 'N/A')} classified\n")
        
        # JSON history
        history = []
        if os.path.exists(self.json_file):
            with open(self.json_file, 'r') as f:
                history = json.load(f)
        
        history.append(report)
        
        with open(self.json_file, 'w') as f:
            json.dump(history, f, indent=2)
        
        print(f"\n📝 Progress logged to: {self.log_file}")
        print(f"📈 History saved to: {self.json_file}")

def main():
    """Run hourly monitoring."""
    from datetime import timedelta
    
    monitor = ProgressMonitor()
    
    print("🚀 Starting Hourly Progress Monitor")
    print("📊 Monitoring both bulk loader and classification worker")
    print("⏰ Reports every hour with hard evidence of progress")
    
    while True:
        try:
            report = monitor.generate_report()
            monitor.log_progress(report)
            
            print(f"\n⏰ Next report in 1 hour...")
            time.sleep(3600)  # 1 hour
            
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
            break
        except Exception as e:
            print(f"\n❌ Error in monitoring: {e}")
            time.sleep(60)  # Wait 1 minute before retrying

if __name__ == "__main__":
    main()
