{"test_id": "final_5system_20250820_141458", "timestamp": "2025-08-20T14:14:58.055473", "systems_tested": 5, "systems_passed": 4, "overall_success": false, "cross_system_tracing": true, "supabase": {"success": true, "operations": {"get_cases_for_processing": {"response_time_ms": 2244.08, "success": true, "cases_found": 5, "sample_case": {"id": "2868286", "case_name": "Texas Case 2868286", "gcs_path": "courtlistener/opinions/2025/cluster_2868286/opinion_2868286.txt", "word_count": 1538}}, "get_case_by_id": {"response_time_ms": 1032.51, "success": true, "case_data": {"id": "2868286", "case_name": "Texas Case 2868286", "case_name_full": "Texas Case 2868286", "court_id": "texas-verified", "jurisdiction": "TX", "date_filed": "2015-09-06", "status": "Published", "docket_number": null, "nature": null, "citation": [], "precedential": true, "source": "courtlistener_csv", "source_id": "2868286", "cluster_id": "2868286", "docket_id": null, "gcs_path": "courtlistener/opinions/2025/cluster_2868286/opinion_2868286.txt", "pinecone_id": null, "opinion_count": 1, "citation_count": 0, "completeness_score": 0, "document_quality": null, "metadata_quality": null, "user_id": null, "user_role": null, "tenant_id": null, "created_at": "2025-08-07T10:20:32.198912+00:00", "updated_at": "2025-08-07T10:20:32.198912+00:00", "practice_areas": [], "primary_practice_area": "Other", "cross_practice_references": 0, "content_hash": "1852fc562b1afd3b6958d4ebcba67e16a5b1d8b4f89c303de62ca6d21aacdcac", "caselaw_access_id": null, "court": null, "historical_era": null, "license_info": null, "source_type": null, "source_url": null, "word_count": 1538, "judge_name": null, "case_type": "civil", "outcome": null, "authority_score": 0, "community_id": null, "embedding_model": "voyage-3-large", "neo4j_node_id": null, "pinecone_namespace": null, "document_type": "unknown", "year_filed": null, "source_window": "unknown", "batch_id": null, "judge_metadata": null, "court_metadata": null, "gcs_cluster_path": null, "gcs_docket_path": null, "gcs_parties_path": null, "chunk_status": null, "chunk_count": 0, "last_chunked_at": null, "appeal_number": null, "trial_cause_number": null, "court_slug": "texas-verified", "matter_uid": null, "author_person_id": null, "panel_person_ids": null, "per_curiam": false, "people_enriched_at": null, "people_checked_at": null, "practice_area_checked_at": "2025-08-15T11:06:30.699131+00:00", "practice_area_confidence": 0.8, "practice_area_reviewed_at": null, "review_decision": null, "review_notes": null}}, "gcs_path_check": {"has_gcs_path": true, "gcs_path": "courtlistener/opinions/2025/cluster_2868286/opinion_2868286.txt", "ready_for_processing": true}}, "production_ready": true}, "neo4j": {"success": true, "operations": {"connection_and_count": {"response_time_ms": 3515.74, "success": true, "node_count": 54}, "entity_crud": {"response_time_ms": 903.87, "success": true, "entity_created": true, "global_uid_stored": true}}}, "pinecone": {"success": false, "operations": {"index_stats": {"response_time_ms": 1375.46, "success": true, "total_vectors": 408, "dimension": 1024}, "vector_upsert": {"response_time_ms": 364.82, "success": true, "vector_id": "test_final_5system_20250820_141458_1755692113"}, "vector_query": {"response_time_ms": 320.64, "success": false, "matches_found": 0, "global_uid_match": false}}}, "vertex_ai": {"success": true, "operations": {"entity_extraction": {"response_time_ms": 2451.1, "success": true, "response_length": 235, "contains_json": true}}}, "voyage_ai": {"success": true, "operations": {"embedding_generation": {"response_time_ms": 774.09, "success": true, "status_code": 200, "embedding_dimension": 1024, "embedding_preview": [-0.023301553, -0.078386813, 0.000987147, -0.032173693, -0.043288242]}}}, "cross_system_tracing_test": {"success": true, "operations": {"neo4j_storage": {"response_time_ms": 3269.68, "success": true, "global_uid": "cross_test_final_5system_20250820_141458_1755692120"}, "pinecone_storage": {"response_time_ms": 1568.45, "success": true, "global_uid": "cross_test_final_5system_20250820_141458_1755692120"}, "neo4j_retrieval": {"response_time_ms": 346.64, "success": true, "global_uid_match": true}, "pinecone_retrieval": {"response_time_ms": 318.73, "success": true, "global_uid_match": true}}, "global_uid": "cross_test_final_5system_20250820_141458_1755692120", "cross_system_consistency": true}, "success_rate": 80.0, "production_ready": false}