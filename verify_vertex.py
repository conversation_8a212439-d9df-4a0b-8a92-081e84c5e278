#!/usr/bin/env python3
"""
Verify Vertex AI setup with structured output for GraphRAG
"""

import os
import json
from jsonschema import validate
import vertexai
from vertexai.generative_models import GenerativeModel, GenerationConfig

# Set your project & region (US)
PROJECT = os.environ.get("VERTEX_PROJECT_ID") or os.popen("gcloud config get-value project").read().strip()
LOCATION = os.environ.get("VERTEX_LOCATION", "us-central1")
MODEL = os.environ.get("VERTEX_MODEL", "gemini-2.5-pro")

print(f"Using PROJECT: {PROJECT}")
print(f"Using LOCATION: {LOCATION}")
print(f"Using MODEL: {MODEL}")

# Initialize Vertex AI
vertexai.init(project=PROJECT, location=LOCATION)

# GraphRAG response schema
RESPONSE_SCHEMA = {
    "type": "object",
    "properties": {
        "nodes": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "label": {"type": "string"},
                    "properties": {"type": "object"}
                },
                "required": ["id", "label", "properties"]
            }
        },
        "relationships": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "type": {"type": "string"},
                    "start_node_id": {"type": "string"},
                    "end_node_id": {"type": "string"},
                    "properties": {"type": "object"}
                },
                "required": ["type", "start_node_id", "end_node_id"]
            }
        }
    },
    "required": ["nodes", "relationships"]
}

# Legal test prompt
prompt = """You extract entities and relationships for a knowledge graph.
Emit ONE JSON object with keys "nodes" and "relationships" and nothing else.

Graph schema:
Allowed labels: Case, Judge, Court, Attorney, Plaintiff, Defendant, Damages
Allowed relationships: PRESIDED_OVER, REPRESENTED, FILED_IN, AWARDED, OPPOSED

Input text:
This is a personal injury case where Plaintiff John Smith sued Defendant Mary Jones for damages resulting from a motor vehicle accident. Judge William Brown presided over the case in Harris County District Court. Attorney Sarah Wilson represented the plaintiff. The jury awarded $50,000 in actual damages.
"""

# Generation config with structured output
cfg = GenerationConfig(
    temperature=0.0,
    response_mime_type="application/json",
    response_schema=RESPONSE_SCHEMA,  # structured output enforcement
)

try:
    print("\nTesting Vertex AI with structured output...")
    
    # Generate content
    model = GenerativeModel(MODEL)
    resp = model.generate_content(prompt, generation_config=cfg)
    
    print(f"Raw response: {resp.text}")
    
    # Parse and validate JSON
    data = json.loads(resp.text)
    validate(data, RESPONSE_SCHEMA)
    
    print("\n✅ SUCCESS! Vertex AI structured output working correctly.")
    print("\nExtracted data:")
    print(json.dumps(data, indent=2))
    
    # Analyze results
    print(f"\nAnalysis:")
    print(f"- Nodes extracted: {len(data['nodes'])}")
    print(f"- Relationships extracted: {len(data['relationships'])}")
    
    for node in data['nodes']:
        print(f"  Node: {node['id']} ({node['label']})")
    
    for rel in data['relationships']:
        print(f"  Relationship: {rel['start_node_id']} --{rel['type']}--> {rel['end_node_id']}")
    
except Exception as e:
    print(f"❌ ERROR: {e}")
    print("\nTroubleshooting:")
    print("1. Ensure you've run the setup script: ./setup_vertex_ai.sh")
    print("2. Authenticate with: gcloud <NAME_EMAIL>")
    print("3. Set environment variables or run: source ~/.zshrc")
    print("4. Check GCP permissions and billing")