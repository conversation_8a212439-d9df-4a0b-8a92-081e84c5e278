#!/usr/bin/env python3
"""
Test Text Differences
Identify why the exact same pipeline works with one text but not another
"""

import asyncio
import os
import logging
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_text_differences():
    """Test different text formats to identify the issue"""
    logger.info("🔍 TESTING TEXT FORMAT DIFFERENCES")
    logger.info("=" * 70)
    
    try:
        import vertexai
        from neo4j_graphrag.llm import VertexAILLM
        from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
        from neo4j_graphrag.experimental.components.text_splitters.langchain import LangChainTextSplitterAdapter
        from langchain_text_splitters import RecursiveCharacterTextSplitter
        from neo4j_graphrag.experimental.components.schema import GraphSchema, NodeType, RelationshipType
        from neo4j import GraphDatabase
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import VoyageAIEmbeddings
        
        # Initialize Vertex AI
        vertex_project_id = os.getenv("VERTEX_PROJECT_ID")
        vertex_location = os.getenv("VERTEX_LOCATION", "us-central1")
        
        if vertex_project_id:
            vertexai.init(project=vertex_project_id, location=vertex_location)
        
        # Different text variations to test
        test_texts = {
            "original_working": {
                "name": "Original Working Text (from debug_simple_kg_pipeline.py)",
                "text": """
        In Anderson v. Memorial Hermann Hospital, plaintiff Sarah Anderson sued Memorial Hermann 
        Hospital for medical malpractice. Judge Robert Wilson presided over the case.
        Attorney Jennifer Martinez represented plaintiff Anderson, while 
        Attorney Michael Davis represented Memorial Hermann Hospital.
        The jury awarded plaintiff $150,000 in damages.
        """
            },
            "enhanced_pipeline_format": {
                "name": "Enhanced Pipeline Format (with metadata headers)",
                "text": """Case: Anderson v. Memorial Hermann Hospital
        Court: Harris County District Court
        Date: 2023-11-01
        Docket: 2023-QUALITY-001

In Anderson v. Memorial Hermann Hospital, plaintiff Sarah Anderson sued Memorial Hermann 
Hospital for medical malpractice. Judge Robert Wilson presided over the case in the 
157th Judicial District Court of Harris County, Texas.

Attorney Jennifer Martinez of Martinez Law Firm represented plaintiff Anderson, while 
Attorney Michael Davis of Healthcare Defense Group represented Memorial Hermann Hospital.

The case involved surgical complications during Anderson's gallbladder removal performed 
by Dr. Lisa Chen at Memorial Hermann Hospital on January 15, 2022. The jury awarded 
plaintiff $150,000 in damages."""
            },
            "single_line": {
                "name": "Single Line Text (no newlines)",
                "text": "In Anderson v. Memorial Hermann Hospital, plaintiff Sarah Anderson sued Memorial Hermann Hospital for medical malpractice. Judge Robert Wilson presided over the case. Attorney Jennifer Martinez represented plaintiff Anderson, while Attorney Michael Davis represented Memorial Hermann Hospital. The jury awarded plaintiff $150,000 in damages."
            },
            "minimal": {
                "name": "Minimal Text",
                "text": "Sarah Anderson sued Memorial Hermann Hospital. Judge Robert Wilson presided. Jennifer Martinez represented Anderson."
            },
            "exact_enhanced_pipeline": {
                "name": "Exact Text from Enhanced Pipeline",
                "text": """Case: Anderson v. Memorial Hermann Hospital
        Court: Harris County District Court
        Date: 2023-11-01
        Docket: 2023-QUALITY-001

In Anderson v. Memorial Hermann Hospital, plaintiff Sarah Anderson sued Memorial Hermann Hospital for medical malpractice. Judge Robert Wilson presided over the case in the 157th Judicial District Court of Harris County, Texas.

Attorney Jennifer Martinez of Martinez Law Firm represented plaintiff Anderson, while Attorney Michael Davis of Healthcare Defense Group represented Memorial Hermann Hospital.

The case involved surgical complications during Anderson's gallbladder removal performed by Dr. Lisa Chen at Memorial Hermann Hospital on January 15, 2022. The jury awarded plaintiff $150,000 in damages."""
            }
        }
        
        # Initialize Neo4j connection
        driver = GraphDatabase.driver(
            os.getenv("NEO4J_URI"),
            auth=(os.getenv("NEO4J_USERNAME", "neo4j"), os.getenv("NEO4J_PASSWORD"))
        )
        
        # Use exact same configuration as working debug script
        schema = GraphSchema(
            node_types=tuple([
                NodeType(label="Person", description="People involved in legal cases"),
                NodeType(label="Organization", description="Organizations and institutions"),
                NodeType(label="Case", description="Legal cases and proceedings"),
            ]),
            relationship_types=tuple([
                RelationshipType(label="REPRESENTED", description="Attorney represented party"),
                RelationshipType(label="SUED", description="Party sued another party"),
                RelationshipType(label="PRESIDED_OVER", description="Judge presided over case"),
            ]),
            patterns=()
        )
        
        llm = VertexAILLM(
            model_name="gemini-2.0-flash-exp",
            model_params={
                "temperature": 0,
                "response_format": {"type": "json_object"}
            }
        )
        
        embedder = VoyageAIEmbeddings(
            model="voyage-3-large",
            api_key=os.getenv("VOYAGE_API_KEY"),
            output_dimension=1024
        )
        
        text_splitter = LangChainTextSplitterAdapter(
            text_splitter=RecursiveCharacterTextSplitter(
                chunk_size=2000,
                chunk_overlap=200,
                length_function=len
            )
        )
        
        # Test each text variation
        results = {}
        
        for text_key, text_config in test_texts.items():
            logger.info(f"🧪 Testing: {text_config['name']}")
            logger.info(f"   Text length: {len(text_config['text'])} characters")
            logger.info(f"   Text preview: {text_config['text'][:100].strip()}...")
            
            try:
                # Clean database
                with driver.session() as session:
                    session.run("MATCH (n:__KGBuilder__) DETACH DELETE n")
                
                # Create pipeline (exact same as working debug script)
                # Import OnError for debugging
                from neo4j_graphrag.experimental.components.entity_relation_extractor import OnError
                
                kg_pipeline = SimpleKGPipeline(
                    llm=llm,
                    embedder=embedder,
                    driver=driver,
                    text_splitter=text_splitter,
                    from_pdf=False,
                    schema=schema,
                    on_error=OnError.RAISE  # Changed from None to RAISE to see errors
                )
                
                # Process with exact same API call as working debug script
                result = await kg_pipeline.run_async(text=text_config['text'])
                
                # Check what was stored
                with driver.session() as session:
                    node_result = session.run("""
                        MATCH (n:__KGBuilder__)
                        WHERE NOT n:Chunk AND n.name IS NOT NULL
                        RETURN count(n) as entity_count,
                               collect(DISTINCT n.name)[0..3] as sample_names
                    """)
                    node_record = node_result.single()
                
                entity_count = node_record['entity_count']
                sample_names = node_record['sample_names']
                
                status = "✅" if entity_count > 0 else "❌"
                logger.info(f"   {status} Result: {entity_count} entities")
                if entity_count > 0:
                    logger.info(f"      Sample: {sample_names}")
                
                results[text_key] = {
                    "success": True,
                    "entity_count": entity_count,
                    "sample_names": sample_names,
                    "text_length": len(text_config['text']),
                    "newline_count": text_config['text'].count('\n')
                }
                
            except Exception as e:
                logger.error(f"   ❌ Failed: {e}")
                results[text_key] = {
                    "success": False,
                    "error": str(e),
                    "text_length": len(text_config['text']),
                    "newline_count": text_config['text'].count('\n')
                }
            
            logger.info("")
        
        # Analysis
        logger.info("🎯 TEXT FORMAT ANALYSIS:")
        logger.info("")
        
        working = []
        failing = []
        
        for text_key, result in results.items():
            if result["success"] and result["entity_count"] > 0:
                working.append(text_key)
                logger.info(f"   ✅ {text_key}: {result['entity_count']} entities (length: {result['text_length']}, newlines: {result['newline_count']})")
            else:
                failing.append(text_key)
                logger.info(f"   ❌ {text_key}: 0 entities (length: {result['text_length']}, newlines: {result['newline_count']})")
        
        logger.info("")
        
        # Identify pattern
        if "original_working" in working and "enhanced_pipeline_format" in failing:
            logger.info("🔧 PATTERN IDENTIFIED:")
            logger.info("   The original simple text works")
            logger.info("   The formatted text with metadata headers fails")
            logger.info("   Issue: The metadata headers may be confusing the LLM")
        
        if all(result["entity_count"] == 0 for result in results.values() if result["success"]):
            logger.info("⚠️  ALL TEXTS FAILING!")
            logger.info("   This suggests an environmental issue, not a text format issue")
        
        driver.close()
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Text testing failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_text_differences())
    
    if result:
        print(f"\n🔍 TEXT TESTING COMPLETE")
        
        working = [k for k, v in result.items() if v.get("success") and v.get("entity_count", 0) > 0]
        failing = [k for k, v in result.items() if not v.get("success") or v.get("entity_count", 0) == 0]
        
        print(f"Working texts: {working}")
        print(f"Failing texts: {failing}")
        
        if working and failing:
            print(f"\n🚨 Found text format issue!")
            print(f"   Working texts have these characteristics:")
            for w in working:
                print(f"      {w}: length={result[w]['text_length']}, newlines={result[w]['newline_count']}")
        elif not working:
            print(f"\n❌ All text formats failing - environmental issue likely")
        else:
            print(f"\n✅ All text formats working")