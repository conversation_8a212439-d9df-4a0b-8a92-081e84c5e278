#!/usr/bin/env python3
"""
Test GraphRAG Configuration Fix
Tests the enhanced GraphRAG pipeline with the PDF processing fix
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from datetime import datetime

# Add the courtlistener processing path
sys.path.append(str(Path(__file__).parent / "courtlistener" / "processing" / "src"))

try:
    from processing.enhanced_graphrag_pipeline import EnhancedGraphRAGPipeline
    from processing.cost_monitor import CostMonitor
    from supabase import create_client
    IMPORTS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Import error: {e}")
    IMPORTS_AVAILABLE = False

from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('graphrag_fix_test.log')
    ]
)

logger = logging.getLogger(__name__)
load_dotenv()

async def test_single_case_processing():
    """Test processing a single case with the fixed GraphRAG configuration"""
    
    if not IMPORTS_AVAILABLE:
        print("❌ Required imports not available")
        return False
    
    logger.info("🔧 Testing GraphRAG Configuration Fix")
    logger.info("=" * 60)
    
    try:
        # Initialize components
        logger.info("🔧 Initializing components...")
        
        cost_monitor = CostMonitor()
        
        # Initialize Supabase
        supabase = create_client(
            os.getenv("SUPABASE_URL"),
            os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        )
        
        # Initialize fixed GraphRAG pipeline
        logger.info("🧠 Initializing Enhanced GraphRAG Pipeline...")
        graphrag_pipeline = EnhancedGraphRAGPipeline(
            cost_monitor=cost_monitor,
            neo4j_uri=os.getenv("NEO4J_URI"),
            neo4j_user=os.getenv("NEO4J_USER"),
            neo4j_password=os.getenv("NEO4J_PASSWORD"),
            gemini_api_key=os.getenv("GEMINI_API_KEY"),
            voyage_api_key=os.getenv("VOYAGE_API_KEY"),
            practice_area="personal_injury",
            enable_schema_discovery=True
        )
        
        # Test with a known good case
        test_case_id = "11113702"  # Known to exist
        
        logger.info(f"📋 Testing case: {test_case_id}")
        
        # Get case data from Supabase
        logger.info("🔍 Retrieving case data...")
        response = supabase.table('cases').select('*').eq('id', test_case_id).execute()
        
        if not response.data:
            logger.error(f"❌ Case {test_case_id} not found in Supabase")
            return False
        
        case_data = response.data[0]
        logger.info(f"✅ Retrieved case: {case_data.get('case_name', 'Unknown')}")
        
        # Check available content
        content_types = []
        for content_type in ['plain_text', 'html_lawbox', 'html', 'html_with_citations']:
            if content_type in case_data and case_data[content_type]:
                content_length = len(case_data[content_type])
                content_types.append(f"{content_type}: {content_length} chars")
        
        logger.info(f"📄 Available content: {', '.join(content_types)}")
        
        # Prepare document for GraphRAG
        document_for_graphrag = {
            'id': case_data.get('id'),
            'case_name': case_data.get('case_name', ''),
            'court': case_data.get('court', {}),
            'date_filed': case_data.get('date_created'),
            'plain_text': case_data.get('plain_text', ''),
            'html_lawbox': case_data.get('html_lawbox', ''),
            'panel': case_data.get('panel', []),
            'docket_number': case_data.get('docket_number', '')
        }
        
        # Test GraphRAG processing
        logger.info("🧠 Testing GraphRAG processing...")
        start_time = datetime.utcnow()
        
        try:
            result = await graphrag_pipeline.process_documents(
                documents=[document_for_graphrag],
                batch_size=1,
                enable_caching=True
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Analyze results
            logger.info("📊 GraphRAG Processing Results:")
            logger.info(f"   Processing Time: {processing_time:.2f}s")
            logger.info(f"   Documents Processed: {result.get('processed', 0)}")
            logger.info(f"   Entities Extracted: {result.get('entities_extracted', 0)}")
            logger.info(f"   Relationships Extracted: {result.get('relationships_extracted', 0)}")
            logger.info(f"   Total Cost: ${result.get('costs', {}).get('total', 0):.4f}")
            
            if result.get('errors'):
                logger.warning("⚠️ Processing Errors:")
                for error in result['errors']:
                    logger.warning(f"   - {error}")
            
            # Determine success
            success = (
                result.get('processed', 0) > 0 and
                result.get('entities_extracted', 0) > 0 and
                len(result.get('errors', [])) == 0
            )
            
            if success:
                logger.info("🎉 GraphRAG Fix Test: SUCCESS")
                logger.info("✅ Pipeline processing entities successfully")
                logger.info("✅ No PDF configuration errors")
                logger.info("✅ Ready for full pilot re-execution")
            else:
                logger.warning("⚠️ GraphRAG Fix Test: PARTIAL SUCCESS")
                if result.get('entities_extracted', 0) == 0:
                    logger.warning("   - No entities extracted (may need prompt tuning)")
                if result.get('errors'):
                    logger.warning("   - Processing errors still present")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ GraphRAG processing failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # Clean up
            if graphrag_pipeline:
                graphrag_pipeline.close()
    
    except Exception as e:
        logger.error(f"❌ Test setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function"""
    
    try:
        logger.info("🚀 Starting GraphRAG Configuration Fix Test")
        
        success = await test_single_case_processing()
        
        if success:
            print("\n🎉 GRAPHRAG FIX TEST: SUCCESS!")
            print("✅ Enhanced GraphRAG Pipeline is working correctly")
            print("✅ Ready to re-run full pilot processing")
            print("🚀 Next step: Execute complete pilot with all 10 cases")
        else:
            print("\n⚠️ GRAPHRAG FIX TEST: NEEDS ATTENTION")
            print("📋 Review logs for specific issues")
            print("🔧 Additional configuration may be needed")
            
    except KeyboardInterrupt:
        logger.info("🛑 Test interrupted by user")
    except Exception as e:
        logger.error(f"❌ Fatal error in test: {e}")

if __name__ == "__main__":
    asyncio.run(main())