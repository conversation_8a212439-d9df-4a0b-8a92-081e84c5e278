#!/usr/bin/env python3
"""
Load environment from .env file and verify setup
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Load environment variables from .env file
from dotenv import load_dotenv

# Load .env file from current directory
env_path = Path(__file__).parent / '.env'
load_dotenv(env_path)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_all_connections():
    """Test all API connections with loaded environment variables"""
    
    logger.info("=== Testing All API Connections ===")
    
    # Check what we have
    required_vars = {
        "NEO4J_URI": os.getenv("NEO4J_URI"),
        "NEO4J_PASSWORD": os.getenv("NEO4J_PASSWORD"), 
        "NEO4J_USER": os.getenv("NEO4J_USER", "neo4j"),
        "GEMINI_API_KEY": os.getenv("GEMINI_API_KEY"),
        "VOYAGE_API_KEY": os.getenv("VOYAGE_API_KEY"),
        "PINECONE_API_KEY": os.getenv("PINECONE_API_KEY"),
        "PINECONE_INDEX_NAME": os.getenv("PINECONE_INDEX_NAME"),
        "SUPABASE_URL": os.getenv("SUPABASE_URL"),
        "SUPABASE_SERVICE_ROLE_KEY": os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    }
    
    logger.info("Environment variables loaded:")
    for var, value in required_vars.items():
        if value:
            logger.info(f"✓ {var}: {'*' * len(value[-10:]) if len(value) > 10 else '*' * len(value)}")
        else:
            logger.error(f"✗ {var}: NOT SET")
    
    # Count missing vars
    missing = [k for k, v in required_vars.items() if not v]
    if missing:
        logger.error(f"Missing environment variables: {missing}")
        return False
    
    # Test Neo4j
    try:
        logger.info("Testing Neo4j connection...")
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            required_vars["NEO4J_URI"], 
            auth=(required_vars["NEO4J_USER"], required_vars["NEO4J_PASSWORD"])
        )
        
        with driver.session() as session:
            result = session.run("RETURN 'Hello Neo4j' as message")
            message = result.single()["message"]
            logger.info(f"✓ Neo4j: {message}")
        
        driver.close()
        
    except Exception as e:
        logger.error(f"✗ Neo4j failed: {e}")
        return False
    
    # Test Gemini
    try:
        logger.info("Testing Gemini API...")
        import google.generativeai as genai
        genai.configure(api_key=required_vars["GEMINI_API_KEY"])
        
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        response = model.generate_content("Hello")
        logger.info("✓ Gemini API working")
        
    except Exception as e:
        logger.error(f"✗ Gemini failed: {e}")
        return False
    
    # Test Voyage
    try:
        logger.info("Testing Voyage AI...")
        import voyageai
        client = voyageai.Client(api_key=required_vars["VOYAGE_API_KEY"])
        
        result = client.embed(["test"], model="voyage-3-large")
        if result.embeddings:
            logger.info("✓ Voyage AI working")
        
    except Exception as e:
        logger.error(f"✗ Voyage AI failed: {e}")
        return False
    
    # Test Pinecone
    try:
        logger.info("Testing Pinecone...")
        from pinecone import Pinecone
        pc = Pinecone(api_key=required_vars["PINECONE_API_KEY"])
        
        indexes = pc.list_indexes()
        logger.info("✓ Pinecone working")
        
    except Exception as e:
        logger.error(f"✗ Pinecone failed: {e}")
        return False
    
    # Test Supabase
    try:
        logger.info("Testing Supabase...")
        from supabase import create_client
        supabase = create_client(
            required_vars["SUPABASE_URL"],
            required_vars["SUPABASE_SERVICE_ROLE_KEY"]
        )
        
        result = supabase.table("cases").select("id").limit(1).execute()
        logger.info("✓ Supabase working")
        
    except Exception as e:
        logger.error(f"✗ Supabase failed: {e}")
        return False
    
    # Test imports
    try:
        import langextract
        from neo4j_graphrag.llm import OpenAILLM
        logger.info("✓ All packages imported successfully")
        
    except Exception as e:
        logger.error(f"✗ Package import failed: {e}")
        return False
    
    logger.info("🎉 ALL TESTS PASSED! Ready for validation pipeline.")
    return True

async def main():
    success = await test_all_connections()
    
    if success:
        print("\n" + "="*60)
        print("✅ SETUP VERIFICATION COMPLETED SUCCESSFULLY!")
        print("="*60)
        print("\n🚀 NEXT STEPS:")
        print("1. Run validation pipeline: python validation_pipeline.py")
        print("2. Test individual components:")
        print("   - python setup_legal_graphrag.py")
        print("   - python setup_langextract_legal.py")
        print("3. Launch monitoring: streamlit run monitoring_dashboard.py")
        print("="*60)
    else:
        print("\n❌ Setup verification failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())