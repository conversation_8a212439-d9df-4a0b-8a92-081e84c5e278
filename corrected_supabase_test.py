#!/usr/bin/env python3
"""
Corrected Supabase Test with Proper Schema
Tests all operations with correct column names
"""

import asyncio
import os
import time
import logging
import json
from datetime import datetime
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
load_dotenv()

class CorrectedSupabaseTest:
    """
    Test Supabase with correct schema and optimized queries
    """
    
    def __init__(self):
        self.test_id = f"supabase_corrected_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"🎯 Corrected Supabase Test: {self.test_id}")
    
    async def test_connection_and_basic_operations(self):
        """Test connection and basic CRUD operations"""
        try:
            from supabase import create_client
            
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
            
            if not supabase_url or not supabase_key:
                return {"success": False, "error": "Missing Supabase credentials"}
            
            client = create_client(supabase_url, supabase_key)
            
            operations = {}
            
            # Test 1: Basic connection and row retrieval
            start_time = time.time()
            result = client.table("cases").select("id", "case_name", "jurisdiction").limit(3).execute()
            operations["basic_select"] = {
                "success": True,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "rows_returned": len(result.data),
                "sample_data": result.data[:2] if result.data else []
            }
            
            # Test 2: Filtered query using existing columns
            start_time = time.time()
            result = client.table("cases").select("id", "case_name").eq("jurisdiction", "TX").limit(5).execute()
            operations["jurisdiction_filter"] = {
                "success": True,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "rows_returned": len(result.data)
            }
            
            # Test 3: Source-based query (courtlistener_csv exists in data)
            start_time = time.time()
            result = client.table("cases").select("id", "source").eq("source", "courtlistener_csv").limit(3).execute()
            operations["source_filter"] = {
                "success": True,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "rows_returned": len(result.data)
            }
            
            # Test 4: Range query (word count)
            start_time = time.time()
            result = client.table("cases").select("id", "word_count").gte("word_count", 100).limit(5).execute()
            operations["range_query"] = {
                "success": True,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "rows_returned": len(result.data)
            }
            
            # Test 5: Complex select with multiple columns
            start_time = time.time()
            result = client.table("cases").select(
                "id", "case_name", "jurisdiction", "practice_areas", "word_count", "date_filed"
            ).limit(2).execute()
            operations["complex_select"] = {
                "success": True,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "rows_returned": len(result.data),
                "sample_practice_areas": [r.get("practice_areas", []) for r in result.data] if result.data else []
            }
            
            return {
                "success": True,
                "connection": "working",
                "operations": operations,
                "url": supabase_url[:30] + "..."
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_insert_update_delete(self):
        """Test write operations with proper schema"""
        try:
            from supabase import create_client
            
            client = create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_SERVICE_ROLE_KEY"))
            
            operations = {}
            
            # Test Insert with correct schema
            test_case = {
                "case_name": f"Test Case {self.test_id}",
                "case_name_full": f"Full Test Case {self.test_id}",
                "court_id": "test-court",
                "jurisdiction": "TX",
                "date_filed": "2024-08-20",
                "status": "Test",
                "source": "test_system",
                "source_id": f"test_{int(time.time())}",
                "cluster_id": f"cluster_{int(time.time())}",
                "opinion_count": 1,
                "citation_count": 0,
                "completeness_score": 100,
                "practice_areas": ["test", "system_validation"],
                "word_count": 150,
                "case_type": "test",
                "document_type": "test_document",
                "court_slug": "test-court"
            }
            
            start_time = time.time()
            insert_result = client.table("cases").insert(test_case).execute()
            operations["insert"] = {
                "success": len(insert_result.data) > 0,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "inserted_id": insert_result.data[0]["id"] if insert_result.data else None
            }
            
            if operations["insert"]["success"]:
                test_id = operations["insert"]["inserted_id"]
                
                # Test Update
                start_time = time.time()
                update_result = client.table("cases").update({
                    "case_name": f"Updated Test Case {self.test_id}",
                    "word_count": 200
                }).eq("id", test_id).execute()
                
                operations["update"] = {
                    "success": len(update_result.data) > 0,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2)
                }
                
                # Test Delete (cleanup)
                start_time = time.time()
                delete_result = client.table("cases").delete().eq("id", test_id).execute()
                operations["delete"] = {
                    "success": len(delete_result.data) > 0,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "deleted_id": delete_result.data[0]["id"] if delete_result.data else None
                }
            
            return {"success": True, "operations": operations}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_performance_benchmarks(self):
        """Test performance benchmarks for production readiness"""
        try:
            from supabase import create_client
            
            client = create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_SERVICE_ROLE_KEY"))
            
            benchmarks = {}
            
            # Benchmark 1: Fast ID lookup (should be under 500ms)
            start_time = time.time()
            result = client.table("cases").select("*").eq("id", "4570055").execute()
            benchmarks["id_lookup"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "target_ms": 500,
                "passed": (time.time() - start_time) * 1000 < 500,
                "found_record": len(result.data) > 0
            }
            
            # Benchmark 2: Jurisdiction filter (should be under 2 seconds)
            start_time = time.time()
            result = client.table("cases").select("id", "case_name").eq("jurisdiction", "TX").limit(10).execute()
            benchmarks["jurisdiction_filter"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "target_ms": 2000,
                "passed": (time.time() - start_time) * 1000 < 2000,
                "records_found": len(result.data)
            }
            
            # Benchmark 3: Recent records (should be under 1 second)
            start_time = time.time()
            result = client.table("cases").select("id", "created_at").order("created_at", desc=True).limit(5).execute()
            benchmarks["recent_records"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "target_ms": 1000,
                "passed": (time.time() - start_time) * 1000 < 1000,
                "records_found": len(result.data)
            }
            
            # Benchmark 4: Word count range query
            start_time = time.time()
            result = client.table("cases").select("id", "word_count").gte("word_count", 1000).limit(5).execute()
            benchmarks["range_query"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "target_ms": 1500,
                "passed": (time.time() - start_time) * 1000 < 1500,
                "records_found": len(result.data)
            }
            
            # Calculate overall performance score
            passed_benchmarks = sum(1 for b in benchmarks.values() if b["passed"])
            total_benchmarks = len(benchmarks)
            
            return {
                "success": True,
                "benchmarks": benchmarks,
                "performance_score": (passed_benchmarks / total_benchmarks) * 100,
                "passed_benchmarks": passed_benchmarks,
                "total_benchmarks": total_benchmarks
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def run_comprehensive_supabase_test(self):
        """Run complete corrected Supabase test suite"""
        
        logger.info("🎯 RUNNING COMPREHENSIVE CORRECTED SUPABASE TEST")
        logger.info("=" * 70)
        
        results = {
            "test_id": self.test_id,
            "timestamp": datetime.now().isoformat(),
            "overall_success": False,
            "production_ready": False
        }
        
        # Test 1: Connection and Basic Operations
        logger.info("🔍 Testing connection and basic operations...")
        connection_result = await self.test_connection_and_basic_operations()
        results["connection_and_operations"] = connection_result
        
        if connection_result["success"]:
            avg_response = sum(op["response_time_ms"] for op in connection_result["operations"].values()) / len(connection_result["operations"])
            logger.info(f"✅ Basic operations: Average {avg_response:.1f}ms response time")
        else:
            logger.error(f"❌ Basic operations failed: {connection_result['error']}")
            return results
        
        # Test 2: Write Operations
        logger.info("🔍 Testing write operations (INSERT/UPDATE/DELETE)...")
        write_result = await self.test_insert_update_delete()
        results["write_operations"] = write_result
        
        if write_result["success"]:
            logger.info("✅ Write operations: All CRUD operations working")
        else:
            logger.error(f"❌ Write operations failed: {write_result['error']}")
        
        # Test 3: Performance Benchmarks
        logger.info("🔍 Testing performance benchmarks...")
        performance_result = await self.test_performance_benchmarks()
        results["performance"] = performance_result
        
        if performance_result["success"]:
            score = performance_result["performance_score"]
            passed = performance_result["passed_benchmarks"]
            total = performance_result["total_benchmarks"]
            logger.info(f"✅ Performance: {score:.1f}% ({passed}/{total} benchmarks passed)")
        else:
            logger.error(f"❌ Performance tests failed: {performance_result['error']}")
        
        # Overall success determination
        results["overall_success"] = (
            connection_result["success"] and 
            write_result["success"] and 
            performance_result["success"]
        )
        
        # Production readiness (need >80% performance and all operations working)
        results["production_ready"] = (
            results["overall_success"] and
            performance_result.get("performance_score", 0) >= 80
        )
        
        return results

async def main():
    """Execute corrected Supabase testing"""
    
    print("🎯 COMPREHENSIVE CORRECTED SUPABASE TEST")
    print("=" * 60)
    print("Testing with proper schema and optimized queries")
    print()
    
    try:
        tester = CorrectedSupabaseTest()
        results = await tester.run_comprehensive_supabase_test()
        
        print(f"\n🏆 CORRECTED SUPABASE TEST RESULTS")
        print("=" * 50)
        print(f"Overall Success: {'✅ YES' if results['overall_success'] else '❌ NO'}")
        print(f"Production Ready: {'✅ YES' if results['production_ready'] else '❌ NO'}")
        
        if results.get("connection_and_operations", {}).get("success"):
            ops = results["connection_and_operations"]["operations"]
            avg_time = sum(op["response_time_ms"] for op in ops.values()) / len(ops)
            print(f"Average Response Time: {avg_time:.1f}ms")
        
        if results.get("performance", {}).get("success"):
            perf = results["performance"]
            print(f"Performance Score: {perf['performance_score']:.1f}% ({perf['passed_benchmarks']}/{perf['total_benchmarks']} benchmarks)")
        
        # Save results
        filename = f"corrected_supabase_test_{tester.test_id}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results: {filename}")
        
        if results["production_ready"]:
            print("\n🎉 SUCCESS: Supabase is PRODUCTION READY!")
            print("   All operations working efficiently")
            print("   Performance benchmarks passed")
        else:
            print("\n⚠️ Issues found - needs optimization")
            
        return results["production_ready"]
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)