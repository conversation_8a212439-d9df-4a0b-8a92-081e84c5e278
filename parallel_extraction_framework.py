#!/usr/bin/env python3
"""
Parallel Extraction Test Framework
Compare GraphRAG vs LangExtract on legal documents
"""

import asyncio
import time
import os
import json
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

@dataclass
class ExtractionMetrics:
    """Metrics for extraction comparison"""
    processor: str
    processing_time: float
    total_entities: int
    entity_types: Dict[str, int]
    cost_estimate: float
    confidence_avg: float
    errors: List[str]

@dataclass
class ComparisonResult:
    """Results of comparing two extraction methods"""
    document_id: str
    text_length: int
    graphrag_metrics: ExtractionMetrics
    langextract_metrics: ExtractionMetrics
    quality_score: Dict[str, float]
    
class ParallelExtractionFramework:
    """Framework for comparing GraphRAG and LangExtract"""
    
    def __init__(self):
        self.graphrag_pipeline = None
        self.langextract_examples = None
        self._setup_components()
    
    def _setup_components(self):
        """Initialize both extraction systems"""
        try:
            # Setup GraphRAG pipeline
            from setup_legal_graphrag import LegalGraphRAGPipeline
            self.graphrag_pipeline = LegalGraphRAGPipeline()
            print("✅ GraphRAG pipeline initialized")
            
            # Setup LangExtract examples
            self._setup_langextract_examples()
            print("✅ LangExtract examples initialized")
            
        except Exception as e:
            print(f"❌ Failed to setup components: {e}")
    
    def _setup_langextract_examples(self):
        """Setup LangExtract with legal examples"""
        import langextract
        from langextract import data
        
        # Create comprehensive legal examples
        example_text = """
        Judge Robert Wilson presided over the case Smith v. Jones in Harris County District Court. 
        Attorney Sarah Brown represented plaintiff John Smith against defendant Mary Jones. 
        The court awarded $25,000 in compensatory damages and $10,000 in punitive damages.
        The case was filed on January 15, 2023, and decided on March 20, 2023.
        """
        
        extractions = [
            data.Extraction("JUDGE", "Robert Wilson", description="Presiding judge"),
            data.Extraction("CASE", "Smith v. Jones", description="Case name"),
            data.Extraction("COURT", "Harris County District Court", description="Court venue"),
            data.Extraction("ATTORNEY", "Sarah Brown", description="Representing attorney"),
            data.Extraction("PLAINTIFF", "John Smith", description="Plaintiff party"),
            data.Extraction("DEFENDANT", "Mary Jones", description="Defendant party"),
            data.Extraction("MONEY", "$25,000", description="Compensatory damages"),
            data.Extraction("MONEY", "$10,000", description="Punitive damages"),
            data.Extraction("DATE", "January 15, 2023", description="Filing date"),
            data.Extraction("DATE", "March 20, 2023", description="Decision date")
        ]
        
        self.langextract_examples = [data.ExampleData(
            text=example_text,
            extractions=extractions
        )]
    
    async def extract_with_graphrag(self, document: Dict[str, Any]) -> ExtractionMetrics:
        """Extract entities using GraphRAG pipeline"""
        start_time = time.time()
        errors = []
        
        try:
            # Process through GraphRAG pipeline
            result = await self.graphrag_pipeline.process_legal_document(document)
            
            processing_time = time.time() - start_time
            
            # Extract metrics
            total_entities = result.get('entities_extracted', 0)
            entity_types = {}
            confidence_scores = []
            
            if 'entities' in result and result['entities']:
                for entity in result['entities']:
                    entity_type = entity.get('type', 'Unknown')
                    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
                    
                    confidence = entity.get('confidence', 0.0)
                    if confidence > 0:
                        confidence_scores.append(confidence)
            
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
            
            # Estimate cost (Vertex AI + voyage-context-3)
            # Vertex AI: ~$0.00015 per 1K tokens, voyage-context-3: ~$0.0001 per 1K tokens
            text_length = len(document.get('plain_text', ''))
            estimated_tokens = text_length / 4  # Rough token estimate
            cost_estimate = (estimated_tokens / 1000) * (0.00015 + 0.0001)
            
            return ExtractionMetrics(
                processor="GraphRAG",
                processing_time=processing_time,
                total_entities=total_entities,
                entity_types=entity_types,
                cost_estimate=cost_estimate,
                confidence_avg=avg_confidence,
                errors=errors
            )
            
        except Exception as e:
            errors.append(str(e))
            processing_time = time.time() - start_time
            
            return ExtractionMetrics(
                processor="GraphRAG",
                processing_time=processing_time,
                total_entities=0,
                entity_types={},
                cost_estimate=0.0,
                confidence_avg=0.0,
                errors=errors
            )
    
    def extract_with_langextract(self, document: Dict[str, Any]) -> ExtractionMetrics:
        """Extract entities using LangExtract"""
        start_time = time.time()
        errors = []
        
        try:
            import langextract
            
            text = document.get('plain_text', '')
            api_key = os.getenv('GEMINI_API_KEY')
            
            if not api_key:
                errors.append("No GEMINI_API_KEY found")
                return ExtractionMetrics("LangExtract", 0.0, 0, {}, 0.0, 0.0, errors)
            
            # Perform LangExtract extraction
            result = langextract.extract(
                text_or_documents=text,
                examples=self.langextract_examples,
                prompt_description="Extract comprehensive legal information including judges, attorneys, parties, courts, monetary awards, dates, and case information from court documents.",
                api_key=api_key,
                temperature=0.0,
                model_id='gemini-2.5-flash',
                debug=False
            )
            
            processing_time = time.time() - start_time
            
            # Extract metrics
            entity_types = {}
            total_entities = 0
            
            if hasattr(result, 'extractions') and result.extractions:
                for extraction in result.extractions:
                    entity_type = extraction.extraction_class
                    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
                    total_entities += 1
            
            # Estimate cost (Gemini Flash)
            text_length = len(text)
            estimated_tokens = text_length / 4
            cost_estimate = (estimated_tokens / 1000) * 0.000075  # Gemini Flash pricing
            
            return ExtractionMetrics(
                processor="LangExtract",
                processing_time=processing_time,
                total_entities=total_entities,
                entity_types=entity_types,
                cost_estimate=cost_estimate,
                confidence_avg=0.9,  # LangExtract doesn't provide confidence scores
                errors=errors
            )
            
        except Exception as e:
            errors.append(str(e))
            processing_time = time.time() - start_time
            
            return ExtractionMetrics(
                processor="LangExtract",
                processing_time=processing_time,
                total_entities=0,
                entity_types={},
                cost_estimate=0.0,
                confidence_avg=0.0,
                errors=errors
            )
    
    async def compare_extraction(self, document: Dict[str, Any]) -> ComparisonResult:
        """Compare GraphRAG vs LangExtract on a single document"""
        print(f"🔄 Processing document: {document.get('id', 'unknown')}")
        
        # Run both extractions in parallel
        graphrag_task = self.extract_with_graphrag(document)
        langextract_task = asyncio.to_thread(self.extract_with_langextract, document)
        
        graphrag_metrics, langextract_metrics = await asyncio.gather(
            graphrag_task, langextract_task, return_exceptions=True
        )
        
        # Handle exceptions
        if isinstance(graphrag_metrics, Exception):
            print(f"❌ GraphRAG failed: {graphrag_metrics}")
            graphrag_metrics = ExtractionMetrics("GraphRAG", 0.0, 0, {}, 0.0, 0.0, [str(graphrag_metrics)])
        
        if isinstance(langextract_metrics, Exception):
            print(f"❌ LangExtract failed: {langextract_metrics}")
            langextract_metrics = ExtractionMetrics("LangExtract", 0.0, 0, {}, 0.0, 0.0, [str(langextract_metrics)])
        
        # Calculate quality scores
        quality_score = self._calculate_quality_score(graphrag_metrics, langextract_metrics)
        
        return ComparisonResult(
            document_id=document.get('id', 'unknown'),
            text_length=len(document.get('plain_text', '')),
            graphrag_metrics=graphrag_metrics,
            langextract_metrics=langextract_metrics,
            quality_score=quality_score
        )
    
    def _calculate_quality_score(self, graphrag: ExtractionMetrics, langextract: ExtractionMetrics) -> Dict[str, float]:
        """Calculate quality comparison scores"""
        quality = {}
        
        # Entity count comparison
        if graphrag.total_entities > 0 or langextract.total_entities > 0:
            max_entities = max(graphrag.total_entities, langextract.total_entities)
            quality['entity_coverage_graphrag'] = graphrag.total_entities / max_entities
            quality['entity_coverage_langextract'] = langextract.total_entities / max_entities
        else:
            quality['entity_coverage_graphrag'] = 0.0
            quality['entity_coverage_langextract'] = 0.0
        
        # Processing speed comparison (entities per second)
        if graphrag.processing_time > 0:
            quality['speed_graphrag'] = graphrag.total_entities / graphrag.processing_time
        else:
            quality['speed_graphrag'] = 0.0
            
        if langextract.processing_time > 0:
            quality['speed_langextract'] = langextract.total_entities / langextract.processing_time
        else:
            quality['speed_langextract'] = 0.0
        
        # Cost efficiency (entities per dollar)
        if graphrag.cost_estimate > 0:
            quality['cost_efficiency_graphrag'] = graphrag.total_entities / graphrag.cost_estimate
        else:
            quality['cost_efficiency_graphrag'] = float('inf') if graphrag.total_entities > 0 else 0.0
            
        if langextract.cost_estimate > 0:
            quality['cost_efficiency_langextract'] = langextract.total_entities / langextract.cost_estimate
        else:
            quality['cost_efficiency_langextract'] = float('inf') if langextract.total_entities > 0 else 0.0
        
        # Error rate
        quality['error_rate_graphrag'] = len(graphrag.errors) / max(1, graphrag.total_entities + len(graphrag.errors))
        quality['error_rate_langextract'] = len(langextract.errors) / max(1, langextract.total_entities + len(langextract.errors))
        
        return quality
    
    def create_test_documents(self) -> List[Dict[str, Any]]:
        """Create diverse test documents for comparison"""
        test_documents = [
            {
                "id": "simple_pi_case",
                "case_name": "Brown v. Smith Motor Co.",
                "practice_area": "personal_injury",
                "plain_text": """
                This is a straightforward personal injury case where Plaintiff Robert Brown sued 
                Defendant Smith Motor Company for damages resulting from a defective vehicle. 
                Judge Maria Rodriguez presided over the case in Travis County District Court. 
                Attorney Jennifer Williams represented the plaintiff. The jury awarded $75,000 
                in compensatory damages on June 15, 2023.
                """
            },
            {
                "id": "complex_multi_party",
                "case_name": "Multiple Plaintiffs v. Industrial Corp.",
                "practice_area": "mass_tort",
                "plain_text": """
                In this complex mass tort litigation, forty-seven plaintiffs including lead plaintiff 
                John Martinez filed suit against Industrial Corporation and its subsidiaries. 
                Chief Judge William Thompson appointed special master Susan Chen to oversee discovery. 
                The case involves environmental contamination affecting the communities of Cedar Park, 
                Round Rock, and Georgetown. Attorney Michael Johnson leads the plaintiff's steering committee, 
                while Jones & Associates represents the corporate defendants. The court previously 
                awarded interim damages of $2.5 million, with final settlement negotiations ongoing. 
                Expert witness Dr. Sarah Kim testified regarding environmental impact. The case cites 
                precedent from EPA v. Chemical Solutions, 456 F.3d 789 (5th Cir. 2019).
                """
            },
            {
                "id": "citation_heavy",
                "case_name": "State v. Doe",
                "practice_area": "criminal_defense",
                "plain_text": """
                The Texas Court of Criminal Appeals in State v. Doe, 567 S.W.3d 123 (Tex. Crim. App. 2023), 
                held that the exclusionary rule applies to evidence obtained through unconstitutional searches. 
                The court distinguished this case from Smith v. State, 234 S.W.3d 567 (Tex. Crim. App. 2020), 
                and Johnson v. State, 345 S.W.3d 890 (Tex. Crim. App. 2021). Judge Martinez wrote the 
                majority opinion, with Judge Brown concurring and Judge Wilson dissenting. The case was 
                originally heard in the 299th District Court of Travis County before Judge Lisa Garcia. 
                Defense attorney David Chen argued the motion to suppress, while prosecutor Jennifer Lee 
                represented the State of Texas.
                """
            },
            {
                "id": "commercial_litigation",
                "case_name": "Tech Corp v. Startup Inc.",
                "practice_area": "commercial",
                "plain_text": """
                Technology Corporation filed a breach of contract claim against Startup Innovations Inc. 
                for $10 million in damages in the District Court of Dallas County. Judge Robert Kim 
                presided over the bench trial from March 1-15, 2024. The dispute centers on a software 
                licensing agreement signed in January 2022. Attorney Susan Martinez from BigLaw Partners 
                represented Technology Corporation, while solo practitioner James Wilson defended 
                Startup Innovations. The court found in favor of plaintiff and awarded $7.5 million 
                in actual damages plus $500,000 in attorney's fees under the prevailing party clause.
                """
            },
            {
                "id": "family_law_case",
                "case_name": "In re Marriage of Johnson",
                "practice_area": "family_law",
                "plain_text": """
                In this contested divorce case, Jennifer Johnson petitioned for dissolution of marriage 
                from Michael Johnson in the 330th Family District Court of Dallas County. Judge Carol 
                Thompson handled the proceedings. The case involved disputes over child custody of 
                minor children Emma (age 12) and Tyler (age 8), as well as division of marital property 
                including the family home valued at $450,000 and retirement accounts totaling $300,000. 
                Attorney Lisa Chen represented the petitioner, while attorney Robert Davis represented 
                the respondent. Dr. Maria Gonzalez served as the child custody evaluator. The court 
                awarded joint custody and ordered spousal support of $3,000 per month for 36 months.
                """
            }
        ]
        
        return test_documents
    
    async def run_comprehensive_comparison(self) -> Dict[str, Any]:
        """Run comprehensive comparison across multiple documents"""
        print("=== Starting Comprehensive Extraction Comparison ===\n")
        
        test_documents = self.create_test_documents()
        results = []
        
        for document in test_documents:
            result = await self.compare_extraction(document)
            results.append(result)
            
            # Print immediate results
            self._print_comparison_result(result)
        
        # Calculate aggregate statistics
        aggregate_stats = self._calculate_aggregate_stats(results)
        
        # Generate final report
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "total_documents": len(test_documents),
            "individual_results": [asdict(result) for result in results],
            "aggregate_statistics": aggregate_stats,
            "recommendations": self._generate_recommendations(aggregate_stats)
        }
        
        return report
    
    def _print_comparison_result(self, result: ComparisonResult):
        """Print individual comparison result"""
        print(f"\n📊 Document: {result.document_id}")
        print(f"   Text length: {result.text_length} characters")
        print(f"   GraphRAG: {result.graphrag_metrics.total_entities} entities in {result.graphrag_metrics.processing_time:.2f}s")
        print(f"   LangExtract: {result.langextract_metrics.total_entities} entities in {result.langextract_metrics.processing_time:.2f}s")
        
        if result.graphrag_metrics.errors:
            print(f"   ⚠️  GraphRAG errors: {result.graphrag_metrics.errors}")
        if result.langextract_metrics.errors:
            print(f"   ⚠️  LangExtract errors: {result.langextract_metrics.errors}")
    
    def _calculate_aggregate_stats(self, results: List[ComparisonResult]) -> Dict[str, Any]:
        """Calculate aggregate statistics across all results"""
        if not results:
            return {}
        
        graphrag_times = [r.graphrag_metrics.processing_time for r in results if not r.graphrag_metrics.errors]
        langextract_times = [r.langextract_metrics.processing_time for r in results if not r.langextract_metrics.errors]
        
        graphrag_entities = [r.graphrag_metrics.total_entities for r in results]
        langextract_entities = [r.langextract_metrics.total_entities for r in results]
        
        graphrag_costs = [r.graphrag_metrics.cost_estimate for r in results]
        langextract_costs = [r.langextract_metrics.cost_estimate for r in results]
        
        return {
            "processing_time": {
                "graphrag_avg": sum(graphrag_times) / len(graphrag_times) if graphrag_times else 0,
                "langextract_avg": sum(langextract_times) / len(langextract_times) if langextract_times else 0,
                "graphrag_total": sum(graphrag_times),
                "langextract_total": sum(langextract_times)
            },
            "entity_extraction": {
                "graphrag_total": sum(graphrag_entities),
                "langextract_total": sum(langextract_entities),
                "graphrag_avg": sum(graphrag_entities) / len(results),
                "langextract_avg": sum(langextract_entities) / len(results)
            },
            "cost_analysis": {
                "graphrag_total": sum(graphrag_costs),
                "langextract_total": sum(langextract_costs),
                "graphrag_per_document": sum(graphrag_costs) / len(results),
                "langextract_per_document": sum(langextract_costs) / len(results)
            },
            "error_analysis": {
                "graphrag_errors": sum(1 for r in results if r.graphrag_metrics.errors),
                "langextract_errors": sum(1 for r in results if r.langextract_metrics.errors)
            }
        }
    
    def _generate_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on comparison results"""
        recommendations = []
        
        # Performance recommendation
        if stats["processing_time"]["graphrag_avg"] < stats["processing_time"]["langextract_avg"]:
            recommendations.append("GraphRAG shows better processing speed performance")
        else:
            recommendations.append("LangExtract shows better processing speed performance")
        
        # Entity extraction recommendation
        if stats["entity_extraction"]["graphrag_avg"] > stats["entity_extraction"]["langextract_avg"]:
            recommendations.append("GraphRAG extracts more entities on average")
        else:
            recommendations.append("LangExtract extracts more entities on average")
        
        # Cost recommendation
        if stats["cost_analysis"]["graphrag_per_document"] < stats["cost_analysis"]["langextract_per_document"]:
            recommendations.append("GraphRAG is more cost-effective per document")
        else:
            recommendations.append("LangExtract is more cost-effective per document")
        
        # Error rate recommendation
        if stats["error_analysis"]["graphrag_errors"] < stats["error_analysis"]["langextract_errors"]:
            recommendations.append("GraphRAG shows better reliability (fewer errors)")
        else:
            recommendations.append("LangExtract shows better reliability (fewer errors)")
        
        return recommendations

async def main():
    """Run the comprehensive comparison"""
    framework = ParallelExtractionFramework()
    
    if not framework.graphrag_pipeline or not framework.langextract_examples:
        print("❌ Failed to initialize extraction frameworks")
        return
    
    # Run comprehensive comparison
    report = await framework.run_comprehensive_comparison()
    
    # Save report
    report_filename = f"extraction_comparison_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_filename, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Full report saved to: {report_filename}")
    
    # Print summary
    print(f"\n=== Comparison Summary ===")
    stats = report["aggregate_statistics"]
    print(f"Documents processed: {report['total_documents']}")
    print(f"GraphRAG avg time: {stats['processing_time']['graphrag_avg']:.2f}s")
    print(f"LangExtract avg time: {stats['processing_time']['langextract_avg']:.2f}s")
    print(f"GraphRAG avg entities: {stats['entity_extraction']['graphrag_avg']:.1f}")
    print(f"LangExtract avg entities: {stats['entity_extraction']['langextract_avg']:.1f}")
    print(f"GraphRAG avg cost: ${stats['cost_analysis']['graphrag_per_document']:.4f}")
    print(f"LangExtract avg cost: ${stats['cost_analysis']['langextract_per_document']:.4f}")
    
    print(f"\n=== Recommendations ===")
    for rec in report["recommendations"]:
        print(f"• {rec}")

if __name__ == "__main__":
    asyncio.run(main())