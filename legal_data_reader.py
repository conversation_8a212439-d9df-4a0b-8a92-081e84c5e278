#!/usr/bin/env python3
"""
Legal Data Reader for GCS/Supabase Integration
Retrieve ingested legal documents for GraphRAG processing
"""

import os
import asyncio
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from google.cloud import storage
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

@dataclass
class LegalDocument:
    """Structured legal document from Supabase/GCS"""
    id: str
    case_name: str
    practice_area: str
    court_name: Optional[str]
    file_date: Optional[str]
    gcs_path: str
    plain_text: str
    processing_status: str
    metadata: Dict[str, Any]

class LegalDataReader:
    """Read ingested legal documents from GCS and Supabase"""
    
    def __init__(self):
        self.gcs_client = storage.Client()
        self.supabase = self._init_supabase()
        self.bucket_name = os.getenv("GCS_BUCKET_NAME", "newtexaslaw-legal-documents")
        
    def _init_supabase(self) -> Client:
        """Initialize Supabase client"""
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_KEY")
        
        if not supabase_url or not supabase_key:
            raise ValueError("Missing SUPABASE_URL or SUPABASE_KEY environment variables")
        
        return create_client(supabase_url, supabase_key)
    
    async def get_documents_for_processing(self, 
                                         batch_size: int = 50,
                                         practice_area: Optional[str] = None,
                                         priority_filter: str = "unprocessed",
                                         court_filter: Optional[str] = None) -> List[LegalDocument]:
        """
        Retrieve documents from Supabase with GCS text content
        Priority: unprocessed -> low_accuracy -> complex_cases
        """
        print(f"🔍 Fetching {batch_size} documents from Supabase (filter: {priority_filter})")
        
        try:
            # Build query based on priority filter
            query = self.supabase.table("legal_documents").select("*")
            
            # Apply filters
            if priority_filter == "unprocessed":
                query = query.eq("processing_status", "unprocessed")
            elif priority_filter == "low_accuracy":
                query = query.or_("processing_status.eq.low_accuracy,processing_metadata->>entity_confidence_avg.lt.0.7")
            elif priority_filter == "complex_cases":
                query = query.in_("practice_area", ["mass_tort", "complex_litigation", "class_action"])
            
            if practice_area:
                query = query.eq("practice_area", practice_area)
            
            if court_filter:
                query = query.ilike("court_name", f"%{court_filter}%")
            
            # Execute query with limit
            response = query.limit(batch_size).execute()
            
            if not response.data:
                print("⚠️  No documents found matching criteria")
                return []
            
            print(f"✅ Found {len(response.data)} documents in Supabase")
            
            # Convert to LegalDocument objects and fetch content
            documents = []
            for row in response.data:
                try:
                    # Get document content from GCS
                    content = await self.get_document_content(row.get("gcs_path", ""))
                    
                    if content:
                        doc = LegalDocument(
                            id=row["id"],
                            case_name=row.get("case_name", "Unknown"),
                            practice_area=row.get("practice_area", "unknown"),
                            court_name=row.get("court_name"),
                            file_date=row.get("file_date"),
                            gcs_path=row.get("gcs_path", ""),
                            plain_text=content,
                            processing_status=row.get("processing_status", "unprocessed"),
                            metadata=row.get("processing_metadata", {})
                        )
                        documents.append(doc)
                        print(f"   📄 Loaded: {doc.case_name} ({len(content)} chars)")
                    else:
                        print(f"   ⚠️  No content for: {row.get('case_name', 'Unknown')}")
                        
                except Exception as e:
                    print(f"   ❌ Failed to load document {row.get('id', 'unknown')}: {e}")
                    continue
            
            print(f"✅ Successfully loaded {len(documents)} documents with content")
            return documents
            
        except Exception as e:
            print(f"❌ Failed to fetch documents from Supabase: {e}")
            return []
    
    async def get_document_content(self, gcs_path: str) -> Optional[str]:
        """Download document content from GCS"""
        if not gcs_path:
            return None
            
        try:
            bucket = self.gcs_client.bucket(self.bucket_name)
            blob = bucket.blob(gcs_path)
            
            if not blob.exists():
                print(f"   ⚠️  GCS blob not found: {gcs_path}")
                return None
            
            # Download content
            content = blob.download_as_text(encoding='utf-8')
            return content
            
        except Exception as e:
            print(f"   ❌ Failed to download from GCS {gcs_path}: {e}")
            return None
    
    def mark_processing_status(self, 
                             doc_id: str, 
                             status: str,
                             graphrag_results: Optional[Dict] = None,
                             enhancement_results: Optional[Dict] = None,
                             processing_metadata: Optional[Dict] = None):
        """Update processing status in Supabase"""
        try:
            update_data = {
                "processing_status": status,
                "last_processed": datetime.utcnow().isoformat()
            }
            
            # Build processing metadata
            metadata = processing_metadata or {}
            
            if graphrag_results:
                metadata.update({
                    "graphrag_entities": len(graphrag_results.get("entities", [])),
                    "graphrag_relationships": len(graphrag_results.get("relationships", [])),
                    "entity_confidence_avg": self._calculate_avg_confidence(graphrag_results.get("entities", []))
                })
            
            if enhancement_results:
                metadata.update({
                    "enhancement_type": enhancement_results.get("processing_type", "standard"),
                    "enhancement_improvement": enhancement_results.get("improvement_metrics", {})
                })
            
            if metadata:
                update_data["processing_metadata"] = metadata
            
            # Update in Supabase
            response = self.supabase.table("legal_documents").update(update_data).eq("id", doc_id).execute()
            
            if response.data:
                print(f"✅ Updated document {doc_id} status to {status}")
            else:
                print(f"⚠️  No rows updated for document {doc_id}")
                
        except Exception as e:
            print(f"❌ Failed to update document {doc_id} status: {e}")
    
    def _calculate_avg_confidence(self, entities: List[Dict]) -> float:
        """Calculate average confidence score from entities"""
        if not entities:
            return 0.0
        
        confidence_scores = [
            entity.get("confidence", 0.0) for entity in entities 
            if entity.get("confidence") is not None
        ]
        
        if not confidence_scores:
            return 0.0
        
        return sum(confidence_scores) / len(confidence_scores)
    
    async def get_documents_by_ids(self, document_ids: List[str]) -> List[LegalDocument]:
        """Get specific documents by their IDs"""
        print(f"🔍 Fetching {len(document_ids)} specific documents")
        
        try:
            response = self.supabase.table("legal_documents").select("*").in_("id", document_ids).execute()
            
            if not response.data:
                print("⚠️  No documents found for provided IDs")
                return []
            
            documents = []
            for row in response.data:
                content = await self.get_document_content(row.get("gcs_path", ""))
                if content:
                    doc = LegalDocument(
                        id=row["id"],
                        case_name=row.get("case_name", "Unknown"),
                        practice_area=row.get("practice_area", "unknown"),
                        court_name=row.get("court_name"),
                        file_date=row.get("file_date"),
                        gcs_path=row.get("gcs_path", ""),
                        plain_text=content,
                        processing_status=row.get("processing_status", "unprocessed"),
                        metadata=row.get("processing_metadata", {})
                    )
                    documents.append(doc)
            
            print(f"✅ Successfully loaded {len(documents)} documents by ID")
            return documents
            
        except Exception as e:
            print(f"❌ Failed to fetch documents by IDs: {e}")
            return []
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing statistics from Supabase"""
        try:
            # Count by processing status
            response = self.supabase.table("legal_documents").select("processing_status", count="exact").execute()
            
            status_counts = {}
            if response.data:
                for row in response.data:
                    status = row.get("processing_status", "unknown")
                    status_counts[status] = status_counts.get(status, 0) + 1
            
            # Count by practice area
            practice_response = self.supabase.table("legal_documents").select("practice_area", count="exact").execute()
            
            practice_counts = {}
            if practice_response.data:
                for row in practice_response.data:
                    practice = row.get("practice_area", "unknown")
                    practice_counts[practice] = practice_counts.get(practice, 0) + 1
            
            return {
                "total_documents": response.count if hasattr(response, 'count') else len(response.data),
                "status_breakdown": status_counts,
                "practice_area_breakdown": practice_counts,
                "last_updated": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Failed to get processing statistics: {e}")
            return {}

async def test_data_reader():
    """Test the LegalDataReader functionality"""
    print("=== Testing Legal Data Reader ===\n")
    
    try:
        reader = LegalDataReader()
        
        # Test 1: Get processing statistics
        print("1. Getting processing statistics...")
        stats = reader.get_processing_statistics()
        if stats:
            print(f"   📊 Total documents: {stats.get('total_documents', 0)}")
            print(f"   📈 Status breakdown: {stats.get('status_breakdown', {})}")
            print(f"   🏛️  Practice areas: {stats.get('practice_area_breakdown', {})}")
        
        # Test 2: Fetch a small batch of unprocessed documents
        print("\n2. Fetching unprocessed documents...")
        documents = await reader.get_documents_for_processing(
            batch_size=5,
            priority_filter="unprocessed",
            practice_area="personal_injury"
        )
        
        if documents:
            print(f"   ✅ Retrieved {len(documents)} documents")
            for doc in documents:
                print(f"      📄 {doc.case_name} ({doc.practice_area}) - {len(doc.plain_text)} chars")
        else:
            print("   ⚠️  No unprocessed documents found")
        
        # Test 3: Try different filters
        print("\n3. Testing different filters...")
        
        # Try complex cases
        complex_docs = await reader.get_documents_for_processing(
            batch_size=3,
            priority_filter="complex_cases"
        )
        print(f"   Complex cases found: {len(complex_docs)}")
        
        return documents
        
    except Exception as e:
        print(f"❌ Data reader test failed: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    asyncio.run(test_data_reader())