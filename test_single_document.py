#!/usr/bin/env python3
"""
Test reading a single document to debug parsing issues
"""

import asyncio
from enhanced_gcs_client import EnhancedGCSClient

async def test_single_document():
    try:
        client = EnhancedGCSClient()
        
        # Test with specific file
        gcs_path = "TX/clusters/10646628.json.gz"
        print(f"🔍 Testing single document: {gcs_path}")
        
        document = await client.read_document(gcs_path)
        
        if document:
            print(f"✅ Success!")
            print(f"   ID: {document.id}")
            print(f"   Case Name: {document.case_name}")
            print(f"   Word Count: {document.word_count}")
            print(f"   Format: {document.file_format}")
            print(f"   Metadata keys: {list(document.metadata.keys())}")
            print(f"   Content preview: {document.content[:200]}...")
        else:
            print(f"❌ Failed to read document")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_single_document())