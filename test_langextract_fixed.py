#!/usr/bin/env python3
"""
Test LangExtract with Proper Extraction Objects
"""

import os
from dotenv import load_dotenv
load_dotenv()

def test_langextract_correct_format():
    """Test LangExtract with proper Extraction objects"""
    try:
        import langextract
        from langextract import data
        
        print("=== Testing LangExtract with Correct Format ===")
        
        # Create example text
        example_text = """
        Judge <PERSON> presided over the case <PERSON> v<PERSON> in Harris County District Court. 
        Attorney <PERSON> represented plaintiff <PERSON>. The court awarded $25,000 in damages.
        """
        
        # Create proper Extraction objects
        extractions = [
            data.Extraction(
                extraction_class="JUDGE",
                extraction_text="<PERSON>",
                description="Presiding judge"
            ),
            data.Extraction(
                extraction_class="CASE",
                extraction_text="<PERSON> v<PERSON>",
                description="Case name"
            ),
            data.Extraction(
                extraction_class="COURT", 
                extraction_text="Harris County District Court",
                description="Court venue"
            ),
            data.Extraction(
                extraction_class="ATTORNEY",
                extraction_text="<PERSON>",
                description="Representing attorney"
            ),
            data.Extraction(
                extraction_class="PLAINTIFF",
                extraction_text="<PERSON>",
                description="Plaintiff party"
            ),
            data.Extraction(
                extraction_class="MONEY",
                extraction_text="$25,000",
                description="Monetary damages"
            )
        ]
        
        # Create ExampleData with proper Extraction objects
        example_data = data.ExampleData(
            text=example_text,
            extractions=extractions
        )
        
        print(f"✅ Created example data with {len(extractions)} extractions")
        for extraction in extractions:
            print(f"   {extraction.extraction_class}: {extraction.extraction_text}")
        
        # Test text to extract from
        test_text = """
        This is a personal injury case where Plaintiff John Smith sued Defendant Mary Jones 
        for damages resulting from a motor vehicle accident. Judge William Brown presided over 
        the case. The jury awarded $50,000 in actual damages and $25,000 in punitive damages. 
        The case was decided on March 15, 2023, in the 55th Judicial District Court of Harris County, Texas.
        Attorney Sarah Wilson represented the plaintiff, while Attorney Robert Davis represented the defendant.
        The court cited precedent from Doe v. Roe, 123 S.W.3d 456 (Tex. 2020).
        """
        
        # Perform extraction
        print(f"\n=== Performing LangExtract Extraction ===")
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("❌ No GEMINI_API_KEY found in environment")
            return None
        
        result = langextract.extract(
            text_or_documents=test_text,
            examples=[example_data],
            prompt_description="Extract legal entities from court documents including judges, attorneys, plaintiffs, defendants, courts, cases, and monetary damages.",
            api_key=api_key,
            temperature=0.0,
            model_id='gemini-2.5-flash',
            debug=True
        )
        
        print(f"✅ LangExtract extraction successful!")
        print(f"   Result type: {type(result)}")
        
        # Extract and display results
        if hasattr(result, 'extractions') and result.extractions:
            print(f"\n=== LangExtract Results ===")
            print(f"   Total extractions: {len(result.extractions)}")
            
            # Group by extraction class
            by_class = {}
            for extraction in result.extractions:
                class_name = extraction.extraction_class
                if class_name not in by_class:
                    by_class[class_name] = []
                by_class[class_name].append(extraction.extraction_text)
            
            for class_name, texts in by_class.items():
                print(f"   {class_name}: {texts}")
        
        return result
        
    except Exception as e:
        print(f"❌ LangExtract correct format test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_extraction_results():
    """Compare LangExtract vs GraphRAG results side by side"""
    print("\n=== Detailed Comparison: LangExtract vs GraphRAG ===")
    
    # Run LangExtract
    langextract_result = test_langextract_correct_format()
    
    # Our known GraphRAG results
    graphrag_entities = {
        "Case": ["personal_injury_case"],
        "Plaintiff": ["john_smith"], 
        "Defendant": ["mary_jones"],
        "Judge": ["william_brown"],
        "Court": ["harris_county_district_court"],
        "Attorney": ["sarah_wilson"],
        "Damages": ["damages_50000"]
    }
    
    print(f"\n=== Side-by-Side Comparison ===")
    print(f"{'Entity Type':<15} {'GraphRAG':<25} {'LangExtract':<25}")
    print("-" * 65)
    
    # Extract LangExtract results by class
    langextract_entities = {}
    if langextract_result and hasattr(langextract_result, 'extractions') and langextract_result.extractions:
        for extraction in langextract_result.extractions:
            class_name = extraction.extraction_class
            if class_name not in langextract_entities:
                langextract_entities[class_name] = []
            langextract_entities[class_name].append(extraction.extraction_text)
    
    # Compare all entity types
    all_types = set(graphrag_entities.keys()) | set(langextract_entities.keys())
    
    for entity_type in sorted(all_types):
        graphrag_items = graphrag_entities.get(entity_type, [])
        langextract_items = langextract_entities.get(entity_type, [])
        
        graphrag_str = str(graphrag_items)[:23] + "..." if len(str(graphrag_items)) > 25 else str(graphrag_items)
        langextract_str = str(langextract_items)[:23] + "..." if len(str(langextract_items)) > 25 else str(langextract_items)
        
        print(f"{entity_type:<15} {graphrag_str:<25} {langextract_str:<25}")
    
    # Summary statistics
    print(f"\n=== Extraction Statistics ===")
    print(f"GraphRAG total entities: {sum(len(items) for items in graphrag_entities.values())}")
    print(f"LangExtract total entities: {sum(len(items) for items in langextract_entities.values())}")
    
    return {
        "graphrag": graphrag_entities,
        "langextract": langextract_entities
    }

def test_langextract_performance():
    """Test LangExtract performance metrics"""
    print("\n=== LangExtract Performance Test ===")
    
    try:
        import time
        start_time = time.time()
        
        # Run extraction
        result = test_langextract_correct_format()
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n=== Performance Metrics ===")
        print(f"Processing time: {processing_time:.2f} seconds")
        
        if result and hasattr(result, 'extractions'):
            entity_count = len(result.extractions) if result.extractions else 0
            print(f"Entities extracted: {entity_count}")
            print(f"Entities per second: {entity_count / processing_time:.2f}")
        
        return {
            "processing_time": processing_time,
            "entity_count": len(result.extractions) if result and result.extractions else 0
        }
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return None

def main():
    """Run all LangExtract tests"""
    print("=== LangExtract Fixed Format Test ===\n")
    
    # Test correct format
    test_langextract_correct_format()
    
    # Compare with GraphRAG
    compare_extraction_results()
    
    # Test performance
    test_langextract_performance()
    
    print("\n=== LangExtract Fixed Format Test Complete ===")

if __name__ == "__main__":
    main()