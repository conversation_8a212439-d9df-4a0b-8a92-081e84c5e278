#!/usr/bin/env python3
"""
Supabase Data Reader for Existing Schema
Work with existing cases and chunks tables
"""

import os
import asyncio
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from google.cloud import storage
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

@dataclass
class LegalCase:
    """Legal case from existing Supabase schema"""
    id: str
    case_name: str
    case_name_full: str
    practice_area: Optional[str]
    court_id: str
    jurisdiction: str
    date_filed: Optional[str]
    gcs_path: str
    word_count: int
    case_type: str
    judge_name: Optional[str]
    content: str
    metadata: Dict[str, Any]

class SupabaseDataReader:
    """Read legal cases from existing Supabase schema"""
    
    def __init__(self):
        self.gcs_client = storage.Client()
        self.supabase = self._init_supabase()
        self.bucket_name = "texas-laws-personalinjury"  # Based on GCS URLs seen
        
    def _init_supabase(self) -> Client:
        """Initialize Supabase client"""
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_KEY")
        
        if not supabase_url or not supabase_key:
            raise ValueError("Missing SUPABASE_URL or SUPABASE_KEY environment variables")
        
        return create_client(supabase_url, supabase_key)
    
    async def get_cases_for_processing(self, 
                                     batch_size: int = 50,
                                     practice_area: Optional[str] = None,
                                     court_filter: Optional[str] = None,
                                     min_word_count: int = 100,
                                     jurisdiction: str = "TX") -> List[LegalCase]:
        """
        Retrieve cases from Supabase with content from GCS
        """
        print(f"🔍 Fetching {batch_size} cases from Supabase")
        
        try:
            # Build query
            query = self.supabase.table("cases").select("*")
            
            # Apply filters
            query = query.eq("jurisdiction", jurisdiction)
            
            if practice_area:
                # Practice area might be in practice_areas array or primary_practice_area
                query = query.or_(f"primary_practice_area.eq.{practice_area},practice_areas.cs.{{\"{practice_area}\"}}")
            
            if court_filter:
                query = query.ilike("court_id", f"%{court_filter}%")
            
            if min_word_count:
                query = query.gte("word_count", min_word_count)
            
            # Order by word count (richer content first) and limit
            query = query.order("word_count", desc=True).limit(batch_size)
            
            # Execute query
            response = query.execute()
            
            if not response.data:
                print("⚠️  No cases found matching criteria")
                return []
            
            print(f"✅ Found {len(response.data)} cases in Supabase")
            
            # Convert to LegalCase objects and fetch content
            cases = []
            for row in response.data:
                try:
                    # Get case content from GCS
                    content = await self.get_case_content(row.get("gcs_path", ""))
                    
                    if content:
                        # Determine practice area
                        practice_area_value = (
                            row.get("primary_practice_area") or
                            (row.get("practice_areas", [{}])[0] if row.get("practice_areas") else None) or
                            "unknown"
                        )
                        
                        case = LegalCase(
                            id=str(row["id"]),
                            case_name=row.get("case_name", "Unknown"),
                            case_name_full=row.get("case_name_full", "Unknown"),
                            practice_area=practice_area_value,
                            court_id=row.get("court_id", "unknown"),
                            jurisdiction=row.get("jurisdiction", "TX"),
                            date_filed=row.get("date_filed"),
                            gcs_path=row.get("gcs_path", ""),
                            word_count=row.get("word_count", 0),
                            case_type=row.get("case_type", "civil"),
                            judge_name=row.get("judge_name"),
                            content=content,
                            metadata={
                                "court_metadata": row.get("court_metadata", {}),
                                "judge_metadata": row.get("judge_metadata", {}),
                                "source": row.get("source", "unknown"),
                                "source_id": row.get("source_id"),
                                "citation": row.get("citation", []),
                                "precedential": row.get("precedential", False),
                                "authority_score": row.get("authority_score", 0),
                                "practice_area_confidence": row.get("practice_area_confidence", 0)
                            }
                        )
                        cases.append(case)
                        print(f"   📄 Loaded: {case.case_name[:50]}... ({len(content)} chars)")
                    else:
                        print(f"   ⚠️  No content for: {row.get('case_name', 'Unknown')}")
                        
                except Exception as e:
                    print(f"   ❌ Failed to load case {row.get('id', 'unknown')}: {e}")
                    continue
            
            print(f"✅ Successfully loaded {len(cases)} cases with content")
            return cases
            
        except Exception as e:
            print(f"❌ Failed to fetch cases from Supabase: {e}")
            return []
    
    async def get_case_content(self, gcs_path: str) -> Optional[str]:
        """Download case content from GCS"""
        if not gcs_path:
            return None
            
        try:
            bucket = self.gcs_client.bucket(self.bucket_name)
            blob = bucket.blob(gcs_path)
            
            if not blob.exists():
                print(f"   ⚠️  GCS blob not found: {gcs_path}")
                return None
            
            # Download content
            content_bytes = blob.download_as_bytes()
            
            # Handle compressed files
            if gcs_path.endswith('.gz'):
                import gzip
                content_bytes = gzip.decompress(content_bytes)
            
            # Try to decode as text
            try:
                content = content_bytes.decode('utf-8')
            except UnicodeDecodeError:
                # Try latin-1 as fallback
                content = content_bytes.decode('latin-1', errors='ignore')
            
            # If it's JSON, extract the main text content
            if gcs_path.endswith('.json') or gcs_path.endswith('.json.gz'):
                try:
                    json_data = json.loads(content)
                    # Look for common text fields
                    text_content = (
                        json_data.get('plain_text') or
                        json_data.get('html') or
                        json_data.get('text') or
                        json_data.get('content') or
                        str(json_data)
                    )
                    return text_content
                except json.JSONDecodeError:
                    # If JSON parsing fails, return raw content
                    return content
            
            return content
            
        except Exception as e:
            print(f"   ❌ Failed to download from GCS {gcs_path}: {e}")
            return None
    
    def mark_case_processed(self, 
                          case_id: str,
                          graphrag_results: Optional[Dict] = None,
                          processing_metadata: Optional[Dict] = None):
        """Mark case as processed by updating metadata"""
        try:
            # Build update data
            update_data = {}
            
            if graphrag_results:
                # Store GraphRAG results in metadata fields
                if 'entities' in graphrag_results:
                    update_data['neo4j_node_id'] = f"processed_{case_id}"
                
                # Could add more specific metadata fields as needed
            
            if processing_metadata:
                # Update any relevant metadata fields
                update_data.update(processing_metadata)
            
            # Add processing timestamp
            update_data['updated_at'] = datetime.utcnow().isoformat()
            
            if update_data:
                response = self.supabase.table("cases").update(update_data).eq("id", case_id).execute()
                
                if response.data:
                    print(f"✅ Updated case {case_id} processing metadata")
                else:
                    print(f"⚠️  No rows updated for case {case_id}")
                    
        except Exception as e:
            print(f"❌ Failed to update case {case_id} metadata: {e}")
    
    async def get_cases_by_ids(self, case_ids: List[str]) -> List[LegalCase]:
        """Get specific cases by their IDs"""
        print(f"🔍 Fetching {len(case_ids)} specific cases")
        
        try:
            response = self.supabase.table("cases").select("*").in_("id", case_ids).execute()
            
            if not response.data:
                print("⚠️  No cases found for provided IDs")
                return []
            
            cases = []
            for row in response.data:
                content = await self.get_case_content(row.get("gcs_path", ""))
                if content:
                    practice_area_value = (
                        row.get("primary_practice_area") or
                        (row.get("practice_areas", [{}])[0] if row.get("practice_areas") else None) or
                        "unknown"
                    )
                    
                    case = LegalCase(
                        id=str(row["id"]),
                        case_name=row.get("case_name", "Unknown"),
                        case_name_full=row.get("case_name_full", "Unknown"),
                        practice_area=practice_area_value,
                        court_id=row.get("court_id", "unknown"),
                        jurisdiction=row.get("jurisdiction", "TX"),
                        date_filed=row.get("date_filed"),
                        gcs_path=row.get("gcs_path", ""),
                        word_count=row.get("word_count", 0),
                        case_type=row.get("case_type", "civil"),
                        judge_name=row.get("judge_name"),
                        content=content,
                        metadata={
                            "court_metadata": row.get("court_metadata", {}),
                            "judge_metadata": row.get("judge_metadata", {}),
                            "source": row.get("source", "unknown"),
                            "source_id": row.get("source_id"),
                            "citation": row.get("citation", []),
                            "precedential": row.get("precedential", False)
                        }
                    )
                    cases.append(case)
            
            print(f"✅ Successfully loaded {len(cases)} cases by ID")
            return cases
            
        except Exception as e:
            print(f"❌ Failed to fetch cases by IDs: {e}")
            return []
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing statistics from Supabase"""
        try:
            # Get total case count
            total_response = self.supabase.table("cases").select("*", count="exact").limit(1).execute()
            total_cases = getattr(total_response, 'count', 0)
            
            # Get cases by jurisdiction
            tx_response = self.supabase.table("cases").select("*", count="exact").eq("jurisdiction", "TX").limit(1).execute()
            tx_cases = getattr(tx_response, 'count', 0)
            
            # Get cases by practice area (try to get some sample data)
            practice_areas = {}
            try:
                pa_response = self.supabase.table("cases").select("primary_practice_area").limit(100).execute()
                if pa_response.data:
                    for row in pa_response.data:
                        pa = row.get("primary_practice_area")
                        if pa:
                            practice_areas[pa] = practice_areas.get(pa, 0) + 1
            except:
                pass
            
            # Get chunk statistics
            chunks_response = self.supabase.table("chunks").select("*", count="exact").limit(1).execute()
            total_chunks = getattr(chunks_response, 'count', 0)
            
            return {
                "total_cases": total_cases,
                "texas_cases": tx_cases,
                "total_chunks": total_chunks,
                "practice_areas_sample": practice_areas,
                "last_updated": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            print(f"❌ Failed to get processing statistics: {e}")
            return {}
    
    async def get_personal_injury_cases(self, batch_size: int = 10) -> List[LegalCase]:
        """Get personal injury cases specifically"""
        print(f"🔍 Fetching {batch_size} personal injury cases...")
        
        try:
            # Try different ways to find personal injury cases
            queries_to_try = [
                # Method 1: Look for 'personal' or 'injury' in case names
                lambda: self.supabase.table("cases").select("*").or_(
                    "case_name.ilike.%personal%,case_name.ilike.%injury%,case_name_full.ilike.%personal%,case_name_full.ilike.%injury%"
                ).eq("jurisdiction", "TX").gte("word_count", 500).order("word_count", desc=True).limit(batch_size).execute(),
                
                # Method 2: Look for tort-related terms
                lambda: self.supabase.table("cases").select("*").or_(
                    "case_name.ilike.%tort%,case_name.ilike.%negligence%,case_name.ilike.%accident%,case_name.ilike.%damages%"
                ).eq("jurisdiction", "TX").gte("word_count", 500).order("word_count", desc=True).limit(batch_size).execute(),
                
                # Method 3: Just get high-quality Texas civil cases
                lambda: self.supabase.table("cases").select("*").eq("jurisdiction", "TX").eq("case_type", "civil").gte("word_count", 1000).order("word_count", desc=True).limit(batch_size).execute()
            ]
            
            cases = []
            for query_func in queries_to_try:
                try:
                    response = query_func()
                    if response.data:
                        print(f"   ✅ Found {len(response.data)} cases with current query")
                        
                        # Convert to LegalCase objects
                        for row in response.data:
                            content = await self.get_case_content(row.get("gcs_path", ""))
                            if content and len(content) > 500:  # Ensure substantial content
                                practice_area_value = (
                                    row.get("primary_practice_area") or
                                    "personal_injury"  # Default for our purposes
                                )
                                
                                case = LegalCase(
                                    id=str(row["id"]),
                                    case_name=row.get("case_name", "Unknown"),
                                    case_name_full=row.get("case_name_full", "Unknown"),
                                    practice_area=practice_area_value,
                                    court_id=row.get("court_id", "unknown"),
                                    jurisdiction=row.get("jurisdiction", "TX"),
                                    date_filed=row.get("date_filed"),
                                    gcs_path=row.get("gcs_path", ""),
                                    word_count=row.get("word_count", 0),
                                    case_type=row.get("case_type", "civil"),
                                    judge_name=row.get("judge_name"),
                                    content=content,
                                    metadata={
                                        "source": row.get("source", "unknown"),
                                        "citation": row.get("citation", []),
                                        "authority_score": row.get("authority_score", 0)
                                    }
                                )
                                cases.append(case)
                                
                                if len(cases) >= batch_size:
                                    break
                        
                        if cases:
                            break  # Found cases, stop trying other queries
                        
                except Exception as e:
                    print(f"   ⚠️  Query failed: {e}")
                    continue
            
            print(f"✅ Successfully loaded {len(cases)} personal injury cases")
            return cases[:batch_size]  # Ensure we don't exceed batch size
            
        except Exception as e:
            print(f"❌ Failed to fetch personal injury cases: {e}")
            return []

async def test_supabase_reader():
    """Test the Supabase data reader"""
    print("=== Testing Supabase Data Reader ===\n")
    
    try:
        reader = SupabaseDataReader()
        
        # Test 1: Get processing statistics
        print("1. Getting processing statistics...")
        stats = reader.get_processing_statistics()
        if stats:
            print(f"   📊 Total cases: {stats.get('total_cases', 0)}")
            print(f"   🏛️  Texas cases: {stats.get('texas_cases', 0)}")
            print(f"   📄 Total chunks: {stats.get('total_chunks', 0)}")
            print(f"   🏷️  Practice areas sample: {stats.get('practice_areas_sample', {})}")
        
        # Test 2: Fetch personal injury cases
        print("\n2. Fetching personal injury cases...")
        cases = await reader.get_personal_injury_cases(batch_size=5)
        
        if cases:
            print(f"   ✅ Retrieved {len(cases)} cases")
            for case in cases:
                print(f"      📄 {case.case_name[:60]}... ({case.word_count} words)")
                print(f"         Court: {case.court_id}, Type: {case.case_type}")
                print(f"         Content: {len(case.content)} chars")
        else:
            print("   ⚠️  No cases found")
        
        # Test 3: Try general case fetch
        print("\n3. Fetching general Texas cases...")
        general_cases = await reader.get_cases_for_processing(
            batch_size=3,
            jurisdiction="TX",
            min_word_count=1000
        )
        
        if general_cases:
            print(f"   ✅ Retrieved {len(general_cases)} general cases")
            for case in general_cases:
                print(f"      📄 {case.case_name[:50]}... ({case.word_count} words)")
        
        return cases
        
    except Exception as e:
        print(f"❌ Supabase reader test failed: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    asyncio.run(test_supabase_reader())