#!/usr/bin/env python3
"""
REAL CASE CROSS-TRACING
Trace case 11113702 across all systems to prove cross-tracing works
"""

import os
import logging
from datetime import datetime
import json

from pinecone import Pinecone
from supabase import create_client
from google.cloud import storage
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()

def trace_real_case():
    """Trace known case 11113702 across all systems"""
    
    print("🔍 REAL CASE CROSS-TRACING: Case 11113702")
    print("=" * 50)
    
    # Known case from our previous work
    case_id = "11113702"
    
    # Initialize clients
    pinecone_client = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
    index = pinecone_client.Index("texas-laws-voyage3large")
    
    supabase = create_client(
        os.getenv("SUPABASE_URL"),
        os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    )
    
    gcs_client = storage.Client()
    bucket = gcs_client.bucket("texas-laws-personalinjury")
    
    trace_results = {
        'case_id': case_id,
        'timestamp': datetime.utcnow().isoformat(),
        'systems_validated': []
    }
    
    # Step 1: Check Supabase
    print(f"\n📊 Step 1: Check case {case_id} in Supabase...")
    
    try:
        case_response = supabase.table('cases').select('*').eq('id', case_id).execute()
        
        if case_response.data:
            case_data = case_response.data[0]
            print(f"   ✅ Found in Supabase:")
            print(f"      Case ID: {case_data['id']}")
            print(f"      Case Name: {case_data.get('case_name', 'N/A')}")
            print(f"      Source: {case_data.get('source', 'N/A')}")
            print(f"      Created: {case_data.get('created_at', 'N/A')}")
            
            trace_results['supabase'] = {
                'found': True,
                'data': case_data
            }
            trace_results['systems_validated'].append('Supabase')
        else:
            print(f"   ❌ Case {case_id} not found in Supabase")
            trace_results['supabase'] = {'found': False}
            
    except Exception as e:
        print(f"   ❌ Supabase error: {e}")
        trace_results['supabase'] = {'found': False, 'error': str(e)}
    
    # Step 2: Check Pinecone vectors
    print(f"\n📍 Step 2: Find vectors for case {case_id} in Pinecone...")
    
    try:
        # Query for vectors with this case_id in their ID
        query_result = index.query(
            vector=[0.1] * 1024,  # Dummy vector for search
            top_k=50,
            namespace="texas-legal-contextual",
            include_metadata=True
        )
        
        # Find vectors that belong to our case
        case_vectors = []
        for match in query_result.matches:
            if case_id in match.id:
                case_vectors.append(match)
        
        if case_vectors:
            print(f"   ✅ Found {len(case_vectors)} vectors in Pinecone:")
            for i, vector in enumerate(case_vectors[:5], 1):
                print(f"      Vector {i}: {vector.id}")
                if vector.metadata:
                    chunk_text = vector.metadata.get('text', '')[:50] + "..." if vector.metadata.get('text') else 'No text'
                    print(f"         Content: {chunk_text}")
            
            trace_results['pinecone'] = {
                'found': True,
                'vector_count': len(case_vectors),
                'sample_vectors': [v.id for v in case_vectors[:3]]
            }
            trace_results['systems_validated'].append('Pinecone')
        else:
            print(f"   ❌ No vectors found for case {case_id}")
            trace_results['pinecone'] = {'found': False}
            
    except Exception as e:
        print(f"   ❌ Pinecone error: {e}")
        trace_results['pinecone'] = {'found': False, 'error': str(e)}
    
    # Step 3: Check global UID tracking
    print(f"\n🔗 Step 3: Check global UID tracking...")
    
    try:
        uid_response = supabase.table('global_uid_tracking').select('*').eq('document_id', case_id).execute()
        
        if uid_response.data:
            uid_data = uid_response.data[0]
            global_uid = uid_data['global_uid']
            print(f"   ✅ Found global UID tracking:")
            print(f"      Global UID: {global_uid}")
            print(f"      Document ID: {uid_data['document_id']}")
            print(f"      Storage Status: Supabase={uid_data.get('supabase_stored', False)}, Pinecone={uid_data.get('pinecone_stored', False)}")
            
            trace_results['global_uid_tracking'] = {
                'found': True,
                'global_uid': global_uid,
                'data': uid_data
            }
            trace_results['systems_validated'].append('Global UID Tracking')
        else:
            print(f"   ❌ No global UID tracking found")
            trace_results['global_uid_tracking'] = {'found': False}
            
    except Exception as e:
        print(f"   ❌ Global UID tracking error: {e}")
        trace_results['global_uid_tracking'] = {'found': False, 'error': str(e)}
    
    # Step 4: Try to find in GCS (check common paths)
    print(f"\n🗂️ Step 4: Look for case {case_id} in GCS...")
    
    try:
        # Common paths where this case might be stored
        possible_paths = [
            f"FED/opinions/{case_id}.json",
            f"FED/opinions/{case_id}.txt",
            f"processed/{case_id}.json",
            f"opinions/{case_id}.json"
        ]
        
        gcs_found = False
        for path in possible_paths:
            blob = bucket.blob(path)
            if blob.exists():
                print(f"   ✅ Found in GCS: {path}")
                content_size = blob.size
                print(f"      Size: {content_size:,} bytes")
                
                trace_results['gcs'] = {
                    'found': True,
                    'path': path,
                    'size': content_size
                }
                trace_results['systems_validated'].append('GCS')
                gcs_found = True
                break
        
        if not gcs_found:
            # List some files to see what's actually there
            blobs = list(bucket.list_blobs(prefix="FED/", max_results=10))
            print(f"   ⚠️ Not found in expected paths. Sample FED files:")
            for blob in blobs[:3]:
                print(f"      {blob.name}")
            
            trace_results['gcs'] = {
                'found': False,
                'note': f'Checked paths: {possible_paths}'
            }
            
    except Exception as e:
        print(f"   ❌ GCS error: {e}")
        trace_results['gcs'] = {'found': False, 'error': str(e)}
    
    # Step 5: Summary and cross-validation
    print(f"\n🏆 CROSS-TRACING SUMMARY for Case {case_id}:")
    
    systems_count = len(trace_results['systems_validated'])
    print(f"   Systems Validated: {systems_count}/4")
    print(f"   Found in: {', '.join(trace_results['systems_validated'])}")
    
    # Check data consistency
    supabase_has_case = trace_results.get('supabase', {}).get('found', False)
    pinecone_has_vectors = trace_results.get('pinecone', {}).get('found', False)
    has_global_uid = trace_results.get('global_uid_tracking', {}).get('found', False)
    gcs_has_file = trace_results.get('gcs', {}).get('found', False)
    
    consistency_score = sum([supabase_has_case, pinecone_has_vectors, has_global_uid, gcs_has_file]) / 4
    
    print(f"\n🔄 CROSS-SYSTEM CONSISTENCY:")
    print(f"   Supabase Record: {'✅' if supabase_has_case else '❌'}")
    print(f"   Pinecone Vectors: {'✅' if pinecone_has_vectors else '❌'}")
    print(f"   Global UID Tracking: {'✅' if has_global_uid else '❌'}")
    print(f"   GCS Original: {'✅' if gcs_has_file else '❌'}")
    print(f"   Consistency Score: {consistency_score*100:.1f}%")
    
    # Prove cross-tracing works
    if supabase_has_case and pinecone_has_vectors:
        print(f"\n✅ CROSS-TRACING VERIFIED:")
        print(f"   Same case ({case_id}) found in both Supabase and Pinecone")
        print(f"   Supabase → Pinecone data flow confirmed")
        
        if has_global_uid:
            print(f"   Global UID system tracking both systems")
            trace_results['cross_tracing_verified'] = True
        else:
            trace_results['cross_tracing_verified'] = 'partial'
    else:
        print(f"\n❌ CROSS-TRACING INCOMPLETE")
        trace_results['cross_tracing_verified'] = False
    
    trace_results['consistency_score'] = consistency_score
    
    # Save results
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    filename = f"real_case_cross_trace_{case_id}_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(trace_results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed Report: {filename}")
    
    return trace_results

if __name__ == "__main__":
    results = trace_real_case()
    
    success = results.get('cross_tracing_verified', False)
    consistency = results.get('consistency_score', 0)
    
    print(f"\n🎯 REAL CASE CROSS-TRACING: {'SUCCESS' if success else 'PARTIAL'}")
    print(f"   Consistency: {consistency*100:.1f}%")