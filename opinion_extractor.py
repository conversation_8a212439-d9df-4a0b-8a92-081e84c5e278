#!/usr/bin/env python3
"""
Full Opinion Extractor for Texas Laws Personal Injury Project
Demonstrates how to extract complete legal opinions from bulk CSV data and GCS storage
"""

import bz2
import csv
import json
import gzip
import os
from typing import Dict, List, Optional, Tuple
from google.cloud import storage
from dotenv import load_dotenv
from bs4 import BeautifulSoup
import re

# Load environment variables
load_dotenv()

class TexasOpinionExtractor:
    """Extract and manage full legal opinions from CSV and GCS."""
    
    def __init__(self):
        """Initialize GCS client and bucket."""
        self.gcs_client = storage.Client()
        self.bucket_name = os.getenv('GCS_BUCKET_NAME', 'texas-laws-personalinjury')
        self.bucket = self.gcs_client.bucket(self.bucket_name)
        
        # Set CSV field size limit for large documents
        csv.field_size_limit(10**9)
        
        # Text field priority for extraction
        self.text_field_priority = [
            'plain_text',           # Best for analysis
            'html_with_citations',  # Best for research
            'html',                 # Good for display
            'html_lawbox',          # Alternative source
            'html_columbia',        # Columbia Law source
            'html_anon_2020',       # Anonymized version
            'xml_harvard'           # Harvard XML format
        ]
    
    def get_opinion_from_gcs(self, case_id: str) -> Optional[Dict]:
        """Retrieve opinion from GCS storage."""
        try:
            # Try compressed version first
            gcs_path = f"TX/opinions/{case_id}.json.gz"
            blob = self.bucket.blob(gcs_path)
            
            if blob.exists():
                compressed_data = blob.download_as_bytes()
                content = gzip.decompress(compressed_data).decode('utf-8')
                return json.loads(content)
            
            # Try uncompressed version
            gcs_path = f"TX/opinions/{case_id}.json"
            blob = self.bucket.blob(gcs_path)
            
            if blob.exists():
                content = blob.download_as_text()
                return json.loads(content)
            
            return None
            
        except Exception as e:
            print(f"Error retrieving opinion {case_id} from GCS: {e}")
            return None
    
    def extract_opinion_from_csv(self, csv_file: str, case_id: str) -> Optional[Dict]:
        """Extract specific opinion from CSV file."""
        try:
            with bz2.open(csv_file, 'rt', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    if str(row.get('id')) == str(case_id):
                        return self._process_csv_row(row)
            
            return None
            
        except Exception as e:
            print(f"Error extracting opinion {case_id} from CSV: {e}")
            return None
    
    def _process_csv_row(self, row: Dict) -> Dict:
        """Process CSV row and extract best available text."""
        # Extract metadata
        metadata = {
            'id': row.get('id'),
            'cluster_id': row.get('cluster_id'),
            'author_str': row.get('author_str'),
            'date_created': row.get('date_created'),
            'type': row.get('type'),
            'page_count': row.get('page_count'),
            'download_url': row.get('download_url'),
            'extracted_by_ocr': row.get('extracted_by_ocr')
        }
        
        # Extract best available text
        text_content = {}
        for field in self.text_field_priority:
            content = row.get(field, '')
            if content and len(content.strip()) > 50:
                text_content[field] = content
        
        return {
            'metadata': metadata,
            'text_content': text_content,
            'best_text': self._get_best_text(text_content),
            'source': 'csv_extraction'
        }
    
    def _get_best_text(self, text_content: Dict) -> Tuple[str, str]:
        """Get the best available text content."""
        for field in self.text_field_priority:
            if field in text_content:
                content = text_content[field]
                if field.startswith('html'):
                    # Clean HTML content
                    cleaned = self._clean_html(content)
                    return cleaned, field
                else:
                    return content, field
        
        return "", "none"
    
    def _clean_html(self, html_content: str) -> str:
        """Clean HTML content for text analysis."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text and clean whitespace
            text = soup.get_text(separator=' ', strip=True)
            
            # Clean up multiple spaces and newlines
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
            
        except Exception as e:
            print(f"Error cleaning HTML: {e}")
            return html_content
    
    def get_full_opinion(self, case_id: str, csv_file: str = None) -> Optional[Dict]:
        """Get full opinion text, trying GCS first, then CSV."""
        # Try GCS first (fastest)
        opinion = self.get_opinion_from_gcs(case_id)
        if opinion:
            # Extract text from GCS JSON structure
            text, source_field = self._get_best_text(opinion)
            return {
                'case_id': case_id,
                'text': text,
                'source_field': source_field,
                'source': 'gcs',
                'metadata': {
                    'id': opinion.get('id'),
                    'cluster_id': opinion.get('cluster_id'),
                    'author_str': opinion.get('author_str'),
                    'date_created': opinion.get('date_created'),
                    'type': opinion.get('type')
                },
                'full_data': opinion
            }
        
        # Fallback to CSV extraction
        if csv_file:
            csv_opinion = self.extract_opinion_from_csv(csv_file, case_id)
            if csv_opinion:
                text, source_field = csv_opinion['best_text']
                return {
                    'case_id': case_id,
                    'text': text,
                    'source_field': source_field,
                    'source': 'csv',
                    'metadata': csv_opinion['metadata'],
                    'full_data': csv_opinion
                }
        
        return None
    
    def batch_extract_opinions(self, case_ids: List[str], csv_file: str = None) -> Dict[str, Dict]:
        """Extract multiple opinions efficiently."""
        results = {}
        
        print(f"Extracting {len(case_ids)} opinions...")
        
        # Try GCS first for all cases
        gcs_found = 0
        for case_id in case_ids:
            opinion = self.get_opinion_from_gcs(case_id)
            if opinion:
                text, source_field = self._get_best_text(opinion)
                results[case_id] = {
                    'case_id': case_id,
                    'text': text,
                    'source_field': source_field,
                    'source': 'gcs',
                    'length': len(text),
                    'metadata': opinion
                }
                gcs_found += 1
        
        print(f"Found {gcs_found} opinions in GCS")
        
        # CSV fallback for missing cases
        missing_ids = set(case_ids) - set(results.keys())
        if missing_ids and csv_file:
            print(f"Extracting {len(missing_ids)} missing opinions from CSV...")
            csv_found = 0
            
            try:
                with bz2.open(csv_file, 'rt', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    
                    for row in reader:
                        row_id = str(row.get('id'))
                        if row_id in missing_ids:
                            processed = self._process_csv_row(row)
                            text, source_field = processed['best_text']
                            
                            results[row_id] = {
                                'case_id': row_id,
                                'text': text,
                                'source_field': source_field,
                                'source': 'csv',
                                'length': len(text),
                                'metadata': processed['metadata']
                            }
                            csv_found += 1
                            
                            if csv_found >= len(missing_ids):
                                break
                                
            except Exception as e:
                print(f"Error during CSV extraction: {e}")
            
            print(f"Found {csv_found} additional opinions in CSV")
        
        return results
    
    def analyze_opinion_quality(self, opinion_text: str) -> Dict:
        """Analyze the quality and characteristics of opinion text."""
        if not opinion_text:
            return {'valid': False, 'reason': 'Empty text'}
        
        # Basic metrics
        word_count = len(opinion_text.split())
        char_count = len(opinion_text)
        
        # Legal content indicators
        legal_terms = [
            'court', 'opinion', 'judgment', 'plaintiff', 'defendant',
            'appeal', 'motion', 'order', 'ruling', 'decision',
            'evidence', 'testimony', 'witness', 'trial', 'jury',
            'statute', 'law', 'legal', 'case', 'precedent'
        ]
        
        text_lower = opinion_text.lower()
        legal_term_count = sum(1 for term in legal_terms if term in text_lower)
        
        # Quality assessment
        is_valid = (
            word_count >= 50 and
            char_count >= 300 and
            legal_term_count >= 3
        )
        
        return {
            'valid': is_valid,
            'word_count': word_count,
            'char_count': char_count,
            'legal_term_count': legal_term_count,
            'legal_term_ratio': legal_term_count / len(legal_terms),
            'avg_word_length': char_count / max(word_count, 1)
        }
    
    def save_opinion_to_gcs(self, case_id: str, opinion_data: Dict, compress: bool = True) -> str:
        """Save opinion data to GCS."""
        gcs_path = f"TX/opinions/{case_id}.json"
        if compress:
            gcs_path += ".gz"
        
        # Prepare content
        json_content = json.dumps(opinion_data, indent=2)
        
        if compress:
            content = gzip.compress(json_content.encode('utf-8'))
            content_type = 'application/gzip'
        else:
            content = json_content.encode('utf-8')
            content_type = 'application/json'
        
        # Upload
        blob = self.bucket.blob(gcs_path)
        blob.metadata = {
            'source': 'opinion_extractor',
            'case_id': case_id,
            'compressed': str(compress),
            'content_length': str(len(json_content))
        }
        
        blob.upload_from_string(content, content_type=content_type)
        
        return f"gs://{self.bucket.name}/{gcs_path}"

# Usage examples
if __name__ == "__main__":
    extractor = TexasOpinionExtractor()
    
    # Example 1: Get single opinion
    print("🔍 Example 1: Single Opinion Extraction")
    opinion = extractor.get_full_opinion('11113215')
    if opinion:
        print(f"Case ID: {opinion['case_id']}")
        print(f"Source: {opinion['source']} ({opinion['source_field']})")
        print(f"Text length: {len(opinion['text']):,} characters")
        print(f"Preview: {opinion['text'][:200]}...")
        
        # Analyze quality
        quality = extractor.analyze_opinion_quality(opinion['text'])
        print(f"Quality: {'✅ Valid' if quality['valid'] else '❌ Invalid'}")
        print(f"Word count: {quality['word_count']:,}")
        print(f"Legal terms: {quality['legal_term_count']}")
    
    # Example 2: Batch extraction
    print("\n🔍 Example 2: Batch Opinion Extraction")
    case_ids = ['11113215', '11113216', '12345678']  # Mix of existing and non-existing
    results = extractor.batch_extract_opinions(case_ids)
    
    print(f"Successfully extracted {len(results)} opinions:")
    for case_id, data in results.items():
        print(f"  {case_id}: {data['length']:,} chars from {data['source']}")
    
    print("\n✅ Opinion extraction examples complete!")
