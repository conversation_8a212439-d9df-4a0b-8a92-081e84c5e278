#!/usr/bin/env python3
"""
Test LangExtract with Proper Examples
"""

import os
from dotenv import load_dotenv
load_dotenv()

def test_langextract_with_examples():
    """Test LangExtract with proper examples"""
    try:
        import langextract
        from langextract import data
        
        print("=== Testing LangExtract with Examples ===")
        
        # Create example data for legal entity extraction
        example_text = """
        Judge <PERSON> presided over the case <PERSON> v<PERSON> in Harris County District Court. 
        Attorney <PERSON> represented plaintiff <PERSON>. The court awarded $25,000 in damages.
        """
        
        # Create expected extractions for the example
        example_extractions = {
            "judges": ["<PERSON>"],
            "attorneys": ["<PERSON>"], 
            "plaintiffs": ["<PERSON>"],
            "defendants": ["<PERSON>"],
            "courts": ["Harris County District Court"],
            "cases": ["<PERSON> v<PERSON>"],
            "damages": ["$25,000"]
        }
        
        # Create ExampleData object
        example_data = data.ExampleData(
            text=example_text,
            extractions=example_extractions
        )
        
        print(f"✅ Created example data")
        print(f"   Text length: {len(example_text)}")
        print(f"   Extractions: {example_extractions}")
        
        # Test text to extract from
        test_text = """
        This is a personal injury case where <PERSON>ti<PERSON> sued De<PERSON>dant <PERSON> 
        for damages resulting from a motor vehicle accident. Judge <PERSON> presided over 
        the case. The jury awarded $50,000 in actual damages and $25,000 in punitive damages. 
        The case was decided on March 15, 2023, in the 55th Judicial District Court of Harris County, Texas.
        Attorney Sarah Wilson represented the plaintiff, while Attorney Robert Davis represented the defendant.
        The court cited precedent from Doe v. Roe, 123 S.W.3d 456 (Tex. 2020).
        """
        
        # Perform extraction with examples
        print(f"\n=== Performing LangExtract Extraction ===")
        
        # Set up API key (uses Gemini by default)
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("❌ No GEMINI_API_KEY found in environment")
            return None
        
        result = langextract.extract(
            text_or_documents=test_text,
            examples=[example_data],
            prompt_description="Extract legal entities from court documents including judges, attorneys, plaintiffs, defendants, courts, cases, and monetary damages.",
            api_key=api_key,
            temperature=0.0,
            debug=True
        )
        
        print(f"✅ LangExtract extraction successful!")
        print(f"   Result type: {type(result)}")
        
        # Extract the results
        if hasattr(result, 'extractions'):
            extractions = result.extractions
            print(f"   Extractions found: {len(extractions) if extractions else 0}")
            
            if extractions:
                for key, values in extractions.items():
                    print(f"   {key}: {values}")
        
        return result
        
    except Exception as e:
        print(f"❌ LangExtract with examples test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_langextract_legal_entities():
    """Test specific legal entity extraction"""
    try:
        import langextract
        from langextract import data
        
        print("\n=== Testing Legal Entity Extraction ===")
        
        # More comprehensive legal example
        legal_example = """
        In the case of Rodriguez v. Texas Motor Corp., No. 2023-CV-1234, filed in the 
        District Court of Harris County, Texas, Judge Maria Gonzalez presided. 
        Attorney David Thompson, representing plaintiff Carlos Rodriguez, argued that 
        defendant Texas Motor Corp. was negligent. The jury awarded $150,000 in 
        compensatory damages and $50,000 in punitive damages on December 15, 2023.
        """
        
        # Comprehensive extractions
        legal_extractions = {
            "case_names": ["Rodriguez v. Texas Motor Corp."],
            "case_numbers": ["2023-CV-1234"],
            "judges": ["Maria Gonzalez"],
            "attorneys": ["David Thompson"],
            "plaintiffs": ["Carlos Rodriguez"],
            "defendants": ["Texas Motor Corp."],
            "courts": ["District Court of Harris County, Texas"],
            "damages": ["$150,000", "$50,000"],
            "dates": ["December 15, 2023"],
            "legal_claims": ["negligent"]
        }
        
        example_data = data.ExampleData(
            text=legal_example,
            extractions=legal_extractions
        )
        
        # Test with a different legal document
        test_legal_text = """
        The Supreme Court of Texas in Johnson v. City of Houston, 456 S.W.3d 789 (Tex. 2024), 
        held that municipalities have immunity. Chief Justice Rebecca Martinez wrote the majority opinion. 
        The case involved attorney Michael Chen representing plaintiff Jennifer Johnson against 
        the City of Houston. The trial court originally awarded $75,000 but this was overturned.
        """
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            print("❌ No GEMINI_API_KEY found")
            return None
        
        result = langextract.extract(
            text_or_documents=test_legal_text,
            examples=[example_data],
            prompt_description="Extract comprehensive legal information including case names, case numbers, judges, attorneys, parties, courts, monetary awards, dates, and legal claims from court documents.",
            api_key=api_key,
            temperature=0.0,
            model_id='gemini-2.5-flash',
            debug=True
        )
        
        print(f"✅ Legal entity extraction successful!")
        
        if hasattr(result, 'extractions'):
            print(f"\n=== Legal Extractions Found ===")
            for category, items in result.extractions.items():
                if items:  # Only show non-empty categories
                    print(f"{category}: {items}")
        
        return result
        
    except Exception as e:
        print(f"❌ Legal entity extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_with_graphrag():
    """Compare LangExtract results with our GraphRAG results"""
    print("\n=== Comparison: LangExtract vs GraphRAG ===")
    
    # Our GraphRAG results from verify_vertex.py
    graphrag_results = {
        "nodes": [
            {"id": "personal_injury_case", "label": "Case"},
            {"id": "john_smith", "label": "Plaintiff"},
            {"id": "mary_jones", "label": "Defendant"},
            {"id": "william_brown", "label": "Judge"},
            {"id": "harris_county_district_court", "label": "Court"},
            {"id": "sarah_wilson", "label": "Attorney"},
            {"id": "damages_50000", "label": "Damages"}
        ],
        "relationships": [
            {"type": "PRESIDED_OVER", "start": "william_brown", "end": "personal_injury_case"},
            {"type": "REPRESENTED", "start": "sarah_wilson", "end": "john_smith"},
            {"type": "FILED_IN", "start": "personal_injury_case", "end": "harris_county_district_court"},
            {"type": "AWARDED", "start": "personal_injury_case", "end": "damages_50000"},
            {"type": "OPPOSED", "start": "john_smith", "end": "mary_jones"}
        ]
    }
    
    print("GraphRAG Results:")
    print(f"  Entities: {len(graphrag_results['nodes'])}")
    print(f"  Relationships: {len(graphrag_results['relationships'])}")
    
    for node in graphrag_results['nodes']:
        print(f"    {node['label']}: {node['id']}")
    
    # Run LangExtract on the same text
    langextract_result = test_langextract_with_examples()
    
    if langextract_result and hasattr(langextract_result, 'extractions'):
        print(f"\nLangExtract Results:")
        total_entities = sum(len(items) for items in langextract_result.extractions.values() if items)
        print(f"  Total entities: {total_entities}")
        
        for category, items in langextract_result.extractions.items():
            if items:
                print(f"    {category}: {len(items)} items")
    
    return True

def main():
    """Run all LangExtract tests with examples"""
    print("=== LangExtract with Examples Test ===\n")
    
    # Test basic extraction with examples
    test_langextract_with_examples()
    
    # Test legal-specific extraction
    test_langextract_legal_entities()
    
    # Compare with GraphRAG
    compare_with_graphrag()
    
    print("\n=== LangExtract Examples Test Complete ===")

if __name__ == "__main__":
    main()