#!/usr/bin/env python3
"""
Test CSV Reading Speed
Quick test to see how fast we can read the 50GB CSV file.
"""

import bz2
import csv
import time

# Set CSV field size limit
csv.field_size_limit(10**9)

def test_csv_reading_speed(csv_file: str, max_rows: int = 50000):
    """Test how fast we can read the CSV file."""
    print(f"🔍 Testing CSV reading speed: {csv_file}")
    print(f"📊 Target: {max_rows:,} rows")
    
    start_time = time.time()
    rows_read = 0
    
    try:
        with bz2.open(csv_file, 'rt', encoding='utf-8', newline='') as f:
            reader = csv.DictReader(f, delimiter=',', quotechar='"',
                                  escapechar='\\', doublequote=False,
                                  strict=True)
            
            for row in reader:
                rows_read += 1
                
                if rows_read >= max_rows:
                    break
                
                # Progress every 10K rows
                if rows_read % 10000 == 0:
                    elapsed = time.time() - start_time
                    rate = rows_read / elapsed
                    print(f"📈 {rows_read:,} rows in {elapsed:.1f}s ({rate:.0f} rows/sec)")
    
    except Exception as e:
        print(f"❌ Error reading CSV: {e}")
        return
    
    elapsed = time.time() - start_time
    rate = rows_read / elapsed if elapsed > 0 else 0
    
    print(f"\n✅ CSV Reading Test Complete:")
    print(f"   Rows read: {rows_read:,}")
    print(f"   Time: {elapsed:.1f}s")
    print(f"   Rate: {rate:.0f} rows/sec")
    
    # Estimate full file time
    if rate > 0:
        # Estimate total rows in 50GB file (rough estimate)
        estimated_total_rows = 50_000_000  # 50M rows estimate
        estimated_time_hours = (estimated_total_rows / rate) / 3600
        print(f"   Estimated full file time: {estimated_time_hours:.1f} hours")

if __name__ == "__main__":
    test_csv_reading_speed("bulk_csv/opinions-2025-07-02.csv.bz2", max_rows=50000)
