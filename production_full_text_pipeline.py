#!/usr/bin/env python3
"""
Production Full-Text Pipeline Coordinator
Processes REAL legal opinions with full text from both bulk CSV and API systems
"""

import os
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import json

# Import components for full text processing
from unified_opinion_extractor import UnifiedOpinionExtractor, FullOpinion, ExtractionReport
from voyage_contextual_embedder import VoyageContextualEmbedder, ContextAwareLegalChunker, ContextualChunk, EmbeddedChunk
from setup_legal_graphrag import LegalGraphRAGPipeline
from global_uid_system import GlobalUIDManager, GlobalUIDRecord, StorageSystem

from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

load_dotenv()

@dataclass
class FullTextProcessingResult:
    """Result of processing a single full-text opinion through the pipeline"""
    global_uid: str
    case_id: str
    case_name: str
    source_system: str  # 'bulk_csv' or 'api'
    source_field: str   # Which field contained the text
    original_text_length: int
    status: str  # 'success', 'error', 'partial'
    
    # Processing metrics
    chunks_created: int
    chunks_embedded: int
    entities_extracted: int
    relationships_extracted: int
    
    # Storage tracking
    supabase_stored: bool = False
    pinecone_stored: bool = False
    neo4j_stored: bool = False
    
    # Quality metrics
    substantial_content: bool = False
    text_quality_score: float = 0.0
    
    # Timing and costs
    processing_time_seconds: float = 0.0
    api_costs: Dict[str, float] = None
    
    # Error tracking
    error_message: Optional[str] = None
    errors: List[str] = None
    
    def __post_init__(self):
        if self.api_costs is None:
            self.api_costs = {}
        if self.errors is None:
            self.errors = []

@dataclass 
class FullTextBatchReport:
    """Report for batch processing of full-text opinions"""
    total_documents: int
    successful_documents: int
    failed_documents: int
    partial_documents: int
    
    # Source system breakdown
    bulk_csv_success: int
    api_success: int
    bulk_csv_total: int
    api_total: int
    
    # Content metrics
    total_chunks: int
    total_entities: int
    total_relationships: int
    total_original_text_length: int
    average_original_text_length: int
    substantial_content_count: int
    
    # Processing metrics
    total_processing_time: float
    total_api_costs: Dict[str, float]
    
    # Quality metrics
    average_entities_per_doc: float
    average_chunks_per_doc: float
    average_text_quality: float
    
    # Individual results
    results: List[FullTextProcessingResult]
    
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
    
    @property
    def success_rate(self) -> float:
        if self.total_documents == 0:
            return 100.0
        return (self.successful_documents / self.total_documents) * 100.0
    
    @property
    def substantial_content_rate(self) -> float:
        if self.total_documents == 0:
            return 0.0
        return (self.substantial_content_count / self.total_documents) * 100.0

class ProductionFullTextPipeline:
    """
    Production pipeline for processing full legal opinion text
    Combines both bulk CSV and API systems for complete coverage
    """
    
    def __init__(self):
        logger.info("🚀 Initializing Production Full-Text Pipeline...")
        
        # Initialize full-text extraction
        self.opinion_extractor = UnifiedOpinionExtractor()
        
        # Initialize processing components
        self.legal_chunker = ContextAwareLegalChunker(chunk_size=2000, overlap=200)
        self.embedder = VoyageContextualEmbedder(model="voyage-context-3", output_dimension=1024)
        self.graphrag_pipeline = LegalGraphRAGPipeline()
        self.uid_manager = GlobalUIDManager()
        
        logger.info("✅ All full-text pipeline components initialized")
    
    async def process_full_text_opinions_test(self,
                                            cases_per_system: int = 5,
                                            practice_area: str = "personal_injury",
                                            max_concurrent: int = 3) -> FullTextBatchReport:
        """
        Test processing with full-text opinions from both systems
        """
        logger.info(f"🔄 Starting full-text test: {cases_per_system} cases per system")
        
        start_time = datetime.utcnow()
        
        try:
            # Step 1: Get sample cases from both systems
            logger.info("📋 Getting sample cases from both systems...")
            api_cases, bulk_cases = await self.opinion_extractor.get_sample_cases_from_each_system(
                count_per_system=cases_per_system
            )
            
            # Combine all cases for processing
            all_case_ids = api_cases + bulk_cases
            
            if not all_case_ids:
                logger.error("❌ No cases found for testing")
                return self._create_empty_report()
            
            logger.info(f"📄 Selected {len(all_case_ids)} cases: {len(api_cases)} API + {len(bulk_cases)} bulk CSV")
            
            # Step 2: Process cases with full text
            logger.info(f"⚙️  Processing {len(all_case_ids)} full-text opinions with {max_concurrent} workers...")
            
            # Use semaphore to control concurrency
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_with_semaphore(case_id: str) -> FullTextProcessingResult:
                async with semaphore:
                    return await self._process_full_text_opinion(case_id, practice_area)
            
            # Process all cases concurrently
            tasks = [process_with_semaphore(case_id) for case_id in all_case_ids]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions in results
            processing_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"❌ Case {all_case_ids[i]} failed: {result}")
                    # Create error result
                    error_result = FullTextProcessingResult(
                        global_uid="unknown",
                        case_id=all_case_ids[i],
                        case_name="Unknown",
                        source_system="unknown",
                        source_field="unknown",
                        original_text_length=0,
                        status="error",
                        chunks_created=0,
                        chunks_embedded=0,
                        entities_extracted=0,
                        relationships_extracted=0,
                        error_message=str(result)
                    )
                    processing_results.append(error_result)
                else:
                    processing_results.append(result)
            
            # Step 3: Generate comprehensive report
            total_time = (datetime.utcnow() - start_time).total_seconds()
            report = self._generate_full_text_report(
                processing_results, 
                total_time,
                len(api_cases),
                len(bulk_cases)
            )
            
            # Step 4: Log detailed summary
            self._log_full_text_summary(report)
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Full-text batch processing failed: {e}")
            import traceback
            traceback.print_exc()
            return self._create_empty_report()
    
    async def _process_full_text_opinion(self, 
                                       case_id: str,
                                       practice_area: str) -> FullTextProcessingResult:
        """
        Process a single full-text opinion through the complete pipeline
        """
        doc_start_time = datetime.utcnow()
        
        try:
            logger.info(f"📖 Processing full-text opinion: {case_id}")
            
            # Step 1: Extract full opinion text from unified extractor
            full_opinion = await self.opinion_extractor.get_full_opinion_unified(case_id)
            
            if not full_opinion:
                return FullTextProcessingResult(
                    global_uid="not_found",
                    case_id=case_id,
                    case_name="Not Found",
                    source_system="unknown",
                    source_field="none",
                    original_text_length=0,
                    status="error",
                    chunks_created=0,
                    chunks_embedded=0,
                    entities_extracted=0,
                    relationships_extracted=0,
                    error_message="Opinion not found in either system"
                )
            
            logger.info(f"✅ Found opinion: {full_opinion.case_name} "
                       f"({full_opinion.text_length:,} chars from {full_opinion.source_system})")
            
            # Step 2: Generate global UID for tracking
            global_uid = self.uid_manager.generate_global_uid(
                document_id=case_id,
                source_system=f"unified_{full_opinion.source_system}"
            )
            
            # Step 3: Create contextual chunks from full text
            logger.info(f"🔪 Creating contextual chunks for full text ({full_opinion.text_length:,} chars)")
            
            # Convert FullOpinion to format expected by chunker
            document_dict = {
                "id": full_opinion.case_id,
                "case_name": full_opinion.case_name,
                "court_name": full_opinion.metadata.get("court", "Unknown Court"),
                "jurisdiction": "TX" if full_opinion.source_system == "bulk_csv" else "FED",
                "practice_area": practice_area,
                "content": full_opinion.full_text,  # Use FULL TEXT instead of metadata
                "date_filed": full_opinion.metadata.get("date_created"),
                "word_count": len(full_opinion.full_text.split()),
                "source_system": full_opinion.source_system,
                "source_field": full_opinion.source_field
            }
            
            contextual_chunks = await self.legal_chunker.create_contextual_chunks(document_dict)
            
            if not contextual_chunks:
                return FullTextProcessingResult(
                    global_uid=global_uid,
                    case_id=case_id,
                    case_name=full_opinion.case_name,
                    source_system=full_opinion.source_system,
                    source_field=full_opinion.source_field,
                    original_text_length=full_opinion.text_length,
                    status="error",
                    chunks_created=0,
                    chunks_embedded=0,
                    entities_extracted=0,
                    relationships_extracted=0,
                    error_message="No valid chunks created from full text"
                )
            
            logger.info(f"✅ Created {len(contextual_chunks)} contextual chunks from full text")
            
            # Step 4: Generate contextual embeddings from full text chunks
            logger.info(f"🔄 Generating contextual embeddings for {len(contextual_chunks)} full-text chunks")
            embedded_chunks = await self.embedder.embed_document_with_full_context(contextual_chunks)
            
            if not embedded_chunks:
                return FullTextProcessingResult(
                    global_uid=global_uid,
                    case_id=case_id,
                    case_name=full_opinion.case_name,
                    source_system=full_opinion.source_system,
                    source_field=full_opinion.source_field,
                    original_text_length=full_opinion.text_length,
                    status="error",
                    chunks_created=len(contextual_chunks),
                    chunks_embedded=0,
                    entities_extracted=0,
                    relationships_extracted=0,
                    error_message="Failed to generate embeddings from full text"
                )
            
            # Step 5: Extract entities using GraphRAG with full text
            logger.info(f"🧠 Extracting entities with GraphRAG from full text")
            
            graphrag_result = await self.graphrag_pipeline.process_document_with_contextual_chunking(
                document_dict, practice_area
            )
            
            entities_extracted = graphrag_result.get('entities_extracted', 0)
            relationships_extracted = graphrag_result.get('relationships_extracted', 0)
            
            # Step 6: Calculate text quality score
            text_quality_score = self._calculate_text_quality_score(full_opinion)
            
            # Step 7: Register global UID
            logger.info(f"🔗 Registering global UID: {global_uid}")
            
            uid_record = GlobalUIDRecord(
                global_uid=global_uid,
                document_id=case_id,
                chunk_id=None,
                entity_id=None,
                source_system=f"unified_{full_opinion.source_system}",
                source_path=full_opinion.gcs_path or "csv_direct",
                gcs_path=full_opinion.gcs_path,
                document_hash=None  # Could add text hash here
            )
            
            uid_registered = await self.uid_manager.register_global_uid(uid_record)
            
            # Step 8: Create comprehensive result
            processing_time = (datetime.utcnow() - doc_start_time).total_seconds()
            
            result = FullTextProcessingResult(
                global_uid=global_uid,
                case_id=case_id,
                case_name=full_opinion.case_name,
                source_system=full_opinion.source_system,
                source_field=full_opinion.source_field,
                original_text_length=full_opinion.text_length,
                status="success",
                chunks_created=len(contextual_chunks),
                chunks_embedded=len(embedded_chunks),
                entities_extracted=entities_extracted,
                relationships_extracted=relationships_extracted,
                supabase_stored=uid_registered,
                pinecone_stored=False,  # Would need to check Pinecone storage
                neo4j_stored=entities_extracted > 0,
                substantial_content=full_opinion.is_substantial,
                text_quality_score=text_quality_score,
                processing_time_seconds=processing_time,
                api_costs={
                    "voyage_embeddings": len(embedded_chunks) * 0.0001,
                    "vertex_ai": entities_extracted * 0.001
                }
            )
            
            logger.info(f"✅ Successfully processed full-text opinion {full_opinion.case_name}: "
                       f"{len(embedded_chunks)} chunks, {entities_extracted} entities "
                       f"from {full_opinion.text_length:,} chars ({full_opinion.source_system})")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error processing full-text opinion {case_id}: {e}")
            
            processing_time = (datetime.utcnow() - doc_start_time).total_seconds()
            
            return FullTextProcessingResult(
                global_uid="error",
                case_id=case_id,
                case_name="Error",
                source_system="unknown",
                source_field="unknown",
                original_text_length=0,
                status="error",
                chunks_created=0,
                chunks_embedded=0,
                entities_extracted=0,
                relationships_extracted=0,
                processing_time_seconds=processing_time,
                error_message=str(e),
                errors=[str(e)]
            )
    
    def _calculate_text_quality_score(self, opinion: FullOpinion) -> float:
        """Calculate quality score for legal opinion text"""
        score = 0.0
        
        # Length score (normalized)
        length_score = min(opinion.text_length / 5000, 1.0) * 0.3
        score += length_score
        
        # Legal content indicators
        legal_keywords = [
            'court', 'opinion', 'judgment', 'plaintiff', 'defendant',
            'appeal', 'motion', 'order', 'ruling', 'decision',
            'evidence', 'testimony', 'statute', 'law', 'legal'
        ]
        
        text_lower = opinion.full_text.lower()
        keyword_matches = sum(1 for keyword in legal_keywords if keyword in text_lower)
        keyword_score = min(keyword_matches / 10, 1.0) * 0.4
        score += keyword_score
        
        # Structure indicators (paragraphs, sentences)
        paragraph_count = opinion.full_text.count('\n\n') + 1
        paragraph_score = min(paragraph_count / 20, 1.0) * 0.3
        score += paragraph_score
        
        return min(score, 1.0)
    
    def _generate_full_text_report(self,
                                 results: List[FullTextProcessingResult],
                                 total_time: float,
                                 api_total: int,
                                 bulk_total: int) -> FullTextBatchReport:
        """Generate comprehensive full-text processing report"""
        
        successful_results = [r for r in results if r.status == "success"]
        failed_results = [r for r in results if r.status == "error"]
        partial_results = [r for r in results if r.status == "partial"]
        
        # System breakdown
        api_success = len([r for r in successful_results if r.source_system == "api"])
        bulk_csv_success = len([r for r in successful_results if r.source_system == "bulk_csv"])
        
        # Content metrics
        total_chunks = sum(r.chunks_embedded for r in results)
        total_entities = sum(r.entities_extracted for r in results)
        total_relationships = sum(r.relationships_extracted for r in results)
        total_original_text_length = sum(r.original_text_length for r in results)
        average_original_text_length = total_original_text_length // len(results) if results else 0
        substantial_content_count = len([r for r in results if r.substantial_content])
        
        # Calculate API costs
        total_costs = {}
        for result in results:
            for api, cost in result.api_costs.items():
                total_costs[api] = total_costs.get(api, 0) + cost
        
        # Calculate averages
        avg_entities = total_entities / len(results) if results else 0
        avg_chunks = total_chunks / len(results) if results else 0
        avg_text_quality = sum(r.text_quality_score for r in results) / len(results) if results else 0
        
        return FullTextBatchReport(
            total_documents=len(results),
            successful_documents=len(successful_results),
            failed_documents=len(failed_results),
            partial_documents=len(partial_results),
            bulk_csv_success=bulk_csv_success,
            api_success=api_success,
            bulk_csv_total=bulk_total,
            api_total=api_total,
            total_chunks=total_chunks,
            total_entities=total_entities,
            total_relationships=total_relationships,
            total_original_text_length=total_original_text_length,
            average_original_text_length=average_original_text_length,
            substantial_content_count=substantial_content_count,
            total_processing_time=total_time,
            total_api_costs=total_costs,
            average_entities_per_doc=avg_entities,
            average_chunks_per_doc=avg_chunks,
            average_text_quality=avg_text_quality,
            results=results
        )
    
    def _create_empty_report(self) -> FullTextBatchReport:
        """Create empty report for error cases"""
        return FullTextBatchReport(
            total_documents=0,
            successful_documents=0,
            failed_documents=0,
            partial_documents=0,
            bulk_csv_success=0,
            api_success=0,
            bulk_csv_total=0,
            api_total=0,
            total_chunks=0,
            total_entities=0,
            total_relationships=0,
            total_original_text_length=0,
            average_original_text_length=0,
            substantial_content_count=0,
            total_processing_time=0.0,
            total_api_costs={},
            average_entities_per_doc=0.0,
            average_chunks_per_doc=0.0,
            average_text_quality=0.0,
            results=[]
        )
    
    def _log_full_text_summary(self, report: FullTextBatchReport):
        """Log comprehensive full-text processing summary"""
        
        logger.info("=" * 70)
        logger.info("📚 FULL-TEXT PIPELINE PROCESSING SUMMARY")
        logger.info("=" * 70)
        logger.info(f"📄 Total Documents: {report.total_documents}")
        logger.info(f"✅ Successful: {report.successful_documents}")
        logger.info(f"❌ Failed: {report.failed_documents}")
        logger.info(f"📈 Success Rate: {report.success_rate:.1f}%")
        logger.info("")
        logger.info(f"🗂️  Source System Breakdown:")
        logger.info(f"   API System: {report.api_success}/{report.api_total} success")
        logger.info(f"   Bulk CSV: {report.bulk_csv_success}/{report.bulk_csv_total} success")
        logger.info("")
        logger.info(f"📝 Content Quality Metrics:")
        logger.info(f"   Total Original Text: {report.total_original_text_length:,} characters")
        logger.info(f"   Average Text Length: {report.average_original_text_length:,} characters")
        logger.info(f"   Substantial Content: {report.substantial_content_count}/{report.total_documents} ({report.substantial_content_rate:.1f}%)")
        logger.info(f"   Average Quality Score: {report.average_text_quality:.2f}/1.0")
        logger.info("")
        logger.info(f"🔪 Processing Metrics:")
        logger.info(f"   Total Chunks: {report.total_chunks}")
        logger.info(f"   Total Entities: {report.total_entities}")
        logger.info(f"   Total Relationships: {report.total_relationships}")
        logger.info(f"   Avg Entities/Doc: {report.average_entities_per_doc:.1f}")
        logger.info(f"   Avg Chunks/Doc: {report.average_chunks_per_doc:.1f}")
        logger.info("")
        logger.info(f"⏱️  Processing Time: {report.total_processing_time:.1f}s")
        
        if report.total_api_costs:
            logger.info("💰 API Costs:")
            for api, cost in report.total_api_costs.items():
                logger.info(f"   {api}: ${cost:.4f}")
        
        logger.info("=" * 70)
        
        # Log sample successful documents with details
        successful_docs = [r for r in report.results if r.status == "success"]
        if successful_docs:
            logger.info("🎯 Sample Successful Full-Text Documents:")
            for doc in successful_docs[:3]:
                logger.info(f"   - {doc.case_name}:")
                logger.info(f"     Source: {doc.source_system} ({doc.source_field})")
                logger.info(f"     Text: {doc.original_text_length:,} chars")
                logger.info(f"     Processing: {doc.entities_extracted} entities, {doc.chunks_embedded} chunks")
                logger.info(f"     Quality: {doc.text_quality_score:.2f} ({'substantial' if doc.substantial_content else 'limited'})")
                logger.info(f"     Time: {doc.processing_time_seconds:.1f}s")
                logger.info()
    
    async def save_full_text_report(self, 
                                  report: FullTextBatchReport, 
                                  filename: Optional[str] = None) -> str:
        """Save full-text processing report to JSON file"""
        
        if filename is None:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"full_text_pipeline_report_{timestamp}.json"
        
        # Convert report to JSON-serializable format
        report_dict = asdict(report)
        report_dict['timestamp'] = report.timestamp.isoformat()
        
        # Convert results to dict
        report_dict['results'] = [asdict(result) for result in report.results]
        
        with open(filename, 'w') as f:
            json.dump(report_dict, f, indent=2, default=str)
        
        logger.info(f"📄 Full-text processing report saved: {filename}")
        return filename
    
    def close(self):
        """Clean up pipeline resources"""
        if hasattr(self.graphrag_pipeline, 'close'):
            self.graphrag_pipeline.close()
        logger.info("🔒 Full-text pipeline coordinator closed")

# Test function for full-text pipeline
async def test_full_text_pipeline():
    """Test the full-text pipeline with real opinion text from both systems"""
    
    print("=== Testing Full-Text Pipeline with Real Opinion Text ===\n")
    
    try:
        # Initialize pipeline
        pipeline = ProductionFullTextPipeline()
        
        # Process 5 cases from each system (10 total)
        print("🚀 Processing full-text opinions from both systems...")
        report = await pipeline.process_full_text_opinions_test(
            cases_per_system=5,
            practice_area="personal_injury",
            max_concurrent=3
        )
        
        # Save report
        report_file = await pipeline.save_full_text_report(report)
        
        print(f"\n📄 Full-text processing report saved: {report_file}")
        print(f"✅ Full-text pipeline test completed!")
        print(f"📊 Success rate: {report.success_rate:.1f}%")
        print(f"📝 Substantial content rate: {report.substantial_content_rate:.1f}%")
        print(f"📈 Average text length: {report.average_original_text_length:,} characters")
        
        # Cleanup
        pipeline.close()
        
        return report
        
    except Exception as e:
        print(f"❌ Full-text pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_full_text_pipeline())