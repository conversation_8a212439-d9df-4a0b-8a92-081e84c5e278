    # Regional Legal GraphRAG Pipeline Deployment Guide

    This guide provides step-by-step instructions for deploying the Texas Legal GraphRAG pipeline to other regions (e.g., EU, California, New York) with Vertex AI, voyage-context-3 embeddings, and Neo4j GraphRAG SDK.

    ## Overview

    The pipeline integrates:
    - **Vertex AI Gemini 2.5 Pro** with structured JSON output for entity/relationship extraction
    - **voyage-context-3** contextualized embeddings for legal document understanding
    - **Neo4j GraphRAG SDK v1.9.1** with legal domain schema
    - **Neo4j AuraDB** for graph storage
    - **Legal document processing** with domain-specific text splitting

    ## Prerequisites

    - Google Cloud Platform account with billing enabled
    - Neo4j AuraDB account
    - Voyage AI API access
    - Python 3.10+ environment

    ## 1. Google Cloud Platform Setup

    ### 1.1 Create New GCP Project

    ```bash
    # Set your region-specific project details
    export REGION="eu"  # or "california", "newyork", etc.
    export PROJECT_ID="${REGION}-legal-graphrag-$(date +%s)"
    export LOCATION="europe-west1"  # EU region, adjust as needed

    # Create project
    gcloud projects create $PROJECT_ID --name="$REGION Legal GraphRAG"
    gcloud config set project $PROJECT_ID

    # Enable billing (replace BILLING_ACCOUNT_ID with your account)
    gcloud billing projects link $PROJECT_ID --billing-account=YOUR_BILLING_ACCOUNT_ID
    ```

    ### 1.2 Enable Required Services

    ```bash
    # Enable AI Platform services
    gcloud services enable aiplatform.googleapis.com
    gcloud services enable compute.googleapis.com
    ```

    ### 1.3 Create Service Account

    ```bash
    # Create service account for the region
    gcloud iam service-accounts create ${REGION}-legal-sa \
    --display-name="${REGION} Legal GraphRAG SA" \
    --description="Service account for ${REGION} legal document processing"

    # Grant necessary permissions
    gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:${REGION}-legal-sa@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/aiplatform.user"

    # Create credentials directory
    mkdir -p "$HOME/.config/gcp"

    # Generate service account key
    gcloud iam service-accounts keys create "$HOME/.config/gcp/${REGION}-legal-sa.json" \
    --iam-account="${REGION}-legal-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    ```

    ### 1.4 Configure Authentication

    ```bash
    # Set application default credentials
    export GOOGLE_APPLICATION_CREDENTIALS="$HOME/.config/gcp/${REGION}-legal-sa.json"

    # Test authentication
    gcloud auth application-default login

    # Verify Vertex AI access
    gcloud ai models list --region=$LOCATION
    ```

    ## 2. Neo4j AuraDB Setup

    ### 2.1 Create Database Instance

    1. Go to [Neo4j AuraDB Console](https://console.neo4j.io/)
    2. Create new instance:
    - **Name**: `${region}-legal-graphrag`
    - **Region**: Choose closest to your Vertex AI region
    - **Size**: Professional (recommended for production)
    3. Save connection details:
    - URI: `neo4j+s://[instance-id].databases.neo4j.io`
    - Username: `neo4j`
    - Password: [generated password]

    ### 2.2 Test Neo4j Connection

    ```bash
    # Install Neo4j driver
    pip install neo4j

    # Test connection (replace with your details)
    python -c "
    from neo4j import GraphDatabase
    driver = GraphDatabase.driver('neo4j+s://your-instance.databases.neo4j.io', 
                                auth=('neo4j', 'your-password'))
    with driver.session() as session:
        result = session.run('RETURN \"Connection successful\" as message')
        print(result.single()['message'])
    driver.close()
    "
    ```

    ## 3. Environment Configuration

    ### 3.1 Create Environment File

    Create `.env` file with region-specific configuration:

    ```bash
    # Regional Legal GraphRAG Configuration
    PROJECT_NAME=${REGION}_legal_graphrag

    # Vertex AI Configuration
    VERTEX_PROJECT_ID=${PROJECT_ID}
    VERTEX_LOCATION=${LOCATION}
    VERTEX_MODEL=gemini-2.5-pro
    GOOGLE_APPLICATION_CREDENTIALS=$HOME/.config/gcp/${REGION}-legal-sa.json

    # Neo4j Configuration
    NEO4J_URI=neo4j+s://[your-instance-id].databases.neo4j.io
    NEO4J_USERNAME=neo4j
    NEO4J_PASSWORD=[your-password]

    # Voyage AI Configuration
    VOYAGE_API_KEY=[your-voyage-api-key]
    VOYAGE_EMBEDDING_MODEL=voyage-context-3

    # Regional Legal Domain Settings
    LEGAL_JURISDICTION=${REGION}
    COURT_SYSTEM=${REGION}_courts
    CITATION_FORMAT=${REGION}_citations
    ```

    ### 3.2 Install Dependencies

    ```bash
    # Core dependencies
    pip install python-dotenv
    pip install neo4j
    pip install voyageai==0.3.4
    pip install google-cloud-aiplatform
    pip install vertexai
    pip install langchain-text-splitters

    # Install Neo4j GraphRAG from GitHub (not on PyPI)
    pip install git+https://github.com/neo4j/neo4j-graphrag-python.git
    ```

    ## 4. Regional Legal Schema Adaptation

    ### 4.1 Define Regional Entity Types

    Customize for your region's legal system:

    ```python
    # Example: EU Legal Entity Types
    eu_entity_types = [
        "Case", "Judge", "Court", "Barrister", "Solicitor", 
        "Claimant", "Defendant", "Directive", "Regulation",
        "Citation", "Judgment", "Damages", "Settlement",
        "Tribunal", "Appeal", "Precedent"
    ]

    # Example: EU Court System Patterns
    eu_court_patterns = [
        r"European Court of Justice",
        r"European Court of Human Rights", 
        r"High Court.*England.*Wales",
        r"Court of Appeal.*(?:Civil|Criminal).*Division",
        r"(?:County|Crown) Court",
        r"(?:Upper|First-tier) Tribunal"
    ]

    # Example: EU Citation Patterns  
    eu_citation_patterns = [
        r"C-\d+/\d+",  # ECJ cases
        r"\[\d{4}\] EWCA (?:Civ|Crim) \d+",  # Court of Appeal
        r"\[\d{4}\] EWHC \d+ \([A-Z]+\)",  # High Court
        r"\[\d{4}\] UKSC \d+",  # Supreme Court
    ]
    ```

    ### 4.2 Create Regional Schema Configuration

    ```python
    from dataclasses import dataclass
    from typing import List, Dict

    @dataclass
    class RegionalLegalSchema:
        """Regional legal domain schema configuration"""
        
        jurisdiction: str
        entity_types: List[str]
        relationship_types: List[str]
        court_patterns: List[str]
        citation_patterns: List[str]
        damage_patterns: List[str]
        
        @classmethod
        def create_eu_schema(cls):
            return cls(
                jurisdiction="EU",
                entity_types=[
                    "Case", "Judge", "Court", "Barrister", "Solicitor",
                    "Claimant", "Defendant", "Directive", "Regulation", 
                    "Citation", "Judgment", "Damages", "Settlement"
                ],
                relationship_types=[
                    "PRESIDED_OVER", "REPRESENTED", "FILED_IN", "AWARDED",
                    "CITED", "OPPOSED", "APPEALED_TO", "DECIDED",
                    "IMPLEMENTS", "INTERPRETS", "OVERRULED"
                ],
                court_patterns=eu_court_patterns,
                citation_patterns=eu_citation_patterns,
                damage_patterns=[
                    r"€[\d,]+(?:\.\d{2})?(?:\s+(?:million|thousand|billion))?",
                    r"(?:actual|punitive|exemplary|nominal)\s+damages",
                    r"(?:compensation|award).*€[\d,]+"
                ]
            )
    ```

    ## 5. Pipeline Implementation

    ### 5.1 Create Regional Pipeline

    Copy and modify `setup_legal_graphrag.py`:

    ```python
    #!/usr/bin/env python3
    """
    Regional Legal GraphRAG Pipeline
    Adapted for {REGION} legal domain
    """

    import os
    from typing import Dict, List, Optional, Any
    from neo4j_graphrag.experimental.components.schema import (
        GraphSchema, NodeType, RelationshipType
    )

    class RegionalLegalGraphRAGPipeline:
        """Regional legal GraphRAG pipeline"""
        
        def __init__(self, region: str = "eu"):
            self.region = region
            self.schema = self._create_regional_schema()
            
            # Initialize with regional settings
            self._setup_vertex_ai()
            self._setup_neo4j()
            self._setup_embeddings()
            self._create_pipeline()
        
        def _create_regional_schema(self):
            """Create region-specific legal schema"""
            if self.region.lower() == "eu":
                schema_config = RegionalLegalSchema.create_eu_schema()
            else:
                # Add other regions as needed
                schema_config = RegionalLegalSchema.create_default_schema()
                
            # Convert to Neo4j GraphRAG schema format
            node_types = [
                NodeType(label=entity_type, description=f"{entity_type} in {self.region} legal system")
                for entity_type in schema_config.entity_types
            ]
            
            relationship_types = [
                RelationshipType(label=rel_type, description=f"{rel_type} relationship")
                for rel_type in schema_config.relationship_types
            ]
            
            return GraphSchema(
                node_types=tuple(node_types),
                relationship_types=tuple(relationship_types),
                patterns=()
            )
    ```

    ### 5.2 Regional Text Splitter

    ```python
    def create_regional_text_splitter(region: str):
        """Create region-specific legal text splitter"""
        
        if region.lower() == "eu":
            separators = [
                "\n\n\n",
                "\nHELD:",
                "\nJUDGMENT:",
                "\nORDER:",
                "\nDIRECTIVE:",
                "\n\n",
                ". ",
                "; ",
                ", ",
                " ",
                ""
            ]
        else:
            # Default legal separators
            separators = [
                "\n\n\n",
                "\nHELD:",
                "\nCONCLUSION:",
                "\nDISPOSITION:",
                "\n\n",
                ". ",
                "; ",
                ", ",
                " ",
                ""
            ]
        
        return RecursiveCharacterTextSplitter(
            chunk_size=2000,
            chunk_overlap=200,
            length_function=len,
            separators=separators,
            keep_separator=True
        )
    ```

    ## 6. Testing and Validation

    ### 6.1 Create Regional Test Script

    ```python
    #!/usr/bin/env python3
    """
    Regional Legal GraphRAG Test
    """

    async def test_regional_pipeline(region: str):
        """Test regional legal GraphRAG setup"""
        
        # Regional sample documents
        sample_documents = {
            "eu": {
                "id": "eu_test_case_001",
                "case_name": "Smith v. Jones Ltd",
                "court": "High Court of Justice, Queen's Bench Division",
                "plain_text": """
                This is a personal injury claim where Claimant John Smith sued 
                Defendant Jones Ltd for damages resulting from a workplace accident.
                Mr Justice Brown presided over the case. The court awarded £75,000 
                in compensatory damages. The case was decided on 15 March 2023.
                Barrister Sarah Wilson represented the claimant.
                The court cited precedent from Donoghue v. Stevenson [1932] AC 562.
                """
            }
        }
        
        try:
            pipeline = RegionalLegalGraphRAGPipeline(region=region)
            sample_doc = sample_documents.get(region)
            
            if not sample_doc:
                print(f"No test document available for region: {region}")
                return False
                
            result = await pipeline.process_legal_document(sample_doc)
            
            print(f"=== {region.upper()} Legal GraphRAG Test Results ===")
            print(f"Document ID: {result['document_id']}")
            print(f"Entities extracted: {result['entities_extracted']}")
            print(f"Relationships extracted: {result['relationships_extracted']}")
            
            return result['entities_extracted'] > 0
            
        except Exception as e:
            print(f"Regional test failed for {region}: {e}")
            return False

    if __name__ == "__main__":
        import asyncio
        asyncio.run(test_regional_pipeline("eu"))
    ```

    ### 6.2 Verification Script

    ```bash
    #!/bin/bash
    # Regional Deployment Verification

    echo "=== Regional Legal GraphRAG Deployment Verification ==="

    # Check environment variables
    echo "Checking environment configuration..."
    python -c "
    import os
    required_vars = [
        'VERTEX_PROJECT_ID', 'VERTEX_LOCATION', 'NEO4J_URI', 
        'NEO4J_PASSWORD', 'VOYAGE_API_KEY'
    ]
    missing = [var for var in required_vars if not os.getenv(var)]
    if missing:
        print(f'Missing environment variables: {missing}')
        exit(1)
    else:
        print('✅ All environment variables configured')
    "

    # Test Vertex AI connection
    echo "Testing Vertex AI connection..."
    python -c "
    import vertexai
    import os
    try:
        vertexai.init(
            project=os.getenv('VERTEX_PROJECT_ID'),
            location=os.getenv('VERTEX_LOCATION')
        )
        print('✅ Vertex AI connection successful')
    except Exception as e:
        print(f'❌ Vertex AI connection failed: {e}')
        exit(1)
    "

    # Test Neo4j connection
    echo "Testing Neo4j connection..."
    python -c "
    from neo4j import GraphDatabase
    import os
    try:
        driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=('neo4j', os.getenv('NEO4J_PASSWORD'))
        )
        with driver.session() as session:
            session.run('RETURN 1')
        driver.close()
        print('✅ Neo4j connection successful')
    except Exception as e:
        print(f'❌ Neo4j connection failed: {e}')
        exit(1)
    "

    # Test Voyage AI connection
    echo "Testing Voyage AI connection..."
    python -c "
    import voyageai
    import os
    try:
        client = voyageai.Client(api_key=os.getenv('VOYAGE_API_KEY'))
        result = client.contextualized_embed(
            inputs=[['test']],
            model='voyage-context-3',
            input_type='query',
            output_dimension=1024
        )
        print('✅ Voyage AI connection successful')
    except Exception as e:
        print(f'❌ Voyage AI connection failed: {e}')
        exit(1)
    "

    echo "✅ All connections verified successfully!"
    ```

    ## 7. Deployment Checklist

    ### 7.1 Pre-deployment

    - [ ] GCP project created with billing enabled
    - [ ] Vertex AI services enabled in target region
    - [ ] Service account created with proper permissions
    - [ ] Neo4j AuraDB instance provisioned
    - [ ] Voyage AI API key obtained
    - [ ] Environment variables configured
    - [ ] Dependencies installed

    ### 7.2 Schema Configuration

    - [ ] Regional entity types defined
    - [ ] Regional relationship types defined
    - [ ] Court system patterns adapted
    - [ ] Citation formats configured
    - [ ] Legal separators customized

    ### 7.3 Testing

    - [ ] Individual component tests pass
    - [ ] End-to-end pipeline test passes
    - [ ] Regional sample document processed
    - [ ] Entity extraction validated
    - [ ] Relationship extraction validated

    ### 7.4 Production Readiness

    - [ ] Error handling implemented
    - [ ] Logging configured
    - [ ] Monitoring setup
    - [ ] Performance benchmarks established
    - [ ] Cost monitoring enabled

    ## 8. Cost Optimization

    ### 8.1 Vertex AI Costs

    ```python
    # Configure generation settings for cost efficiency
    generation_config = GenerationConfig(
        temperature=0.0,  # Deterministic output
        max_output_tokens=2048,  # Limit output size
        response_mime_type="application/json",
        response_schema=RESPONSE_SCHEMA
    )
    ```

    ### 8.2 Voyage AI Costs

    ```python
    # Batch embedding requests when possible
    def batch_embed_documents(texts: List[str], batch_size: int = 100):
        """Process embeddings in batches for cost efficiency"""
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i+batch_size]
            yield embed_batch(batch)
    ```

    ### 8.3 Neo4j Costs

    - Use Neo4j AuraDB Professional for production
    - Monitor query performance and optimize indexes
    - Implement connection pooling

    ## 9. Monitoring and Maintenance

    ### 9.1 Key Metrics

    - Entity extraction accuracy
    - Relationship extraction accuracy
    - Processing latency
    - API costs per document
    - Error rates

    ### 9.2 Regular Maintenance

    - Update legal schema as laws change
    - Retrain extraction patterns
    - Monitor API rate limits
    - Update security credentials
    - Review cost optimization

    ## 10. Regional Variations

    ### 10.1 United States (State-specific)

    ```python
    # California-specific configuration
    california_config = {
        "jurisdiction": "California",
        "court_patterns": [
            r"California Supreme Court",
            r"California Court of Appeal.*District.*Division",
            r"Superior Court.*(?:Los Angeles|San Francisco|San Diego).*County"
        ],
        "citation_patterns": [
            r"\d+\s+Cal\.\s*(?:2d|3d|4th|5th)\s+\d+",
            r"\d+\s+Cal\.App\.\s*(?:2d|3d|4th|5th)\s+\d+",
            r"\d+\s+Cal\.Rptr\.\s*(?:2d|3d)\s+\d+"
        ]
    }
    ```

    ### 10.2 United Kingdom

    ```python
    # UK-specific configuration
    uk_config = {
        "jurisdiction": "UK",
        "entity_types": [
            "Case", "Judge", "Lord Justice", "Master", "Court",
            "Barrister", "Solicitor", "QC", "Claimant", "Defendant",
            "Act", "Statutory Instrument", "Citation", "Judgment"
        ],
        "currency_symbol": "£"
    }
    ```

    This guide provides a complete framework for deploying the legal GraphRAG pipeline to any region with appropriate customizations for local legal systems, courts, and citation formats.