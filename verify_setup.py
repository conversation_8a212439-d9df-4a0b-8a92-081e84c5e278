#!/usr/bin/env python3
"""
Quick verification script for Phase 1 setup
Tests all required API connections and environment variables
"""

import os
import asyncio
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def verify_environment_and_apis() -> Dict[str, Any]:
    """Verify environment variables and test all API connections"""
    
    results = {
        "environment_check": {},
        "api_connections": {},
        "overall_status": "PENDING"
    }
    
    logger.info("=== Starting Environment and API Verification ===")
    
    # 1. Check Environment Variables
    logger.info("Checking environment variables...")
    
    required_vars = {
        "NEO4J_URI": os.getenv("NEO4J_URI"),
        "NEO4J_USER": os.getenv("NEO4J_USER", os.getenv("NEO4J_USERNAME", "neo4j")),
        "NEO4J_PASSWORD": os.getenv("NEO4J_PASSWORD"),
        "GEMINI_API_KEY": os.getenv("GEMINI_API_KEY"),
        "VOYAGE_API_KEY": os.getenv("VOYAGE_API_KEY"),
        "PINECONE_API_KEY": os.getenv("PINECONE_API_KEY"),
        "PINECONE_INDEX_NAME": os.getenv("PINECONE_INDEX_NAME"),
        "SUPABASE_URL": os.getenv("SUPABASE_URL"),
        "SUPABASE_SERVICE_ROLE_KEY": os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    }
    
    missing_vars = []
    for var_name, var_value in required_vars.items():
        if not var_value:
            missing_vars.append(var_name)
            results["environment_check"][var_name] = "MISSING"
            logger.error(f"✗ {var_name} is not set")
        else:
            results["environment_check"][var_name] = "SET"
            logger.info(f"✓ {var_name} is set")
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        results["overall_status"] = "FAILED - Missing environment variables"
        return results
    
    # 2. Test API Connections
    logger.info("Testing API connections...")
    
    # Test Neo4j
    try:
        logger.info("Testing Neo4j connection...")
        from neo4j import GraphDatabase
        
        uri = required_vars["NEO4J_URI"]
        username = required_vars["NEO4J_USER"]
        password = required_vars["NEO4J_PASSWORD"]
        
        driver = GraphDatabase.driver(uri, auth=(username, password))
        
        with driver.session() as session:
            result = session.run("RETURN 'Hello Neo4j' as message")
            message = result.single()["message"]
            logger.info(f"✓ Neo4j connection successful: {message}")
            results["api_connections"]["neo4j"] = "SUCCESS"
        
        driver.close()
        
    except Exception as e:
        logger.error(f"✗ Neo4j connection failed: {e}")
        results["api_connections"]["neo4j"] = f"FAILED: {str(e)}"
    
    # Test Gemini API
    try:
        logger.info("Testing Gemini API...")
        import google.generativeai as genai
        genai.configure(api_key=required_vars["GEMINI_API_KEY"])
        
        model = genai.GenerativeModel('gemini-2.0-flash-exp')
        response = model.generate_content("Hello")
        logger.info("✓ Gemini API connection successful")
        results["api_connections"]["gemini"] = "SUCCESS"
        
    except Exception as e:
        logger.error(f"✗ Gemini API connection failed: {e}")
        results["api_connections"]["gemini"] = f"FAILED: {str(e)}"
    
    # Test Voyage AI
    try:
        logger.info("Testing Voyage AI API...")
        import voyageai
        client = voyageai.Client(api_key=required_vars["VOYAGE_API_KEY"])
        
        result = client.embed(["test"], model="voyage-3-large")
        if result.embeddings:
            logger.info("✓ Voyage AI API connection successful")
            results["api_connections"]["voyage"] = "SUCCESS"
        
    except Exception as e:
        logger.error(f"✗ Voyage AI API connection failed: {e}")
        results["api_connections"]["voyage"] = f"FAILED: {str(e)}"
    
    # Test Pinecone
    try:
        logger.info("Testing Pinecone API...")
        from pinecone import Pinecone
        pc = Pinecone(api_key=required_vars["PINECONE_API_KEY"])
        
        indexes = pc.list_indexes()
        logger.info("✓ Pinecone API connection successful")
        results["api_connections"]["pinecone"] = "SUCCESS"
        
    except Exception as e:
        logger.error(f"✗ Pinecone API connection failed: {e}")
        results["api_connections"]["pinecone"] = f"FAILED: {str(e)}"
    
    # Test Supabase
    try:
        logger.info("Testing Supabase API...")
        from supabase import create_client
        supabase = create_client(
            required_vars["SUPABASE_URL"],
            required_vars["SUPABASE_SERVICE_ROLE_KEY"]
        )
        
        # Test simple query
        result = supabase.table("cases").select("id").limit(1).execute()
        logger.info("✓ Supabase API connection successful")
        results["api_connections"]["supabase"] = "SUCCESS"
        
    except Exception as e:
        logger.error(f"✗ Supabase API connection failed: {e}")
        results["api_connections"]["supabase"] = f"FAILED: {str(e)}"
    
    # 3. Test LangExtract
    try:
        logger.info("Testing LangExtract...")
        import langextract
        logger.info("✓ LangExtract import successful")
        results["api_connections"]["langextract"] = "SUCCESS"
        
    except Exception as e:
        logger.error(f"✗ LangExtract test failed: {e}")
        results["api_connections"]["langextract"] = f"FAILED: {str(e)}"
    
    # 4. Test Neo4j GraphRAG
    try:
        logger.info("Testing Neo4j GraphRAG SDK...")
        from neo4j_graphrag.llm import OpenAILLM
        logger.info("✓ Neo4j GraphRAG SDK import successful")
        results["api_connections"]["neo4j_graphrag"] = "SUCCESS"
        
    except Exception as e:
        logger.error(f"✗ Neo4j GraphRAG SDK test failed: {e}")
        results["api_connections"]["neo4j_graphrag"] = f"FAILED: {str(e)}"
    
    # Determine overall status
    all_apis_success = all(
        status == "SUCCESS" for status in results["api_connections"].values()
    )
    
    if all_apis_success:
        results["overall_status"] = "SUCCESS"
        logger.info("🎉 All environment variables and API connections verified successfully!")
    else:
        failed_apis = [
            api for api, status in results["api_connections"].items() 
            if status != "SUCCESS"
        ]
        results["overall_status"] = f"PARTIAL SUCCESS - Failed APIs: {failed_apis}"
        logger.warning(f"Some API connections failed: {failed_apis}")
    
    return results

def print_verification_summary(results: Dict[str, Any]):
    """Print a formatted summary of verification results"""
    
    print("\n" + "="*60)
    print("🔍 ENVIRONMENT & API VERIFICATION SUMMARY")
    print("="*60)
    
    print("\n📋 ENVIRONMENT VARIABLES:")
    for var, status in results["environment_check"].items():
        status_icon = "✓" if status == "SET" else "✗"
        print(f"  {status_icon} {var}: {status}")
    
    print("\n🌐 API CONNECTIONS:")
    for api, status in results["api_connections"].items():
        status_icon = "✓" if status == "SUCCESS" else "✗"
        print(f"  {status_icon} {api}: {status}")
    
    print(f"\n🎯 OVERALL STATUS: {results['overall_status']}")
    
    if results["overall_status"] == "SUCCESS":
        print("\n🚀 READY FOR VALIDATION TESTING!")
        print("Next steps:")
        print("1. Run: python validation_pipeline.py")
        print("2. Launch dashboard: streamlit run monitoring_dashboard.py")
    else:
        print("\n⚠️  ISSUES DETECTED - Please resolve before proceeding")
    
    print("="*60)

async def main():
    """Main verification function"""
    try:
        results = await verify_environment_and_apis()
        print_verification_summary(results)
        return results["overall_status"] == "SUCCESS"
        
    except Exception as e:
        logger.error(f"Verification failed with error: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)