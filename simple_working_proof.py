#!/usr/bin/env python3
"""
Simple Working Proof - Direct System Validation
Proves our core systems work by testing them directly with minimal imports
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Add processing to path
sys.path.insert(0, str(Path(__file__).parent / "courtlistener" / "processing" / "src"))

from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
load_dotenv()

class SimpleWorkingProof:
    """
    Direct proof that our core systems are functional
    """
    
    def __init__(self):
        self.test_id = f"simple_proof_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"🎯 Simple Working Proof: {self.test_id}")
        
    async def test_supabase_direct(self):
        """Test Supabase connection directly"""
        try:
            from supabase import create_client
            
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
            
            if not supabase_url or not supabase_key:
                return {"success": False, "error": "Missing Supabase credentials"}
            
            client = create_client(supabase_url, supabase_key)
            
            # Test query
            result = client.table("cases").select("count", count="exact").limit(1).execute()
            case_count = result.count if hasattr(result, 'count') else 0
            
            return {
                "success": True,
                "case_count": case_count,
                "connection": "working",
                "url": supabase_url[:30] + "..."
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_neo4j_direct(self):
        """Test Neo4j connection directly"""
        try:
            from neo4j import GraphDatabase
            
            uri = os.getenv("NEO4J_URI")
            username = os.getenv("NEO4J_USERNAME", "neo4j")
            password = os.getenv("NEO4J_PASSWORD")
            
            if not uri or not password:
                return {"success": False, "error": "Missing Neo4j credentials"}
                
            driver = GraphDatabase.driver(uri, auth=(username, password))
            
            with driver.session() as session:
                # Test simple query
                result = session.run("MATCH (n) RETURN COUNT(n) as node_count LIMIT 1")
                record = result.single()
                node_count = record["node_count"] if record else 0
                
            driver.close()
            
            return {
                "success": True,
                "node_count": node_count,
                "connection": "working",
                "uri": uri[:30] + "..."
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_pinecone_direct(self):
        """Test Pinecone connection directly"""
        try:
            from pinecone import Pinecone
            
            api_key = os.getenv("PINECONE_API_KEY")
            index_name = os.getenv("PINECONE_INDEX_NAME", "legal-documents")
            
            if not api_key:
                return {"success": False, "error": "Missing Pinecone API key"}
            
            pc = Pinecone(api_key=api_key)
            index = pc.Index(index_name)
            
            # Test index stats
            stats = index.describe_index_stats()
            
            return {
                "success": True,
                "total_vectors": stats.total_vector_count,
                "dimension": stats.dimension,
                "connection": "working",
                "index_name": index_name
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_vertex_ai_direct(self):
        """Test Vertex AI connection directly"""
        try:
            import vertexai
            from vertexai.generative_models import GenerativeModel
            
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                return {"success": False, "error": "Missing Vertex AI credentials"}
            
            # Initialize Vertex AI
            vertexai.init()
            model = GenerativeModel("gemini-2.0-flash-exp")
            
            # Test simple query
            test_prompt = "Extract entities from: 'John Smith vs ABC Corp case filed on 2024-01-01'"
            response = model.generate_content(test_prompt)
            
            return {
                "success": True,
                "model_available": True,
                "response_received": len(response.text) > 0,
                "connection": "working"
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_voyage_ai_direct(self):
        """Test Voyage AI connection directly"""
        try:
            import requests
            
            api_key = os.getenv("VOYAGE_API_KEY")
            if not api_key:
                return {"success": False, "error": "Missing Voyage AI API key"}
            
            # Test embedding generation
            url = "https://api.voyageai.com/v1/embeddings"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            data = {
                "input": ["Personal injury case involving medical malpractice"],
                "model": "voyage-3"
            }
            
            response = requests.post(url, headers=headers, json=data)
            
            if response.status_code == 200:
                result = response.json()
                embedding = result["data"][0]["embedding"]
                
                return {
                    "success": True,
                    "embedding_dimension": len(embedding),
                    "connection": "working",
                    "model": "voyage-3"
                }
            else:
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def run_complete_proof(self):
        """Run complete proof of all systems"""
        
        logger.info("🚀 RUNNING COMPLETE SYSTEM PROOF")
        logger.info("=" * 60)
        
        evidence = {
            "test_id": self.test_id,
            "timestamp": datetime.now().isoformat(),
            "systems_tested": {},
            "overall_success": False,
            "working_systems": 0,
            "total_systems": 5
        }
        
        # Test each system
        systems = [
            ("Supabase", self.test_supabase_direct),
            ("Neo4j", self.test_neo4j_direct), 
            ("Pinecone", self.test_pinecone_direct),
            ("Vertex AI", self.test_vertex_ai_direct),
            ("Voyage AI", self.test_voyage_ai_direct)
        ]
        
        for system_name, test_func in systems:
            logger.info(f"🔍 Testing {system_name}...")
            
            result = await test_func()
            evidence["systems_tested"][system_name.lower()] = result
            
            if result["success"]:
                evidence["working_systems"] += 1
                logger.info(f"✅ {system_name}: Working")
            else:
                logger.error(f"❌ {system_name}: {result['error']}")
        
        # Calculate success
        evidence["success_rate"] = (evidence["working_systems"] / evidence["total_systems"]) * 100
        evidence["overall_success"] = evidence["working_systems"] >= 4  # At least 4/5 systems working
        
        return evidence
    
    def save_proof_report(self, evidence):
        """Save proof report"""
        filename = f"simple_working_proof_{self.test_id}.json"
        
        with open(filename, 'w') as f:
            json.dump(evidence, f, indent=2, default=str)
        
        logger.info(f"📄 Proof report saved: {filename}")
        return filename

async def main():
    """Execute simple working proof"""
    
    print("🎯 SIMPLE WORKING PROOF - SYSTEM VALIDATION")
    print("=" * 60)
    print("Testing core infrastructure components directly")
    print("")
    
    try:
        # Run proof
        proof = SimpleWorkingProof()
        evidence = await proof.run_complete_proof()
        
        # Save report
        report_file = proof.save_proof_report(evidence)
        
        # Display results
        print(f"\n🏆 SYSTEM VALIDATION RESULTS")
        print("=" * 40)
        print(f"Working Systems: {evidence['working_systems']}/{evidence['total_systems']}")
        print(f"Success Rate: {evidence['success_rate']:.1f}%")
        
        # Show system status
        for system, result in evidence["systems_tested"].items():
            status = "✅" if result["success"] else "❌"
            print(f"  {status} {system.title()}: {'Working' if result['success'] else 'Failed'}")
        
        print(f"\n📄 Detailed report: {report_file}")
        
        if evidence["overall_success"]:
            print("\n🎉 SUCCESS: Core infrastructure is FUNCTIONAL!")
            print("   Ready for enhanced GraphRAG processing")
        else:
            print(f"\n⚠️ PARTIAL SUCCESS: Fix failing systems before scale testing")
        
        return evidence["overall_success"]
        
    except Exception as e:
        print(f"❌ System proof failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)