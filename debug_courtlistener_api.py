#!/usr/bin/env python3
"""
Debug CourtListener API to see what we're getting
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project paths
sys.path.append(str(Path(__file__).parent / "courtlistener" / "processing" / "src"))

from processors.court_listener_precise_texas import PreciseCourtListenerProcessor

load_dotenv()

def debug_courtlistener():
    """Debug CourtListener API response"""
    
    processor = PreciseCourtListenerProcessor()
    
    print("🔍 Testing CourtListener API...")
    
    # Get a small sample first
    response = processor.get_all_texas_courts_combined(page_size=5)
    
    print(f"📊 Response keys: {list(response.keys()) if response else 'No response'}")
    
    if response and 'results' in response:
        print(f"📄 Found {len(response['results'])} results")
        
        # Show first result structure
        if response['results']:
            first_result = response['results'][0]
            print(f"📋 First result keys: {list(first_result.keys())}")
            print(f"📝 Case name: {first_result.get('case_name', 'N/A')}")
            print(f"🏛️ Court: {first_result.get('court', 'N/A')}")
            
            # Check for text content
            if 'sub_opinions' in first_result:
                print(f"📖 Sub-opinions: {len(first_result['sub_opinions'])}")
                if first_result['sub_opinions']:
                    opinion = first_result['sub_opinions'][0]
                    print(f"📰 Opinion keys: {list(opinion.keys())}")
                    
                    text_fields = ['plain_text', 'html_lawbox', 'html', 'text']
                    for field in text_fields:
                        content = opinion.get(field, '')
                        if content:
                            print(f"✅ Found {field}: {len(content)} chars")
                        else:
                            print(f"❌ No {field}")
            
            # Check case-level text fields
            text_fields = ['plain_text', 'html_lawbox', 'html', 'snippet']
            for field in text_fields:
                content = first_result.get(field, '')
                if content:
                    print(f"✅ Found case-level {field}: {len(content)} chars")
                else:
                    print(f"❌ No case-level {field}")
    
    else:
        print("❌ No results found")
        if response:
            print(f"📄 Response: {response}")

if __name__ == "__main__":
    debug_courtlistener()