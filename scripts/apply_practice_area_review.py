#!/usr/bin/env python3
"""
Apply human review decisions to practice area classifications.
"""

import argparse
import csv
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logging.basicConfig(level=logging.INFO)
LOG = logging.getLogger(__name__)


def apply_review_decisions(csv_path: str, dry_run: bool = False):
    """Apply human review decisions from CSV file"""

    # Initialize Supabase client
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    supabase: Client = create_client(url, key)
    
    updates_applied = 0
    updates_rejected = 0
    
    with open(csv_path, 'r') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            case_id = row['case_id']
            predicted = row['predicted']
            keep = row['keep'].upper()
            notes = row['notes']
            
            LOG.info(f"Processing case {case_id}: {predicted} -> Keep: {keep}")
            
            if keep == 'Y':
                # Keep the prediction - confirm the practice area
                update_data = {
                    'practice_area_reviewed_at': datetime.utcnow().isoformat(),
                    'review_decision': 'confirm',
                    'review_notes': notes
                }
                updates_applied += 1
                
            elif keep == 'N':
                # Reject the prediction - clear practice area
                update_data = {
                    'primary_practice_area': None,
                    'practice_area_confidence': None,
                    'practice_area_reviewed_at': datetime.utcnow().isoformat(),
                    'review_decision': 'reject',
                    'review_notes': notes
                }
                updates_rejected += 1
                
            else:
                LOG.warning(f"Invalid keep value '{keep}' for case {case_id}, skipping")
                continue
            
            if not dry_run:
                try:
                    result = supabase.table('cases').update(update_data).eq('id', case_id).execute()
                    if result.data:
                        LOG.info(f"Updated case {case_id}: {update_data}")
                    else:
                        LOG.warning(f"No rows updated for case {case_id}")
                except Exception as e:
                    LOG.error(f"Failed to update case {case_id}: {e}")
            else:
                LOG.info(f"DRY RUN - Would update case {case_id}: {update_data}")
    
    LOG.info(f"Review complete: {updates_applied} confirmed, {updates_rejected} rejected")
    return updates_applied, updates_rejected


def main():
    parser = argparse.ArgumentParser(description="Apply practice area review decisions")
    parser.add_argument("--input", required=True, help="CSV file with review decisions")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be updated without making changes")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        LOG.error(f"Input file not found: {args.input}")
        return 1
    
    try:
        apply_review_decisions(args.input, args.dry_run)
        return 0
    except Exception as e:
        LOG.error(f"Failed to apply review decisions: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
