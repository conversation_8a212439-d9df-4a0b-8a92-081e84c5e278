#!/usr/bin/env python3
"""
Backfill vector metadata: author/panel/per_curiam

Finds chunked cases and updates Pinecone vector metadata with author/panel fields.
Does not re-embed or change content - metadata-only updates.
"""

import argparse
import logging
import os
import sys
from pathlib import Path
from typing import List, Optional, Tuple

# Add the courtlistener processing directory to path
processing_dir = Path(__file__).parent.parent / "courtlistener" / "processing" / "src" / "api"
sys.path.insert(0, str(processing_dir))

from dotenv import load_dotenv

# Supabase
from supabase import create_client, Client as SupabaseClient

# Pinecone
from pinecone import Pinecone

LOGGER = logging.getLogger("backfill_author_panel")
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def parse_args():
    ap = argparse.ArgumentParser(description="Backfill vector metadata: author/panel/per_curiam")
    ap.add_argument("--state", required=True, help="e.g., TX")
    ap.add_argument("--namespace", default=None, help="Namespace to target (default: use current worker default).")
    ap.add_argument("--limit", type=int, default=1000, help="Max cases to scan per run.")
    ap.add_argument("--force", action="store_true", help="Rewrite even if author/panel metadata already present.")
    ap.add_argument("--dry-run", action="store_true", help="Log only, no Pinecone updates")
    return ap.parse_args()


def _filter_none(d: dict) -> dict:
    """Remove None values from dictionary but keep empty lists"""
    return {k: v for k, v in d.items() if v is not None}


def fetch_cases_to_backfill(supa: SupabaseClient, state: str, limit: int, force: bool) -> List[dict]:
    """
    Fetch cases that need author/panel metadata backfill
    Strategy:
    - select cases from jurisdiction=state with chunk_status='chunked'
    - if not force: prefer those likely missing judge metadata in Pinecone
      (we approximate by checking that author_person_id IS NOT NULL OR panel_person_ids IS NOT NULL in cases)
    """
    q = (
        supa.table("cases")
        .select("id, jurisdiction, author_person_id, panel_person_ids, per_curiam, court_slug, primary_practice_area, matter_uid, docket_id, docket_number, gcs_path")
        .eq("jurisdiction", state)
        .eq("chunk_status", "chunked")
        .order("id", desc=True)  # Changed to desc to get recent cases first
        .limit(limit)
    )

    if not force:
        # Only process cases that have judge data
        q = q.or_("author_person_id.not.is.null,panel_person_ids.not.is.null,per_curiam.eq.true")
    
    res = q.execute()
    return res.data or []


def fetch_people_names(supa: SupabaseClient, author_person_id: Optional[str], panel_person_ids: Optional[List[str]]) -> Tuple[Optional[str], Optional[List[str]]]:
    """Fetch display names for author and panel person IDs"""
    author_name = None
    panel_names = None
    
    try:
        if author_person_id:
            r = supa.table("people").select("display_name").eq("id", author_person_id).limit(1).execute()
            if r.data:
                author_name = r.data[0].get("display_name")
                
        if panel_person_ids:
            names = []
            # Loop through panel IDs to get names
            for pid in panel_person_ids:
                pr = supa.table("people").select("display_name").eq("id", pid).limit(1).execute()
                if pr.data:
                    names.append(pr.data[0].get("display_name"))
            if names:
                panel_names = names
                
    except Exception as e:
        LOGGER.warning("people lookup failed: %s", e)
        
    return author_name, panel_names


def fetch_case_chunks(supa: SupabaseClient, case_id: str, state: str, limit: int = 10000) -> List[dict]:
    """Fetch chunk records for a case"""
    # Map state codes to jurisdiction names used in chunks table
    jurisdiction_map = {"TX": "Texas"}
    jurisdiction = jurisdiction_map.get(state, state)

    r = (
        supa.table("chunks")
        .select("pinecone_id, seq, namespace")
        .eq("global_uid", str(case_id))
        .eq("jurisdiction", jurisdiction)
        .limit(limit)
        .execute()
    )
    return r.data or []


def update_pinecone_metadata(pc_client, index_name: str, namespace: str, vector_id: str, metadata: dict, dry_run: bool) -> bool:
    """Update Pinecone vector metadata"""
    if dry_run:
        LOGGER.info("DRY-RUN: Would update vector %s in ns=%s with metadata keys: %s",
                   vector_id, namespace, list(metadata.keys()))
        return True

    try:
        index = pc_client.Index(index_name)
        # Use update method for metadata-only updates
        index.update(
            id=vector_id,
            set_metadata=metadata,
            namespace=namespace
        )
        return True
    except Exception as e:
        LOGGER.error("Failed to update vector %s: %s", vector_id, e)
        return False


def main():
    args = parse_args()
    
    # Load environment
    load_dotenv()
    
    # Initialize Supabase client
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    if not supabase_url or not supabase_key:
        LOGGER.error("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
        return 1
    
    supa = create_client(supabase_url, supabase_key)
    
    # Initialize Pinecone client
    pinecone_api_key = os.getenv("PINECONE_API_KEY")
    pinecone_index_name = os.getenv("PINECONE_INDEX_NAME", "legal-docs")
    if not pinecone_api_key:
        LOGGER.error("PINECONE_API_KEY must be set")
        return 1
    
    pc = Pinecone(api_key=pinecone_api_key)
    
    scanned = 0
    updated = 0
    skipped = 0

    cases = fetch_cases_to_backfill(supa, args.state, args.limit, args.force)
    if not cases:
        LOGGER.info("No cases found to backfill.")
        return 0

    LOGGER.info("Found %d cases to process", len(cases))

    for case in cases:
        scanned += 1

        author_pid = case.get("author_person_id")
        panel_pids = case.get("panel_person_ids")
        per_curiam = case.get("per_curiam")

        # Fetch display names
        author_name, panel_names = fetch_people_names(supa, author_pid, panel_pids)

        # If not forcing and nothing to add, skip
        if not args.force and not any([author_pid, panel_pids, per_curiam]):
            LOGGER.debug("Case %s: No judge data to add", case["id"])
            skipped += 1
            continue

        LOGGER.info("Case %s: author_pid=%s, panel_pids=%s, per_curiam=%s",
                   case["id"], author_pid, panel_pids, per_curiam)

        # Get chunks for this case
        chunks = fetch_case_chunks(supa, case["id"], args.state)
        if not chunks:
            LOGGER.debug("No chunks found for case %s", case["id"])
            skipped += 1
            continue

        # Update each chunk's metadata
        case_updated = 0
        for ch in chunks:
            ns = args.namespace or ch.get("namespace")
            if not ns or not ch.get("pinecone_id"):
                continue

            # Build comprehensive metadata update (IDs + names + practice_area + list normalization)
            practice_area = case.get("primary_practice_area")
            md = _filter_none({
                "author_person_id": str(author_pid) if author_pid else None,
                "author_name": author_name,
                "panel_person_ids": [str(pid) for pid in panel_pids] if panel_pids else [],
                "panel_names": panel_names if panel_names else [],
                "per_curiam": per_curiam,
                "practice_area": practice_area,
            })

            LOGGER.info("Case %s chunk %s: metadata=%s", case["id"], ch["pinecone_id"], md)

            if not md:
                LOGGER.debug("Case %s chunk %s: No metadata to update", case["id"], ch["pinecone_id"])
                continue

            # Update Pinecone metadata
            if update_pinecone_metadata(pc, pinecone_index_name, ns, ch["pinecone_id"], md, args.dry_run):
                case_updated += 1

        if case_updated > 0:
            updated += case_updated
            LOGGER.info("Case %s: updated %d chunks with judge metadata", case["id"], case_updated)

    LOGGER.info("Backfill complete. scanned=%d updated=%d skipped=%d", scanned, updated, skipped)
    return 0


if __name__ == "__main__":
    raise SystemExit(main())
