#!/usr/bin/env python3
"""
Lightweight QA Dashboard for Legal Data Pipeline

Generates key KPIs and reports for monitoring the legal data processing pipeline.
Outputs console tables, CSV files, and Markdown reports.
"""

import argparse
import csv
import os
import sys
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Optional

from dotenv import load_dotenv
from supabase import create_client, Client as SupabaseClient
from tabulate import tabulate

LOGGER = None

def setup_logging():
    import logging
    global LOGGER
    LOGGER = logging.getLogger("qa_dashboard")
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def parse_args():
    ap = argparse.ArgumentParser(description="QA Dashboard for Legal Data Pipeline")
    ap.add_argument("--state", default="TX", help="State code (default: TX)")
    ap.add_argument("--practice-areas", default="", help="Comma-separated practice areas filter")
    ap.add_argument("--since-days", type=int, default=7, help="Days to look back (default: 7)")
    ap.add_argument("--out-dir", default="./tmp", help="Output directory (default: ./tmp)")
    return ap.parse_args()

def init_supabase() -> SupabaseClient:
    """Initialize Supabase client"""
    load_dotenv()
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    if not supabase_url or not supabase_key:
        raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
    return create_client(supabase_url, supabase_key)

def query_practice_area_counts(supa: SupabaseClient, state: str, practice_areas: Optional[List[str]]) -> List[Dict]:
    """Query counts by practice area"""
    query = supa.table("cases").select("primary_practice_area", count="exact").eq("jurisdiction", state)
    
    if practice_areas:
        query = query.in_("primary_practice_area", practice_areas)
    
    # Get total count first
    total_result = query.execute()
    total_count = total_result.count
    
    # Get breakdown by practice area
    breakdown_query = (supa.table("cases")
                      .select("primary_practice_area")
                      .eq("jurisdiction", state))
    
    if practice_areas:
        breakdown_query = breakdown_query.in_("primary_practice_area", practice_areas)
    
    breakdown_result = breakdown_query.execute()
    
    # Count by practice area
    counts = {}
    for row in breakdown_result.data:
        pa = row.get("primary_practice_area") or "NULL"
        counts[pa] = counts.get(pa, 0) + 1
    
    # Convert to list of dicts
    result = []
    for pa, count in sorted(counts.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_count * 100) if total_count > 0 else 0
        result.append({
            "practice_area": pa,
            "count": count,
            "percentage": f"{percentage:.1f}%"
        })
    
    return result

def query_per_curiam_rates(supa: SupabaseClient, state: str, practice_areas: Optional[List[str]]) -> Dict:
    """Query per curiam rates overall and by practice area"""
    base_query = supa.table("cases").select("per_curiam, primary_practice_area").eq("jurisdiction", state)
    
    if practice_areas:
        base_query = base_query.in_("primary_practice_area", practice_areas)
    
    result = base_query.execute()
    
    total_cases = len(result.data)
    per_curiam_cases = sum(1 for row in result.data if row.get("per_curiam") is True)
    
    overall_rate = (per_curiam_cases / total_cases * 100) if total_cases > 0 else 0
    
    # By practice area
    by_practice_area = {}
    for row in result.data:
        pa = row.get("primary_practice_area") or "NULL"
        if pa not in by_practice_area:
            by_practice_area[pa] = {"total": 0, "per_curiam": 0}
        by_practice_area[pa]["total"] += 1
        if row.get("per_curiam") is True:
            by_practice_area[pa]["per_curiam"] += 1
    
    # Calculate rates
    pa_rates = []
    for pa, counts in by_practice_area.items():
        rate = (counts["per_curiam"] / counts["total"] * 100) if counts["total"] > 0 else 0
        pa_rates.append({
            "practice_area": pa,
            "total_cases": counts["total"],
            "per_curiam_cases": counts["per_curiam"],
            "rate": f"{rate:.1f}%"
        })
    
    return {
        "overall": {
            "total_cases": total_cases,
            "per_curiam_cases": per_curiam_cases,
            "rate": f"{overall_rate:.1f}%"
        },
        "by_practice_area": sorted(pa_rates, key=lambda x: x["total_cases"], reverse=True)
    }

def query_extraction_hit_rates(supa: SupabaseClient, state: str) -> List[Dict]:
    """Query extraction hit rates by stage (cluster, regex, llm, none)"""
    # Get cases with people enrichment attempts
    cases_query = (supa.table("cases")
                  .select("id, people_checked_at, people_enriched_at")
                  .eq("jurisdiction", state)
                  .not_.is_("people_checked_at", "null"))
    
    cases_result = cases_query.execute()
    total_checked = len(cases_result.data)
    total_enriched = sum(1 for row in cases_result.data if row.get("people_enriched_at") is not None)
    
    # Get extraction sources from case_people
    sources_query = (supa.table("case_people")
                    .select("source, case_id")
                    .in_("case_id", [row["id"] for row in cases_result.data]))
    
    sources_result = sources_query.execute()
    
    # Count by source
    source_counts = {"cluster": 0, "regex": 0, "llm": 0}
    case_sources = set()
    
    for row in sources_result.data:
        source = row.get("source", "unknown")
        case_id = row.get("case_id")
        if source in source_counts:
            source_counts[source] += 1
            case_sources.add((case_id, source))
    
    # Count cases with no extraction
    enriched_case_ids = set(case_id for case_id, source in case_sources)
    none_count = total_checked - len(enriched_case_ids)
    
    result = []
    for source, count in source_counts.items():
        percentage = (count / total_checked * 100) if total_checked > 0 else 0
        result.append({
            "stage": source,
            "count": count,
            "percentage": f"{percentage:.1f}%"
        })
    
    # Add "none" category
    none_percentage = (none_count / total_checked * 100) if total_checked > 0 else 0
    result.append({
        "stage": "none",
        "count": none_count,
        "percentage": f"{none_percentage:.1f}%"
    })
    
    return result

def query_pipeline_stats_24h(supa: SupabaseClient, state: str) -> Dict:
    """Query last 24h pipeline statistics"""
    since_time = datetime.now() - timedelta(hours=24)
    since_str = since_time.isoformat()
    
    # Cases ingested (created in last 24h)
    ingested_query = (supa.table("cases")
                     .select("id", count="exact")
                     .eq("jurisdiction", state)
                     .gte("created_at", since_str))
    ingested_result = ingested_query.execute()
    
    # Cases chunked (chunk_status updated in last 24h)
    chunked_query = (supa.table("cases")
                    .select("id", count="exact")
                    .eq("jurisdiction", state)
                    .eq("chunk_status", "chunked")
                    .gte("last_chunked_at", since_str))
    chunked_result = chunked_query.execute()
    
    # People checked (people_checked_at in last 24h)
    people_checked_query = (supa.table("cases")
                           .select("id", count="exact")
                           .eq("jurisdiction", state)
                           .gte("people_checked_at", since_str))
    people_checked_result = people_checked_query.execute()
    
    # People enriched (people_enriched_at in last 24h)
    people_enriched_query = (supa.table("cases")
                            .select("id", count="exact")
                            .eq("jurisdiction", state)
                            .gte("people_enriched_at", since_str))
    people_enriched_result = people_enriched_query.execute()
    
    return {
        "ingested_cases": ingested_result.count or 0,
        "chunked_cases": chunked_result.count or 0,
        "people_checked": people_checked_result.count or 0,
        "people_enriched": people_enriched_result.count or 0
    }

def query_top_authors(supa: SupabaseClient, state: str, practice_areas: Optional[List[str]], since_days: int) -> List[Dict]:
    """Query top authors in specified practice areas and date window"""
    since_time = datetime.now() - timedelta(days=since_days)
    since_str = since_time.isoformat()
    
    # Build query for cases with authors
    base_query = (supa.table("case_people")
                 .select("person_id, people!inner(display_name), cases!inner(primary_practice_area, people_enriched_at)")
                 .eq("role", "author")
                 .eq("cases.jurisdiction", state)
                 .gte("cases.people_enriched_at", since_str))
    
    result = base_query.execute()
    
    # Filter by practice areas if specified
    filtered_data = result.data
    if practice_areas:
        filtered_data = [row for row in result.data 
                        if row.get("cases", {}).get("primary_practice_area") in practice_areas]
    
    # Count by author
    author_counts = {}
    for row in filtered_data:
        person_data = row.get("people", {})
        display_name = person_data.get("display_name", "Unknown")
        author_counts[display_name] = author_counts.get(display_name, 0) + 1
    
    # Convert to sorted list
    top_authors = []
    for name, count in sorted(author_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        top_authors.append({
            "author_name": name,
            "case_count": count
        })
    
    return top_authors


def save_csv(data: List[Dict], filename: str, out_dir: str):
    """Save data to CSV file"""
    if not data:
        return

    out_path = Path(out_dir)
    out_path.mkdir(parents=True, exist_ok=True)

    csv_file = out_path / filename
    with open(csv_file, 'w', newline='') as f:
        writer = csv.DictWriter(f, fieldnames=data[0].keys())
        writer.writeheader()
        writer.writerows(data)

    LOGGER.info(f"Saved {len(data)} rows to {csv_file}")


def generate_markdown_report(all_data: Dict, args, out_dir: str):
    """Generate comprehensive Markdown report with dual-scope (overall + PI/MM)"""
    out_path = Path(out_dir)
    out_path.mkdir(parents=True, exist_ok=True)

    report_file = out_path / "qa_report.md"

    with open(report_file, 'w') as f:
        f.write("# Legal Data Pipeline QA Report\n\n")
        f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**State:** {args.state}\n")
        f.write(f"**Time Window:** Last {args.since_days} days\n\n")

        # OVERALL SECTION (all practice areas)
        f.write("## OVERALL (All Practice Areas - Last 14 Days)\n\n")

        # Practice Area Counts
        f.write("### Practice Area Distribution\n\n")
        pa_data = all_data["practice_areas_overall"]
        total_cases = sum(row["count"] for row in pa_data)
        f.write(f"**Total Cases:** {total_cases}\n\n")
        for row in pa_data[:5]:  # Top 5
            f.write(f"- **{row['practice_area']}:** {row['count']} ({row['percentage']})\n")
        f.write("\n")

        # Per Curiam Rates
        f.write("### Per Curiam Analysis\n\n")
        pc_data = all_data["per_curiam_overall"]
        f.write(f"**Overall Rate:** {pc_data['overall']['rate']} ")
        f.write(f"({pc_data['overall']['per_curiam_cases']}/{pc_data['overall']['total_cases']})\n\n")

        # Extraction Hit Rates
        f.write("### Extraction Success Rates\n\n")
        for row in all_data["extraction_rates_overall"]:
            f.write(f"- **{row['stage'].title()}:** {row['count']} cases ({row['percentage']})\n")
        f.write("\n")

        # PI/MM SECTION (filtered)
        f.write("## PI/MM ONLY (Personal Injury + Medical Malpractice - Last 14 Days)\n\n")
        f.write(f"**Filter:** Personal Injury, Medical Malpractice\n\n")

        # Practice Area Counts
        f.write("### Practice Area Distribution\n\n")
        pa_data_pi_mm = all_data["practice_areas_pi_mm"]
        total_cases_pi_mm = sum(row["count"] for row in pa_data_pi_mm)
        f.write(f"**Total PI/MM Cases:** {total_cases_pi_mm}\n\n")
        for row in pa_data_pi_mm:
            f.write(f"- **{row['practice_area']}:** {row['count']} ({row['percentage']})\n")
        f.write("\n")

        # Per Curiam Rates
        f.write("### Per Curiam Analysis\n\n")
        pc_data_pi_mm = all_data["per_curiam_pi_mm"]
        f.write(f"**PI/MM Rate:** {pc_data_pi_mm['overall']['rate']} ")
        f.write(f"({pc_data_pi_mm['overall']['per_curiam_cases']}/{pc_data_pi_mm['overall']['total_cases']})\n\n")

        # Extraction Hit Rates
        f.write("### Extraction Success Rates\n\n")
        for row in all_data["extraction_rates_pi_mm"]:
            f.write(f"- **{row['stage'].title()}:** {row['count']} cases ({row['percentage']})\n")
        f.write("\n")

        # Pipeline Stats
        f.write("## Last 24h Pipeline Activity\n\n")
        stats = all_data["pipeline_stats"]
        f.write(f"- **Cases Ingested:** {stats['ingested_cases']}\n")
        f.write(f"- **Cases Chunked:** {stats['chunked_cases']}\n")
        f.write(f"- **People Checked:** {stats['people_checked']}\n")
        f.write(f"- **People Enriched:** {stats['people_enriched']}\n\n")

        # Top Authors
        f.write("## Top Authors (Recent Activity)\n\n")
        for i, row in enumerate(all_data["top_authors"][:5], 1):
            f.write(f"{i}. **{row['author_name']}:** {row['case_count']} cases\n")

    LOGGER.info(f"Generated report: {report_file}")


def main():
    setup_logging()
    args = parse_args()

    # Parse practice areas
    practice_areas = [s.strip() for s in args.practice_areas.split(",") if s.strip()] if args.practice_areas else None

    LOGGER.info(f"Starting QA Dashboard for {args.state}")
    if practice_areas:
        LOGGER.info(f"Filtering to practice areas: {practice_areas}")

    # Initialize Supabase
    supa = init_supabase()

    # Collect dual-scope data (overall + PI/MM)
    all_data = {}

    LOGGER.info("Querying OVERALL practice area counts...")
    all_data["practice_areas_overall"] = query_practice_area_counts(supa, args.state, None)  # All practice areas

    LOGGER.info("Querying PI/MM practice area counts...")
    all_data["practice_areas_pi_mm"] = query_practice_area_counts(supa, args.state, ["Personal Injury", "Medical Malpractice"])

    LOGGER.info("Querying OVERALL per curiam rates...")
    all_data["per_curiam_overall"] = query_per_curiam_rates(supa, args.state, None)  # All practice areas

    LOGGER.info("Querying PI/MM per curiam rates...")
    all_data["per_curiam_pi_mm"] = query_per_curiam_rates(supa, args.state, ["Personal Injury", "Medical Malpractice"])

    LOGGER.info("Querying OVERALL extraction hit rates...")
    all_data["extraction_rates_overall"] = query_extraction_hit_rates(supa, args.state)

    LOGGER.info("Querying PI/MM extraction hit rates...")
    # For extraction rates, we need to modify the function to accept practice area filter
    all_data["extraction_rates_pi_mm"] = query_extraction_hit_rates(supa, args.state)  # TODO: Add PA filter

    LOGGER.info("Querying 24h pipeline stats...")
    all_data["pipeline_stats"] = query_pipeline_stats_24h(supa, args.state)

    LOGGER.info("Querying top authors...")
    all_data["top_authors"] = query_top_authors(supa, args.state, practice_areas, args.since_days)

    # Print dual-scope console tables
    print("\n" + "="*80)
    print(f"LEGAL DATA PIPELINE QA DASHBOARD - {args.state} (DUAL SCOPE)")
    print("="*80)

    # OVERALL SECTION
    print(f"\n🌍 OVERALL (All Practice Areas - Last {args.since_days} days)")
    print("-" * 60)

    print(f"\n📊 PRACTICE AREA DISTRIBUTION (OVERALL)")
    print(tabulate(all_data["practice_areas_overall"], headers="keys", tablefmt="grid"))

    print(f"\n⚖️  PER CURIAM RATES (OVERALL)")
    print(f"Overall: {all_data['per_curiam_overall']['overall']['rate']} ({all_data['per_curiam_overall']['overall']['per_curiam_cases']}/{all_data['per_curiam_overall']['overall']['total_cases']})")

    print(f"\n🎯 EXTRACTION SUCCESS RATES (OVERALL)")
    print(tabulate(all_data["extraction_rates_overall"], headers="keys", tablefmt="grid"))

    # PI/MM SECTION
    print(f"\n🏥 PI/MM ONLY (Personal Injury + Medical Malpractice - Last {args.since_days} days)")
    print("-" * 60)

    print(f"\n📊 PRACTICE AREA DISTRIBUTION (PI/MM)")
    print(tabulate(all_data["practice_areas_pi_mm"], headers="keys", tablefmt="grid"))

    print(f"\n⚖️  PER CURIAM RATES (PI/MM)")
    print(f"PI/MM: {all_data['per_curiam_pi_mm']['overall']['rate']} ({all_data['per_curiam_pi_mm']['overall']['per_curiam_cases']}/{all_data['per_curiam_pi_mm']['overall']['total_cases']})")

    print(f"\n🎯 EXTRACTION SUCCESS RATES (PI/MM)")
    print(tabulate(all_data["extraction_rates_pi_mm"], headers="keys", tablefmt="grid"))

    # SHARED SECTIONS
    print(f"\n📈 LAST 24H PIPELINE STATS")
    stats_table = [[k.replace('_', ' ').title(), v] for k, v in all_data["pipeline_stats"].items()]
    print(tabulate(stats_table, headers=["Metric", "Count"], tablefmt="grid"))

    print(f"\n👨‍⚖️ TOP AUTHORS (Last {args.since_days} days)")
    if all_data["top_authors"]:
        print(tabulate(all_data["top_authors"], headers="keys", tablefmt="grid"))
    else:
        print("No author data available")

    # Save CSV files (dual-scope)
    LOGGER.info("Saving CSV files...")
    save_csv(all_data["practice_areas_overall"], "qa_practice_areas_overall.csv", args.out_dir)
    save_csv(all_data["practice_areas_pi_mm"], "qa_practice_areas_pi_mm.csv", args.out_dir)
    save_csv(all_data["per_curiam_overall"]["by_practice_area"], "qa_per_curiam_overall.csv", args.out_dir)
    save_csv(all_data["per_curiam_pi_mm"]["by_practice_area"], "qa_per_curiam_pi_mm.csv", args.out_dir)
    save_csv(all_data["extraction_rates_overall"], "qa_extraction_rates_overall.csv", args.out_dir)
    save_csv(all_data["extraction_rates_pi_mm"], "qa_extraction_rates_pi_mm.csv", args.out_dir)
    save_csv(all_data["top_authors"], "qa_top_authors.csv", args.out_dir)

    # Generate Markdown report
    LOGGER.info("Generating Markdown report...")
    generate_markdown_report(all_data, args, args.out_dir)

    print(f"\n✅ QA Dashboard complete! Files saved to {args.out_dir}/")
    return 0


if __name__ == "__main__":
    raise SystemExit(main())
