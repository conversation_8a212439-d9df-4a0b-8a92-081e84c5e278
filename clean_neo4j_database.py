#!/usr/bin/env python3
"""
Neo4j Database Cleanup Utility
Clean GraphRAG entities to ensure isolated test runs and accurate metrics
"""

import asyncio
import os
import logging
from datetime import datetime
from dotenv import load_dotenv
from neo4j import GraphDatabase

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Neo4jCleaner:
    """Utility class for cleaning Neo4j database of GraphRAG entities"""
    
    def __init__(self, neo4j_uri: str, neo4j_user: str, neo4j_password: str):
        self.driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
    
    def get_database_stats(self) -> dict:
        """Get current database statistics"""
        with self.driver.session() as session:
            # Count all GraphRAG nodes
            node_result = session.run("""
                MATCH (n:__KGBuilder__)
                RETURN count(n) as total_nodes,
                       count(DISTINCT n.name) as unique_names,
                       count(CASE WHEN n:Chunk THEN 1 END) as chunk_nodes,
                       count(CASE WHEN NOT n:Chunk THEN 1 END) as entity_nodes
            """)
            node_stats = node_result.single()
            
            # Count relationships
            rel_result = session.run("""
                MATCH (start:__KGBuilder__)-[r]->(end:__KGBuilder__)
                RETURN count(r) as total_relationships,
                       count(CASE WHEN type(r) IN ['FROM_CHUNK', 'NEXT_CHUNK'] THEN 1 END) as chunk_rels,
                       count(CASE WHEN NOT type(r) IN ['FROM_CHUNK', 'NEXT_CHUNK'] THEN 1 END) as entity_rels
            """)
            rel_stats = rel_result.single()
            
            # Get node type breakdown
            type_result = session.run("""
                MATCH (n:__KGBuilder__)
                WHERE NOT n:Chunk
                UNWIND labels(n) as label
                WITH label, count(*) as count
                WHERE label <> '__KGBuilder__'
                RETURN collect({type: label, count: count}) as node_types
            """)
            type_record = type_result.single()
            node_types = type_record["node_types"] if type_record else []
            
            return {
                "total_nodes": node_stats["total_nodes"],
                "unique_names": node_stats["unique_names"],
                "chunk_nodes": node_stats["chunk_nodes"],
                "entity_nodes": node_stats["entity_nodes"],
                "total_relationships": rel_stats["total_relationships"],
                "chunk_relationships": rel_stats["chunk_rels"],
                "entity_relationships": rel_stats["entity_rels"],
                "node_types": node_types
            }
    
    def clean_all_graphrag_data(self, confirm: bool = False) -> dict:
        """Clean all GraphRAG nodes and relationships"""
        if not confirm:
            raise ValueError("Must explicitly confirm database cleanup with confirm=True")
        
        logger.info("🧹 Starting complete GraphRAG database cleanup...")
        
        # Get stats before cleanup
        before_stats = self.get_database_stats()
        logger.info(f"Before cleanup: {before_stats['total_nodes']} nodes, {before_stats['total_relationships']} relationships")
        
        with self.driver.session() as session:
            # Delete all GraphRAG relationships first
            rel_result = session.run("""
                MATCH (start:__KGBuilder__)-[r]->(end:__KGBuilder__)
                DELETE r
                RETURN count(r) as deleted_relationships
            """)
            deleted_rels = rel_result.single()["deleted_relationships"]
            
            # Delete all GraphRAG nodes
            node_result = session.run("""
                MATCH (n:__KGBuilder__)
                DELETE n
                RETURN count(n) as deleted_nodes
            """)
            deleted_nodes = node_result.single()["deleted_nodes"]
            
            # Clean up any orphaned indexes
            session.run("""
                DROP INDEX __entity__tmp_internal_id IF EXISTS
            """)
        
        # Get stats after cleanup
        after_stats = self.get_database_stats()
        
        logger.info(f"✅ Cleanup complete: Deleted {deleted_nodes} nodes and {deleted_rels} relationships")
        logger.info(f"After cleanup: {after_stats['total_nodes']} nodes remain")
        
        return {
            "deleted_nodes": deleted_nodes,
            "deleted_relationships": deleted_rels,
            "before_stats": before_stats,
            "after_stats": after_stats
        }
    
    def clean_by_run_id(self, run_id: str) -> dict:
        """Clean entities created in a specific run"""
        logger.info(f"🧹 Cleaning entities from run: {run_id}")
        
        with self.driver.session() as session:
            # Delete relationships for this run
            rel_result = session.run("""
                MATCH (start:__KGBuilder__)-[r]->(end:__KGBuilder__)
                WHERE start.run_id = $run_id OR end.run_id = $run_id
                DELETE r
                RETURN count(r) as deleted_relationships
            """, run_id=run_id)
            deleted_rels = rel_result.single()["deleted_relationships"]
            
            # Delete nodes for this run
            node_result = session.run("""
                MATCH (n:__KGBuilder__)
                WHERE n.run_id = $run_id
                DELETE n
                RETURN count(n) as deleted_nodes
            """, run_id=run_id)
            deleted_nodes = node_result.single()["deleted_nodes"]
        
        logger.info(f"✅ Cleaned run {run_id}: {deleted_nodes} nodes, {deleted_rels} relationships")
        
        return {
            "run_id": run_id,
            "deleted_nodes": deleted_nodes,
            "deleted_relationships": deleted_rels
        }
    
    def get_sample_entities(self, limit: int = 10) -> list:
        """Get sample entities for inspection"""
        with self.driver.session() as session:
            result = session.run("""
                MATCH (n:__KGBuilder__)
                WHERE n.name IS NOT NULL AND NOT n:Chunk
                RETURN n.name as name, 
                       [label IN labels(n) WHERE label <> '__KGBuilder__'][0] as type,
                       n.run_id as run_id,
                       n.__tmp_internal_id as internal_id
                ORDER BY n.__tmp_internal_id DESC
                LIMIT $limit
            """, limit=limit)
            
            entities = []
            for record in result:
                entities.append({
                    "name": record["name"],
                    "type": record["type"],
                    "run_id": record["run_id"],
                    "internal_id": record["internal_id"]
                })
            
            return entities
    
    def close(self):
        """Close database connection"""
        if self.driver:
            self.driver.close()

async def main():
    """Main cleanup utility function"""
    logger.info("🧹 NEO4J GRAPHRAG DATABASE CLEANUP UTILITY")
    logger.info("=" * 60)
    
    try:
        # Initialize cleaner
        cleaner = Neo4jCleaner(
            neo4j_uri=os.getenv("NEO4J_URI"),
            neo4j_user=os.getenv("NEO4J_USERNAME", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD")
        )
        
        # Get current database status
        logger.info("📊 Current database status:")
        stats = cleaner.get_database_stats()
        logger.info(f"   Total GraphRAG nodes: {stats['total_nodes']}")
        logger.info(f"   Entity nodes: {stats['entity_nodes']}")
        logger.info(f"   Chunk nodes: {stats['chunk_nodes']}")
        logger.info(f"   Total relationships: {stats['total_relationships']}")
        logger.info(f"   Entity relationships: {stats['entity_relationships']}")
        logger.info("")
        
        # Show sample entities
        logger.info("🔍 Sample entities in database:")
        sample_entities = cleaner.get_sample_entities(5)
        if sample_entities:
            for entity in sample_entities:
                logger.info(f"   {entity['type']}: {entity['name']} (Run: {entity['run_id']})")
        else:
            logger.info("   No entities found")
        logger.info("")
        
        # Show entity type breakdown
        if stats['node_types']:
            logger.info("📋 Entity types breakdown:")
            for node_type in stats['node_types'][:5]:
                logger.info(f"   {node_type['type']}: {node_type['count']} entities")
            logger.info("")
        
        # Clean database if there are entities
        if stats['total_nodes'] > 0:
            logger.info("⚠️  Database contains GraphRAG entities from previous runs")
            logger.info("   This will cause contaminated entity counting in tests")
            logger.info("   Run with cleanup=True to clean database before testing")
        else:
            logger.info("✅ Database is clean - ready for isolated testing")
        
        cleaner.close()
        
        return {
            "database_status": "clean" if stats['total_nodes'] == 0 else "contaminated",
            "total_nodes": stats['total_nodes'],
            "entity_nodes": stats['entity_nodes'],
            "requires_cleanup": stats['total_nodes'] > 0
        }
        
    except Exception as e:
        logger.error(f"❌ Database cleanup utility failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--clean":
        # Perform actual cleanup
        async def cleanup():
            cleaner = Neo4jCleaner(
                neo4j_uri=os.getenv("NEO4J_URI"),
                neo4j_user=os.getenv("NEO4J_USERNAME", "neo4j"),
                neo4j_password=os.getenv("NEO4J_PASSWORD")
            )
            
            result = cleaner.clean_all_graphrag_data(confirm=True)
            cleaner.close()
            return result
        
        result = asyncio.run(cleanup())
        if result:
            print(f"\n✅ DATABASE CLEANED SUCCESSFULLY")
            print(f"   Deleted {result['deleted_nodes']} nodes and {result['deleted_relationships']} relationships")
    else:
        # Just show status
        result = asyncio.run(main())
        if result and result.get("requires_cleanup"):
            print(f"\n⚠️  DATABASE NEEDS CLEANING")
            print(f"   Run: python clean_neo4j_database.py --clean")
        elif result:
            print(f"\n✅ DATABASE IS READY FOR TESTING")