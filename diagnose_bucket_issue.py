#!/usr/bin/env python3
"""
Diagnose GCS Bucket Issue and Provide Fix Instructions
Specifically for texas-laws-personalinjury bucket permission issues
"""

import os
from dotenv import load_dotenv

load_dotenv()

def diagnose_gcs_permission_issue():
    print("=== GCS Permission Issue Diagnosis ===\n")
    
    # Get current configuration
    current_bucket = os.getenv("GCS_BUCKET_NAME")
    service_account_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    
    print(f"📋 Current Configuration:")
    print(f"   GCS_BUCKET_NAME: {current_bucket}")
    print(f"   Service Account: {service_account_path}")
    
    # Identify the issue
    print(f"\n🔍 Issue Analysis:")
    print(f"   ✅ Expected bucket: texas-laws-personalinjury (where full Texas docs are)")
    print(f"   ❌ Permission error: ailex-gemini-sa lacks storage.objects.list")
    print(f"   ⚠️  Current fallback: {current_bucket} (only has test files)")
    
    # Service account details from the error
    service_account_email = "<EMAIL>"
    
    print(f"\n🔧 REQUIRED FIXES:")
    print(f"   1. Grant permissions to service account: {service_account_email}")
    print(f"   2. Target bucket: texas-laws-personalinjury")
    print(f"   3. Required IAM role: Storage Object Viewer (or Storage Admin)")
    
    print(f"\n💡 HOW TO FIX (choose one method):")
    
    print(f"\n📱 METHOD 1: Google Cloud Console")
    print(f"   1. Go to: https://console.cloud.google.com/storage/browser")
    print(f"   2. Find bucket: texas-laws-personalinjury")
    print(f"   3. Click 'Permissions' tab")
    print(f"   4. Click '+ Grant Access'")
    print(f"   5. Add principal: {service_account_email}")
    print(f"   6. Select role: 'Storage Object Viewer' or 'Storage Admin'")
    print(f"   7. Click 'Save'")
    
    print(f"\n⚡ METHOD 2: Command Line (if you have gcloud access)")
    print(f"   gsutil iam ch serviceAccount:{service_account_email}:objectViewer gs://texas-laws-personalinjury")
    
    print(f"\n📊 METHOD 3: Check if bucket exists first")
    print(f"   gsutil ls gs://texas-laws-personalinjury")
    print(f"   (If this fails, the bucket might not exist)")
    
    print(f"\n🎯 AFTER FIXING PERMISSIONS:")
    print(f"   1. Update .env: GCS_BUCKET_NAME=texas-laws-personalinjury")
    print(f"   2. Test pipeline with: python investigate_gcs_buckets.py")
    print(f"   3. Run pipeline: python production_pipeline_coordinator.py")
    
    print(f"\n❓ ALTERNATIVE SCENARIOS:")
    print(f"   • If texas-laws-personalinjury doesn't exist: Create it and upload docs")
    print(f"   • If docs are in different bucket: Update client to point there")
    print(f"   • If using wrong project: Check PROJECT_ID environment variable")
    
    # Current project check
    project_id = os.getenv("PROJECT_ID")
    vertex_project = os.getenv("VERTEX_PROJECT_ID")
    
    print(f"\n📋 Project Configuration Check:")
    print(f"   PROJECT_ID: {project_id}")
    print(f"   VERTEX_PROJECT_ID: {vertex_project}")
    print(f"   Expected bucket project: Should match one of these")
    
if __name__ == "__main__":
    diagnose_gcs_permission_issue()