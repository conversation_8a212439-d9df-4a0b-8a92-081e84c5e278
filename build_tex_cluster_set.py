#!/usr/bin/env python3
"""
Build Texas Cluster Set Pickle
Creates a pickle file with all 680K Texas cluster IDs using the 3-stage filtering approach.
"""

import bz2
import csv
import pickle
import time
from typing import Set

# Set CSV field size limit for large legal documents
csv.field_size_limit(10**9)

def load_texas_courts(courts_file: str) -> Set[str]:
    """Load Texas court IDs from courts CSV."""
    print(f"🏛️ Loading Texas courts from {courts_file}")
    texas_court_ids = set()
    
    try:
        with bz2.open(courts_file, 'rt', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                court_id = row.get('id', '')
                full_name = row.get('full_name', '')
                jurisdiction = row.get('jurisdiction', '')
                
                # Texas court identification
                if (court_id.startswith('tex') or court_id.startswith('tx') or 
                    'texas' in full_name.lower() or jurisdiction == 'TX'):
                    texas_court_ids.add(court_id)
        
        print(f"✅ Loaded {len(texas_court_ids)} Texas courts")
        return texas_court_ids
        
    except Exception as e:
        print(f"❌ Error loading Texas courts: {e}")
        return set()

def load_texas_dockets(dockets_file: str, texas_court_ids: Set[str]) -> Set[str]:
    """Load Texas docket IDs from dockets CSV using court filtering."""
    print(f"📋 Loading Texas dockets from {dockets_file}")
    texas_docket_ids = set()
    
    try:
        with bz2.open(dockets_file, 'rt', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                court_id = row.get('court_id', '')
                docket_id = row.get('id', '')
                
                if court_id in texas_court_ids and docket_id:
                    texas_docket_ids.add(docket_id)
        
        print(f"✅ Loaded {len(texas_docket_ids)} Texas dockets")
        return texas_docket_ids
        
    except Exception as e:
        print(f"❌ Error loading Texas dockets: {e}")
        return set()

def load_texas_clusters(clusters_file: str, texas_docket_ids: Set[str]) -> Set[int]:
    """Load Texas cluster IDs from clusters CSV using docket filtering."""
    print(f"🗂️ Loading Texas clusters from {clusters_file}")
    texas_cluster_ids = set()
    
    try:
        with bz2.open(clusters_file, 'rt', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                docket_id = row.get('docket_id', '')
                cluster_id = row.get('id', '')
                
                # Filter by Texas dockets and convert to int
                if docket_id in texas_docket_ids and cluster_id and cluster_id.isdigit():
                    texas_cluster_ids.add(int(cluster_id))
        
        print(f"✅ Loaded {len(texas_cluster_ids)} Texas clusters")
        return texas_cluster_ids
        
    except Exception as e:
        print(f"❌ Error loading Texas clusters: {e}")
        return set()

def save_pickle(cluster_set: Set[int], output_file: str) -> bool:
    """Save Texas cluster set to pickle file."""
    print(f"💾 Saving Texas cluster set to {output_file}")
    
    try:
        with open(output_file, 'wb') as f:
            pickle.dump(cluster_set, f)
        
        print(f"✅ Saved {len(cluster_set)} cluster IDs to {output_file}")
        
        # Verify the pickle file
        with open(output_file, 'rb') as f:
            loaded_set = pickle.load(f)
        
        print(f"✅ Verified: Loaded {len(loaded_set)} cluster IDs from pickle")
        print(f"📊 Sample cluster IDs: {list(loaded_set)[:10]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error saving pickle: {e}")
        return False

def main():
    """Main function to build Texas cluster set pickle."""
    print("🚧 Building Texas Cluster Set Pickle")
    print("=" * 50)
    
    # File paths
    courts_file = "bulk_csv/courts-2025-07-02.csv.bz2"
    dockets_file = "bulk_csv/dockets-2025-07-02.csv.bz2"
    clusters_file = "bulk_csv/opinion-clusters-2025-07-02.csv.bz2"
    output_file = "tex_clusters.p"
    
    start_time = time.time()
    
    # Step 1: Load Texas courts
    texas_courts = load_texas_courts(courts_file)
    if not texas_courts:
        print("❌ Failed to load Texas courts")
        return 1
    
    # Step 2: Load Texas dockets
    texas_dockets = load_texas_dockets(dockets_file, texas_courts)
    if not texas_dockets:
        print("❌ Failed to load Texas dockets")
        return 1
    
    # Step 3: Load Texas clusters
    texas_clusters = load_texas_clusters(clusters_file, texas_dockets)
    if not texas_clusters:
        print("❌ Failed to load Texas clusters")
        return 1
    
    # Step 4: Save to pickle
    if not save_pickle(texas_clusters, output_file):
        print("❌ Failed to save pickle file")
        return 1
    
    elapsed = time.time() - start_time
    
    print(f"\n🎉 SUCCESS!")
    print(f"   Texas courts: {len(texas_courts):,}")
    print(f"   Texas dockets: {len(texas_dockets):,}")
    print(f"   Texas clusters: {len(texas_clusters):,}")
    print(f"   Processing time: {elapsed:.1f}s")
    print(f"   Output file: {output_file}")
    print(f"\n🎯 Next step: Update s3_bulk_loader.py to use {output_file}")
    
    return 0

if __name__ == "__main__":
    exit(main())
