#!/usr/bin/env python3
"""
Parallel Classification Worker
High-speed parallel Gemini classification of stored cases.
"""

import os
import time
import threading
import queue
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import google.generativeai as genai
from supabase import create_client, Client
from google.cloud import storage
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class ClassificationResult:
    case_id: str
    primary_practice_area: str
    confidence: float
    processing_time: float
    worker_id: int

class GeminiClassificationWorker:
    """Individual worker for Gemini classification with rate limiting and API key rotation."""
    
    def __init__(self, worker_id: int, max_requests_per_minute: int = 200):
        self.worker_id = worker_id
        self.max_requests_per_minute = max_requests_per_minute
        self.requests_made = 0
        self.window_start = time.time()
        
        # Load all available Gemini API keys
        self.api_keys = []
        for i in range(1, 6):  # GEMINI_API_KEY, GEMINI_API_KEY_2, ..., GEMINI_API_KEY_5
            key_name = "GEMINI_API_KEY" if i == 1 else f"GEMINI_API_KEY_{i}"
            api_key = os.getenv(key_name)
            if api_key:
                self.api_keys.append(api_key)
        
        if not self.api_keys:
            raise ValueError("No GEMINI_API_KEY environment variables found")
        
        # Assign API key to worker (round-robin)
        self.api_key = self.api_keys[worker_id % len(self.api_keys)]
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
        
        logger.info(f"Worker {worker_id} initialized with API key #{(worker_id % len(self.api_keys)) + 1}, {max_requests_per_minute} req/min limit")
    
    def _rate_limit(self):
        """Implement rate limiting."""
        current_time = time.time()
        elapsed = current_time - self.window_start
        
        # Reset window if more than 60 seconds have passed
        if elapsed >= 60:
            self.requests_made = 0
            self.window_start = current_time
            elapsed = 0
        
        # If we've hit the rate limit, wait
        if self.requests_made >= self.max_requests_per_minute:
            wait_time = 60 - elapsed + 1
            logger.debug(f"Worker {self.worker_id} rate limit reached, waiting {wait_time:.1f}s")
            time.sleep(wait_time)
            self.requests_made = 0
            self.window_start = time.time()
    
    def classify_case(self, case_id: str, case_text: str) -> ClassificationResult:
        """Classify a single case using Gemini with the same prompt as serial classification."""
        start_time = time.time()
        
        try:
            # Apply rate limiting
            self._rate_limit()
            
            # Use same truncation as serial classifier
            text_sample = case_text[:1500] if len(case_text) > 1500 else case_text
            
            # Use exact same prompt as enhanced_bulk_loader.py
            prompt = f"""Analyze this legal case text and classify its primary practice area.

Text: "{text_sample}"

Respond with ONLY a JSON object in this exact format:
{{
    "primary_practice_area": "Medical Malpractice" | "Personal Injury" | "Other",
    "practice_areas": ["array of relevant practice areas"],
    "confidence": 0.0-1.0
}}

Focus on:
- Medical Malpractice: medical negligence, doctor/hospital liability, standard of care
- Personal Injury: accidents, negligence, tort liability, damages, wrongful death
- Other: all other legal areas

Be precise and confident."""
            
            response = self.model.generate_content(prompt)
            self.requests_made += 1
            
            # Parse response with same logic as serial classifier
            response_text = response.text.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]
            
            import json
            result = json.loads(response_text)
            
            processing_time = time.time() - start_time
            
            return ClassificationResult(
                case_id=case_id,
                primary_practice_area=result.get("primary_practice_area", "Other"),
                confidence=float(result.get("confidence", 0.5)),
                processing_time=processing_time,
                worker_id=self.worker_id
            )
            
        except Exception as e:
            logger.warning(f"Worker {self.worker_id} failed to classify case {case_id}: {e}")
            processing_time = time.time() - start_time
            
            return ClassificationResult(
                case_id=case_id,
                primary_practice_area="Other",
                confidence=0.3,
                processing_time=processing_time,
                worker_id=self.worker_id
            )

class ParallelClassificationProcessor:
    """Production parallel classification processor."""
    
    def __init__(self, num_workers: int = 5):
        self.num_workers = num_workers
        
        # Initialize Supabase
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        if not supabase_url or not supabase_key:
            raise ValueError("Supabase environment variables not set")
        
        self.supabase: Client = create_client(supabase_url, supabase_key)
        
        # Initialize GCS
        self.gcs_client = storage.Client()
        
        logger.info(f"Initialized processor with {num_workers} workers")
    
    def get_unclassified_cases(self, limit: int = 10000, jurisdiction: str = 'TX') -> List[Dict]:
        """Get unclassified cases for processing."""
        logger.info(f"Fetching {limit} unclassified {jurisdiction} cases...")

        response = self.supabase.table('cases').select(
            'id, gcs_path, case_name'
        ).eq('jurisdiction', jurisdiction).is_('primary_practice_area', 'null').limit(limit).execute()

        cases = response.data
        logger.info(f"Found {len(cases)} unclassified cases")
        return cases

    def get_low_confidence_cases(self, limit: int = 1000, jurisdiction: str = 'TX', max_confidence: float = 0.6) -> List[Dict]:
        """Get low confidence cases for re-classification."""
        logger.info(f"Fetching {limit} low confidence {jurisdiction} cases (confidence < {max_confidence})...")

        response = self.supabase.table('cases').select(
            'id, gcs_path, case_name, primary_practice_area, practice_area_confidence'
        ).eq('jurisdiction', jurisdiction).lt('practice_area_confidence', max_confidence).limit(limit).execute()

        cases = response.data
        logger.info(f"Found {len(cases)} low confidence cases for re-classification")
        return cases
    
    def get_case_text(self, gcs_path: str) -> str:
        """Get case text from GCS."""
        try:
            # Parse GCS path: gs://bucket/path/to/file
            if not gcs_path.startswith('gs://'):
                return "Error: Invalid GCS path format"
            
            path_parts = gcs_path[5:].split('/', 1)  # Remove 'gs://' and split
            if len(path_parts) != 2:
                return "Error: Invalid GCS path format"
            
            bucket_name, blob_name = path_parts
            bucket = self.gcs_client.bucket(bucket_name)
            blob = bucket.blob(blob_name)
            
            # Download text content
            if blob.exists():
                content = blob.download_as_text(encoding='utf-8')
                return content[:3000]  # Limit to first 3000 chars for classification
            else:
                return "Error: File not found in GCS"
                
        except Exception as e:
            logger.warning(f"Failed to fetch GCS content from {gcs_path}: {e}")
            return f"Error fetching content: {str(e)}"
    
    def update_classification(self, result: ClassificationResult) -> bool:
        """Update case classification in Supabase."""
        try:
            self.supabase.table('cases').update({
                'primary_practice_area': result.primary_practice_area,
                'practice_area_confidence': result.confidence,
                'practice_area_checked_at': 'now()'
            }).eq('id', result.case_id).execute()
            return True
        except Exception as e:
            logger.error(f"Failed to update case {result.case_id}: {e}")
            return False
    
    def process_batch(self, cases: List[Dict]) -> Dict:
        """Process a batch of cases with parallel workers."""
        logger.info(f"Starting parallel classification of {len(cases)} cases with {self.num_workers} workers")
        
        start_time = time.time()
        results = []
        successful_updates = 0
        
        # Create work queue
        work_queue = queue.Queue()
        for case in cases:
            work_queue.put(case)
        
        def worker_task(worker_id: int):
            worker = GeminiClassificationWorker(worker_id)
            worker_results = []
            
            while True:
                try:
                    case = work_queue.get_nowait()
                except queue.Empty:
                    break
                
                # Get case text from GCS
                case_text = self.get_case_text(case.get('gcs_path', ''))
                
                # Classify case
                result = worker.classify_case(case['id'], case_text)
                worker_results.append(result)
                
                # Update database
                if self.update_classification(result):
                    nonlocal successful_updates
                    successful_updates += 1
                
                work_queue.task_done()
            
            return worker_results
        
        # Run workers in parallel
        with ThreadPoolExecutor(max_workers=self.num_workers) as executor:
            futures = [executor.submit(worker_task, i) for i in range(self.num_workers)]
            
            for future in as_completed(futures):
                worker_results = future.result()
                results.extend(worker_results)
        
        total_time = time.time() - start_time
        
        # Calculate metrics
        total_cases = len(results)
        cases_per_hour = (total_cases / total_time) * 3600 if total_time > 0 else 0
        avg_processing_time = sum(r.processing_time for r in results) / total_cases if results else 0
        
        # Practice area distribution
        area_counts = {}
        for result in results:
            area = result.primary_practice_area
            area_counts[area] = area_counts.get(area, 0) + 1
        
        return {
            'total_cases': total_cases,
            'total_time': total_time,
            'cases_per_hour': cases_per_hour,
            'successful_updates': successful_updates,
            'avg_processing_time': avg_processing_time,
            'area_distribution': area_counts,
            'num_workers': self.num_workers
        }

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Parallel Classification Worker")
    parser.add_argument("--workers", type=int, default=5, help="Number of parallel workers")
    parser.add_argument("--batch-size", type=int, default=1000, help="Batch size for processing")
    parser.add_argument("--jurisdiction", default="TX", help="Jurisdiction to process")
    parser.add_argument("--limit", type=int, help="Maximum cases to process (default: all unclassified)")
    parser.add_argument("--reclassify-low-confidence", action="store_true", help="Re-classify low confidence cases instead of unclassified")
    parser.add_argument("--max-confidence", type=float, default=0.6, help="Maximum confidence for re-classification (default: 0.6)")
    
    args = parser.parse_args()
    
    load_dotenv()
    
    try:
        processor = ParallelClassificationProcessor(num_workers=args.workers)
        
        # Get cases to process
        if args.reclassify_low_confidence:
            cases = processor.get_low_confidence_cases(
                limit=args.limit or 5000,  # Default to smaller batch for re-classification
                jurisdiction=args.jurisdiction,
                max_confidence=args.max_confidence
            )
        else:
            cases = processor.get_unclassified_cases(
                limit=args.limit or 50000,  # Default to large batch
                jurisdiction=args.jurisdiction
            )
        
        if not cases:
            logger.info("No unclassified cases found")
            return
        
        # Process in batches
        batch_size = args.batch_size
        total_processed = 0
        
        for i in range(0, len(cases), batch_size):
            batch = cases[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}: {len(batch)} cases")
            
            results = processor.process_batch(batch)
            total_processed += results['total_cases']
            
            # Print batch results
            print(f"\n📊 BATCH {i//batch_size + 1} RESULTS:")
            print(f"Cases processed: {results['total_cases']}")
            print(f"Time: {results['total_time']:.1f}s")
            print(f"Speed: {results['cases_per_hour']:.0f} cases/hour")
            print(f"Successful updates: {results['successful_updates']}")
            
            # Practice area distribution
            for area, count in results['area_distribution'].items():
                percentage = (count / results['total_cases']) * 100
                print(f"  {area}: {count} ({percentage:.1f}%)")
        
        print(f"\n🎉 PARALLEL CLASSIFICATION COMPLETE!")
        print(f"Total cases processed: {total_processed}")
        
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        raise

if __name__ == "__main__":
    main()
