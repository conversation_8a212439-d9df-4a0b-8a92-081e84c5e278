#!/usr/bin/env python3
"""
Debug SimpleKGPipeline Integration Issue
Compare SimpleKGPipeline vs direct LLMEntityRelationExtractor to identify integration problems
"""

import asyncio
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_simple_kg_pipeline():
    """Debug SimpleKGPipeline vs direct LLMEntityRelationExtractor"""
    logger.info("🔧 DEBUGGING SIMPLEKG PIPELINE INTEGRATION")
    logger.info("=" * 60)
    
    try:
        import vertexai
        from vertexai.generative_models import GenerationConfig
        from neo4j_graphrag.llm import VertexAILLM
        from neo4j_graphrag.experimental.components.entity_relation_extractor import (
            LLMEntityRelationExtractor,
            OnError
        )
        from neo4j_graphrag.experimental.components.schema import GraphSchema, NodeType, RelationshipType
        from neo4j_graphrag.experimental.components.types import TextChunk, TextChunks
        from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
        from neo4j_graphrag.experimental.components.text_splitters.langchain import LangChainTextSplitterAdapter
        from langchain_text_splitters import RecursiveCharacterTextSplitter
        from neo4j import GraphDatabase
        
        # Initialize Vertex AI
        vertex_project_id = os.getenv("VERTEX_PROJECT_ID")
        vertex_location = os.getenv("VERTEX_LOCATION", "us-central1")
        
        if vertex_project_id:
            vertexai.init(project=vertex_project_id, location=vertex_location)
            logger.info(f"Initialized Vertex AI with project: {vertex_project_id}")
        
        # Test text - same as in debug_llm_response.py
        test_text = """
        In Anderson v. Memorial Hermann Hospital, plaintiff Sarah Anderson sued Memorial Hermann 
        Hospital for medical malpractice. Judge Robert Wilson presided over the case.
        Attorney Jennifer Martinez represented plaintiff Anderson, while 
        Attorney Michael Davis represented Memorial Hermann Hospital.
        The jury awarded plaintiff $150,000 in damages.
        """
        
        # Create schema
        node_types = [
            NodeType(label="Person", description="People involved in legal cases"),
            NodeType(label="Organization", description="Organizations and institutions"),
            NodeType(label="Case", description="Legal cases and proceedings"),
        ]
        
        relationship_types = [
            RelationshipType(label="REPRESENTED", description="Attorney represented party"),
            RelationshipType(label="SUED", description="Party sued another party"),
            RelationshipType(label="PRESIDED_OVER", description="Judge presided over case"),
        ]
        
        schema = GraphSchema(
            node_types=tuple(node_types),
            relationship_types=tuple(relationship_types),
            patterns=()
        )
        
        # Test 1: Direct LLMEntityRelationExtractor (known to work)
        logger.info("🧪 Test 1: Direct LLMEntityRelationExtractor")
        llm = VertexAILLM(
            model_name="gemini-2.0-flash-exp",
            model_params={
                "temperature": 0,
                "response_format": {"type": "json_object"}
            }
        )
        
        extractor = LLMEntityRelationExtractor(
            llm=llm,
            create_lexical_graph=False,
            on_error=OnError.RAISE
        )
        
        chunk = TextChunk(
            text=test_text,
            index=0,
            chunk_id="debug_chunk_direct"
        )
        
        chunks = TextChunks(chunks=[chunk])
        
        logger.info("   Running direct extraction...")
        direct_result = await extractor.run(chunks, schema=schema)
        
        logger.info(f"   ✅ Direct extraction: {len(direct_result.nodes)} nodes, {len(direct_result.relationships)} relationships")
        for i, node in enumerate(direct_result.nodes[:3]):
            logger.info(f"      Node {i+1}: {node.label} - {node.properties.get('name', 'unnamed')}")
        logger.info("")
        
        # Test 2: SimpleKGPipeline (currently failing)
        logger.info("🧪 Test 2: SimpleKGPipeline Integration")
        
        # Initialize Neo4j connection
        driver = GraphDatabase.driver(
            os.getenv("NEO4J_URI"),
            auth=(os.getenv("NEO4J_USERNAME", "neo4j"), os.getenv("NEO4J_PASSWORD"))
        )
        
        # Clean database first
        with driver.session() as session:
            session.run("MATCH (n:__KGBuilder__) DETACH DELETE n")
        logger.info("   Neo4j cleaned for test")
        
        # Create VoyageAI embedder (same as in our pipeline)
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import VoyageAIEmbeddings
        
        embedder = VoyageAIEmbeddings(
            model="voyage-3-large",
            api_key=os.getenv("VOYAGE_API_KEY"),
            output_dimension=1024
        )
        
        # Create text splitter
        recursive_splitter = RecursiveCharacterTextSplitter(
            chunk_size=2000,
            chunk_overlap=200,
            length_function=len
        )
        text_splitter = LangChainTextSplitterAdapter(text_splitter=recursive_splitter)
        
        # Initialize SimpleKGPipeline
        kg_pipeline = SimpleKGPipeline(
            llm=llm,
            embedder=embedder,
            driver=driver,
            text_splitter=text_splitter,
            from_pdf=False,
            schema=schema,
            on_error=OnError.RAISE
        )
        
        logger.info("   Running SimpleKGPipeline...")
        
        # Process with SimpleKGPipeline
        pipeline_result = await kg_pipeline.run_async(text=test_text)
        
        logger.info(f"   Pipeline result type: {type(pipeline_result)}")
        logger.info(f"   Pipeline result: {pipeline_result}")
        
        # Check what was actually stored in Neo4j
        with driver.session() as session:
            # Count all nodes
            node_result = session.run("""
                MATCH (n:__KGBuilder__)
                WHERE NOT n:Chunk
                RETURN count(n) as node_count,
                       collect(DISTINCT labels(n)[1]) as node_types,
                       collect(DISTINCT n.name)[0..5] as sample_names
            """)
            node_record = node_result.single()
            
            # Count relationships
            rel_result = session.run("""
                MATCH (start:__KGBuilder__)-[r]->(end:__KGBuilder__)
                WHERE NOT start:Chunk AND NOT end:Chunk
                AND NOT type(r) IN ['FROM_CHUNK', 'NEXT_CHUNK']
                RETURN count(r) as rel_count,
                       collect(DISTINCT type(r))[0..5] as rel_types
            """)
            rel_record = rel_result.single()
        
        logger.info(f"   ✅ SimpleKGPipeline completed")
        logger.info(f"   Neo4j stored: {node_record['node_count']} nodes, {rel_record['rel_count']} relationships")
        logger.info(f"   Node types: {node_record['node_types']}")
        logger.info(f"   Sample names: {node_record['sample_names']}")
        logger.info(f"   Relationship types: {rel_record['rel_types']}")
        logger.info("")
        
        # Analysis
        logger.info("🎯 ANALYSIS:")
        
        direct_success = len(direct_result.nodes) > 0
        pipeline_success = node_record['node_count'] > 0
        
        logger.info(f"   Direct LLMEntityRelationExtractor: {'✅' if direct_success else '❌'} {len(direct_result.nodes)} entities")
        logger.info(f"   SimpleKGPipeline Integration: {'✅' if pipeline_success else '❌'} {node_record['node_count']} entities")
        
        if direct_success and not pipeline_success:
            logger.error("🚨 INTEGRATION ISSUE CONFIRMED:")
            logger.error("   LLM extraction works perfectly in isolation")
            logger.error("   SimpleKGPipeline integration is broken - entities not being stored")
            logger.error("   This explains why our pipeline shows 0 entities despite processing")
            
            # Get more debug info
            logger.info("")
            logger.info("🔍 Pipeline Debug Info:")
            if hasattr(pipeline_result, '__dict__'):
                for key, value in pipeline_result.__dict__.items():
                    logger.info(f"   {key}: {value}")
        
        driver.close()
        
        return {
            "direct_extraction_works": direct_success,
            "pipeline_integration_works": pipeline_success,
            "direct_entities": len(direct_result.nodes),
            "pipeline_entities": node_record['node_count'],
            "issue_confirmed": direct_success and not pipeline_success
        }
        
    except Exception as e:
        logger.error(f"❌ SimpleKGPipeline debugging failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(debug_simple_kg_pipeline())
    
    if result and result.get("issue_confirmed"):
        print(f"\n🚨 INTEGRATION ISSUE CONFIRMED!")
        print(f"   Direct LLM extraction: {result['direct_entities']} entities")
        print(f"   SimpleKGPipeline integration: {result['pipeline_entities']} entities")
        print(f"   The LLM works perfectly but SimpleKGPipeline is not storing the results")
        print(f"   This explains the 0 entity extraction in our main pipeline")
    elif result:
        print(f"\n✅ Both methods working")
        print(f"   Direct: {result['direct_entities']} entities")  
        print(f"   Pipeline: {result['pipeline_entities']} entities")
    else:
        print(f"\n❌ Debug failed")