"""
Queue Helper Bridge for CourtListener Client
Bridges the client.py with the existing processing queue infrastructure.
"""

import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

# Import the existing Supabase infrastructure for queue management
try:
    import sys
    from pathlib import Path
    # Add the processing directory to the path
    processing_dir = Path(__file__).parent.parent.parent / "processing"
    sys.path.insert(0, str(processing_dir))

    from storage.supabase_connector import SupabaseConnector
    _supabase = SupabaseConnector()
    logger.info("Using real Supabase for queue management")
except Exception as e:
    logger.warning(f"Supabase connector failed for queue: {e}")
    _supabase = None


def enqueue_for_processing(case_id: str, jurisdiction: str = "TX", priority: int = 5, 
                          metadata: Optional[Dict] = None) -> bool:
    """
    Add a case to the processing queue.
    
    Args:
        case_id: The case ID to process
        jurisdiction: Jurisdiction code (default: TX)
        priority: Processing priority (1-10, 1 is highest)
        metadata: Optional metadata dictionary
        
    Returns:
        True if successfully enqueued, False otherwise
    """
    if not _supabase:
        logger.warning(f"[Queue fallback] would enqueue case {case_id} for processing")
        return False
    
    try:
        # Create queue entry with metadata
        queue_data = {
            "case_id": case_id,
            "jurisdiction": jurisdiction,
            "status": "pending",
            "priority": priority,
            "attempts": 0
        }

        # Add metadata if provided
        if metadata:
            queue_data["metadata"] = metadata

        # Check if already in queue
        existing = _supabase.client.table("case_processing_queue") \
            .select("*") \
            .eq("case_id", case_id) \
            .eq("jurisdiction", jurisdiction) \
            .execute()

        if hasattr(existing, 'data') and existing.data:
            # Already in queue, update metadata if provided
            if metadata:
                _supabase.client.table("case_processing_queue") \
                    .update({"metadata": metadata, "updated_at": "NOW()"}) \
                    .eq("case_id", case_id) \
                    .eq("jurisdiction", jurisdiction) \
                    .execute()
            logger.info(f"Updated existing queue entry for case {case_id}")
            return True

        # Add new entry to queue
        response = _supabase.client.table("case_processing_queue") \
            .insert(queue_data) \
            .execute()

        if hasattr(response, 'error') and response.error:
            logger.error(f"Error adding to queue: {response.error}")
            return False

        logger.info(f"Enqueued case {case_id} for processing (priority: {priority}) with metadata: {list(metadata.keys()) if metadata else 'none'}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to enqueue case {case_id}: {e}")
        return False


def enqueue_batch_for_processing(case_ids: list, jurisdiction: str = "TX", 
                                priority: int = 5) -> int:
    """
    Add multiple cases to the processing queue.
    
    Args:
        case_ids: List of case IDs to process
        jurisdiction: Jurisdiction code (default: TX)
        priority: Processing priority (1-10, 1 is highest)
        
    Returns:
        Number of cases successfully enqueued
    """
    if not _supabase:
        logger.warning(f"[Queue fallback] would enqueue {len(case_ids)} cases for processing")
        return 0
    
    success_count = 0
    for case_id in case_ids:
        if enqueue_for_processing(case_id, jurisdiction, priority):
            success_count += 1
    
    logger.info(f"Enqueued {success_count}/{len(case_ids)} cases for processing")
    return success_count


def get_queue_status(jurisdiction: str = "TX") -> Dict:
    """
    Get processing queue status for a jurisdiction.
    
    Args:
        jurisdiction: Jurisdiction code (default: TX)
        
    Returns:
        Dictionary with queue statistics
    """
    if not _supabase:
        return {"error": "Supabase not available"}
    
    try:
        # Get queue counts by status
        response = _supabase.client.table("case_processing_queue") \
            .select("status") \
            .eq("jurisdiction", jurisdiction) \
            .execute()
        
        if hasattr(response, 'data') and response.data:
            status_counts = {}
            for row in response.data:
                status = row.get("status", "unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
            
            return {
                "jurisdiction": jurisdiction,
                "total": len(response.data),
                "by_status": status_counts
            }
        
        return {"jurisdiction": jurisdiction, "total": 0, "by_status": {}}
        
    except Exception as e:
        logger.error(f"Failed to get queue status for {jurisdiction}: {e}")
        return {"error": str(e)}


def clear_completed_queue_items(jurisdiction: str = "TX", days_old: int = 7) -> int:
    """
    Clear completed queue items older than specified days.
    
    Args:
        jurisdiction: Jurisdiction code (default: TX)
        days_old: Remove items completed more than this many days ago
        
    Returns:
        Number of items removed
    """
    if not _supabase:
        logger.warning(f"[Queue fallback] would clear completed items for {jurisdiction}")
        return 0
    
    try:
        # This would require a more complex SQL query to handle date filtering
        # For now, just log the intent
        logger.info(f"Would clear completed queue items for {jurisdiction} older than {days_old} days")
        return 0
        
    except Exception as e:
        logger.error(f"Failed to clear completed queue items: {e}")
        return 0
