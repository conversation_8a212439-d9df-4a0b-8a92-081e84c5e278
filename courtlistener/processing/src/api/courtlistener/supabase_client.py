"""
Supabase Client Bridge for CourtListener Client
Bridges the client.py with the existing Supabase infrastructure for cursor management.
"""

import os
import time
import json
import logging
from typing import Optional

logger = logging.getLogger(__name__)

# Import the existing Supabase infrastructure
try:
    import sys
    from pathlib import Path
    # Add the processing directory to the path
    processing_dir = Path(__file__).parent.parent.parent / "processing"
    sys.path.insert(0, str(processing_dir))

    from storage.supabase_connector import SupabaseConnector
    _supabase = SupabaseConnector()
    logger.info("Using real Supabase storage")
except Exception as e:
    logger.warning(f"Supabase connector failed: {e}")
    _supabase = None

# ETL state table name
_TABLE = "etl_state"


def ensure_etl_state_table():
    """Ensure the etl_state table exists in Supabase."""
    if not _supabase:
        return False
    
    try:
        # Check if table exists by trying to query it
        _supabase.client.table(_TABLE).select("*").limit(1).execute()
        return True
    except Exception:
        # Table doesn't exist, create it
        try:
            create_sql = """
            CREATE TABLE IF NOT EXISTS public.etl_state (
                state text NOT NULL,
                court_slug text NOT NULL,
                cursor text,
                ts bigint,
                PRIMARY KEY (state, court_slug)
            );
            """
            _supabase.execute_sql(create_sql)
            logger.info("Created etl_state table")
            return True
        except Exception as e:
            logger.error(f"Failed to create etl_state table: {e}")
            return False


def load_cursor(state: str, court_slug: str) -> Optional[str]:
    """
    Load cursor for a specific state and court from Supabase.
    
    Args:
        state: State code (e.g., 'TX')
        court_slug: Court slug (e.g., 'txctapp1')
        
    Returns:
        Cursor string if found, None otherwise
    """
    if not _supabase:
        return None
    
    try:
        # Ensure table exists
        if not ensure_etl_state_table():
            return None
        
        response = _supabase.client.table(_TABLE) \
            .select("cursor") \
            .eq("state", state) \
            .eq("court_slug", court_slug) \
            .maybe_single() \
            .execute()
        
        if hasattr(response, 'data') and response.data:
            cursor = response.data.get("cursor")
            logger.info(f"Loaded cursor for {state}/{court_slug}: {cursor[:50] if cursor else None}...")
            return cursor
        
        return None
        
    except Exception as e:
        logger.error(f"Failed to load cursor for {state}/{court_slug}: {e}")
        return None


def save_cursor(state: str, court_slug: str, cursor: Optional[str]) -> None:
    """
    Save cursor for a specific state and court to Supabase.
    
    Args:
        state: State code (e.g., 'TX')
        court_slug: Court slug (e.g., 'txctapp1')
        cursor: Cursor string to save (can be None to clear)
    """
    if not _supabase:
        return
    
    try:
        # Ensure table exists
        if not ensure_etl_state_table():
            return
        
        payload = {
            "state": state,
            "court_slug": court_slug,
            "cursor": cursor,
            "ts": int(time.time())
        }
        
        # Upsert on (state, court_slug)
        response = _supabase.client.table(_TABLE) \
            .upsert(payload, on_conflict="state,court_slug") \
            .execute()
        
        logger.info(f"Saved cursor for {state}/{court_slug}: {cursor[:50] if cursor else None}...")
        
    except Exception as e:
        logger.error(f"Failed to save cursor for {state}/{court_slug}: {e}")


def get_all_cursors(state: str) -> dict:
    """
    Get all cursors for a specific state.
    
    Args:
        state: State code (e.g., 'TX')
        
    Returns:
        Dictionary mapping court_slug to cursor
    """
    if not _supabase:
        return {}
    
    try:
        response = _supabase.client.table(_TABLE) \
            .select("court_slug, cursor") \
            .eq("state", state) \
            .execute()
        
        if hasattr(response, 'data') and response.data:
            return {row["court_slug"]: row["cursor"] for row in response.data if row["cursor"]}
        
        return {}
        
    except Exception as e:
        logger.error(f"Failed to get cursors for {state}: {e}")
        return {}


def clear_cursor(state: str, court_slug: str) -> None:
    """
    Clear cursor for a specific state and court.
    
    Args:
        state: State code (e.g., 'TX')
        court_slug: Court slug (e.g., 'txctapp1')
    """
    save_cursor(state, court_slug, None)
