"""
GCS Helper Bridge for CourtListener Client
Bridges the client.py with the existing GCS infrastructure.
"""

import os
import gzip
import json
import logging
from pathlib import Path
from typing import Optional

logger = logging.getLogger(__name__)

# Import the existing GCS infrastructure
try:
    import sys
    from pathlib import Path
    # Add the processing directory to the path
    processing_dir = Path(__file__).parent.parent.parent / "processing"
    sys.path.insert(0, str(processing_dir))

    from storage.gcs_connector import GCSConnector
    _gcs = GCSConnector(bucket_name=os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury"))
    logger.info("Using real GCS storage")
except Exception as e:
    try:
        from storage.gcs_connector_mock import GCSConnector
        _gcs = GCSConnector(bucket_name=os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury"))
        logger.warning(f"Using mock GCS storage due to error: {str(e)}")
    except Exception as e2:
        logger.error(f"Failed to import any GCS connector: {e2}")
        _gcs = None


def upload_to_gcs(local_path: str, gcs_path: str, state: str = "TX") -> None:
    """
    Uploads a local file to GCS using the existing infrastructure with state prefix.

    Args:
        local_path: Path to the local file
        gcs_path: Destination path in GCS bucket (will be prefixed with state)
        state: State code to prefix the path (default: TX)
    """
    try:
        if not os.path.exists(local_path):
            raise FileNotFoundError(f"Local file not found: {local_path}")

        # Add state prefix to the GCS path
        state_prefixed_path = f"{state.upper()}/{gcs_path}"

        # Use the existing GCS connector
        result = _gcs.store_file(local_path, state_prefixed_path)
        logger.info(f"Uploaded {local_path} to {state_prefixed_path}")

    except Exception as e:
        logger.error(f"Failed to upload {local_path} to {state_prefixed_path}: {e}")
        raise


def upload_json_to_gcs(data: dict, gcs_path: str) -> None:
    """
    Uploads JSON data directly to GCS.
    
    Args:
        data: Dictionary to upload as JSON
        gcs_path: Destination path in GCS bucket
    """
    try:
        result = _gcs.store_json(data, gcs_path)
        logger.info(f"Uploaded JSON data to {gcs_path}")
        
    except Exception as e:
        logger.error(f"Failed to upload JSON to {gcs_path}: {e}")
        raise


def upload_compressed_json_to_gcs(data: dict, gcs_path: str) -> None:
    """
    Uploads compressed JSON data to GCS.
    
    Args:
        data: Dictionary to upload as compressed JSON
        gcs_path: Destination path in GCS bucket (should end with .gz)
    """
    try:
        import tempfile
        
        # Create temporary compressed file
        with tempfile.NamedTemporaryFile(mode='wb', delete=False, suffix='.json.gz') as tmp_file:
            with gzip.open(tmp_file.name, 'wt', encoding='utf-8') as gz_file:
                json.dump(data, gz_file, indent=2)
            
            # Upload the compressed file
            upload_to_gcs(tmp_file.name, gcs_path)
            
            # Clean up
            os.unlink(tmp_file.name)
            
    except Exception as e:
        logger.error(f"Failed to upload compressed JSON to {gcs_path}: {e}")
        raise
