# People Enrichment System

This system enriches case data with judge and panel information from CourtListener's people directory.

## Overview

The people enrichment system automatically:
1. Extracts author and panel information from CourtListener opinion data
2. Fetches detailed people records from CourtListener's `/people/{id}` API
3. Creates normalized people records in the `people` table
4. Links cases to people via the `case_people` table
5. Updates cases with convenience fields for quick access

## Database Schema

### `people` Table
Stores unique people (judges/justices) from CourtListener:
- `id` (BIGINT): CourtListener people ID (primary key)
- `display_name` (TEXT): Full name as displayed
- `given_name`, `middle_name`, `family_name` (TEXT): Name components
- `is_judge` (BOOLEAN): Flag indicating judicial role
- `raw` (JSONB): Full CourtListener people payload

### `case_people` Table
Many-to-many linking table between cases and people:
- `case_id` (TEXT): References cases.id
- `person_id` (BIGINT): References people.id
- `role` (TEXT): 'author', 'panel', 'concurring', 'dissenting', etc.
- `matter_uid` (TEXT): Links to matter for grouping
- `position` (INT): Order in panel

### `cases` Table Additions
Convenience fields added to cases:
- `author_person_id` (BIGINT): Direct reference to author
- `panel_person_ids` (BIGINT[]): Array of panel member IDs
- `per_curiam` (BOOLEAN): Flag for per curiam decisions
- `people_enriched_at` (TIMESTAMPTZ): Timestamp of enrichment

## Usage

### During Ingestion
People enrichment runs automatically during case ingestion via `client.py`:

```bash
python client.py --state TX --max-cases 50
```

The system will:
1. Extract author/panel IDs from opinion JSON
2. Fetch people data from CourtListener API
3. Create people records and case_people links
4. Update case convenience fields

### Backfill Existing Cases
For cases ingested before people enrichment was enabled:

```bash
# Dry run to see what would be processed
python scripts/backfill_people_links.py --limit 50 --dry-run

# Process cases
python scripts/backfill_people_links.py --limit 100 --state TX
```

### Querying People Data

**Find all cases by a specific judge:**
```sql
SELECT c.id, c.case_name, c.matter_uid
FROM cases c
JOIN case_people cp ON cp.case_id = c.id
JOIN people p ON p.id = cp.person_id
WHERE p.display_name ILIKE '%Smith%' AND cp.role = 'author';
```

**Find all panel members for a matter:**
```sql
SELECT p.display_name, cp.role, cp.position
FROM case_people cp
JOIN people p ON p.id = cp.person_id
WHERE cp.matter_uid = 'dck:70999052'
ORDER BY cp.role, cp.position;
```

**Cases with multiple opinions by same author:**
```sql
SELECT cp.matter_uid, p.display_name, COUNT(*) as opinion_count
FROM case_people cp
JOIN people p ON p.id = cp.person_id
WHERE cp.role = 'author'
GROUP BY cp.matter_uid, p.display_name
HAVING COUNT(*) > 1;
```

## Data Flow

1. **Ingestion**: `client.py` extracts people IDs from opinion JSON
2. **API Fetch**: System calls CourtListener `/people/{id}` for each person
3. **Normalization**: People data stored in normalized `people` table
4. **Linking**: `case_people` records created with role and matter context
5. **Convenience**: `cases` table updated with direct references

## Error Handling

- If people API calls fail, the case is still ingested without people data
- Failed people enrichment is logged but doesn't block the pipeline
- Retries are handled via idempotent upserts on unique constraints

## Integration with Matter Linking

People enrichment works seamlessly with the matter linking system:
- All `case_people` records include `matter_uid` for grouping
- Related opinions (memorandum + judgment) share people links
- Vector search metadata includes people information via matter links

## Performance Considerations

- People records are cached via upserts (no duplicate API calls)
- Batch processing minimizes database round trips
- Indexes on `case_people` support efficient queries by case, person, role, and matter
