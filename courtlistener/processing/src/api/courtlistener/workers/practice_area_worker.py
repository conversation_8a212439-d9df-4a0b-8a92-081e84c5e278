#!/usr/bin/env python3
"""
Practice Area Classification Worker

Classifies legal cases into practice areas using Gemini LLM.
Focuses on Personal Injury and Medical Malpractice classification.
"""

import argparse
import json
import logging
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional

# Add the project root to Python path
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from supabase import create_client, Client as SupabaseClient
from google.cloud import storage
import google.generativeai as genai

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
LOG = logging.getLogger(__name__)

# Practice area categories
CATEGORIES = ["Personal Injury", "Medical Malpractice"]

# Enhanced keyword lists for better classification
MED_MAL_POS_TERMS = [
    "doctor", "physician", "surgery", "hospital", "medical negligence",
    "surgical error", "misdiagnosis", "failure to diagnose", "medication error",
    "informed consent", "standard of care", "res ipsa", "medical malpractice"
]

PI_POS_TERMS = [
    "negligence", "premises liability", "wrongful death", "car accident",
    "truck accident", "products liability", "slip and fall", "personal injury"
]

# Negative terms that reduce confidence
GEN_NEG_TERMS = [
    "s.p.a.", "llc", "inc.", "corporation", "trademark", "patent",
    "contract", "employment", "criminal", "family law", "divorce"
]

class PracticeAreaClassifier:
    def __init__(self):
        self.supabase = self._init_supabase()
        self.gcs_client = self._init_gcs()
        self.gemini_client = self._init_gemini()
        
    def _init_supabase(self) -> SupabaseClient:
        """Initialize Supabase client"""
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        if not supabase_url or not supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
        return create_client(supabase_url, supabase_key)
    
    def _init_gcs(self) -> storage.Client:
        """Initialize Google Cloud Storage client"""
        return storage.Client()
    
    def _init_gemini(self):
        """Initialize Gemini client"""
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY must be set")
        genai.configure(api_key=api_key)
        return genai.GenerativeModel('gemini-2.5-flash-lite')
    
    def get_cases_to_classify(self, state: str, limit: int, since_days: int, 
                            include_courts: Optional[List[str]] = None,
                            exclude_courts: Optional[List[str]] = None,
                            force: bool = False) -> List[Dict]:
        """Get cases that need practice area classification"""
        
        # Build base query
        query = self.supabase.table("cases").select(
            "id, gcs_path, gcs_cluster_path, court_slug, date_filed, docket_number"
        ).eq("jurisdiction", state).not_.is_("gcs_path", "null")

        # Filter by practice area status
        if not force:
            query = query.is_("primary_practice_area", "null")

        # Apply date filter
        if since_days > 0:
            since_date = datetime.now() - timedelta(days=since_days)
            query = query.gte("date_filed", since_date.isoformat())
        
        # Apply court filters
        if include_courts:
            query = query.in_("court_slug", include_courts)
        elif exclude_courts:
            query = query.not_.in_("court_slug", exclude_courts)
        
        # Apply limit
        query = query.limit(limit)
        
        result = query.execute()
        return result.data
    
    def download_opinion_text(self, gcs_path: str, gcs_cluster_path: Optional[str] = None) -> Optional[str]:
        """Download opinion text from GCS"""
        try:
            bucket_name = "texas-laws-personalinjury"
            bucket = self.gcs_client.bucket(bucket_name)
            
            # Download main opinion text
            blob = bucket.blob(gcs_path)
            if not blob.exists():
                LOG.warning(f"Opinion file not found: {gcs_path}")
                return None
            
            content = blob.download_as_bytes()
            if gcs_path.endswith('.gz'):
                import gzip
                content = gzip.decompress(content)
            
            opinion_data = json.loads(content.decode('utf-8'))
            text = opinion_data.get('plain_text', '')
            
            # If text is short and cluster exists, append cluster summary
            if len(text) < 5000 and gcs_cluster_path:
                try:
                    cluster_blob = bucket.blob(gcs_cluster_path)
                    if cluster_blob.exists():
                        cluster_content = cluster_blob.download_as_bytes()
                        if gcs_cluster_path.endswith('.gz'):
                            cluster_content = gzip.decompress(cluster_content)
                        cluster_data = json.loads(cluster_content.decode('utf-8'))
                        
                        # Add relevant cluster fields
                        cluster_summary = []
                        for field in ['syllabus', 'summary', 'headnotes']:
                            if cluster_data.get(field):
                                cluster_summary.append(f"{field}: {cluster_data[field]}")
                        
                        if cluster_summary:
                            text += "\n\nCase Summary:\n" + "\n".join(cluster_summary)
                except Exception as e:
                    LOG.debug(f"Could not append cluster summary: {e}")
            
            return text[:200000]  # Limit text size
            
        except Exception as e:
            LOG.error(f"Error downloading opinion {gcs_path}: {e}")
            return None
    
    def has_keywords(self, text: str, keywords: List[str]) -> bool:
        """Check if text contains any of the specified keywords"""
        if not keywords:
            return True  # No filter = pass all

        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in keywords)

    def calculate_keyword_score(self, text: str) -> Dict[str, float]:
        """Calculate keyword-based scores for practice areas"""
        text_lower = text.lower()

        # Count positive terms
        med_mal_score = sum(1 for term in MED_MAL_POS_TERMS if term.lower() in text_lower)
        pi_score = sum(1 for term in PI_POS_TERMS if term.lower() in text_lower)

        # Count negative terms (reduce confidence)
        neg_score = sum(1 for term in GEN_NEG_TERMS if term.lower() in text_lower)

        # Normalize scores
        med_mal_boost = min(med_mal_score * 0.1, 0.3)  # Max 0.3 boost
        pi_boost = min(pi_score * 0.1, 0.3)  # Max 0.3 boost
        neg_penalty = min(neg_score * 0.15, 0.4)  # Max 0.4 penalty

        return {
            "med_mal_boost": med_mal_boost,
            "pi_boost": pi_boost,
            "neg_penalty": neg_penalty
        }

    def classify_text(self, text: str, keywords: Optional[List[str]] = None, threshold_medmal: float = 0.75, threshold_pi: float = 0.70) -> Dict:
        """Classify text using Gemini LLM with optional keyword pre-filtering"""
        if not text or len(text.strip()) < 100:
            return {"practice_area": "NONE", "confidence": 0.0, "rationale": "Insufficient text"}

        # Apply keyword pre-filter if specified
        if keywords and not self.has_keywords(text, keywords):
            return {"practice_area": "NONE", "confidence": 0.95, "rationale": "No relevant keywords found - likely not PI/MM"}
        
        system_prompt = """You are a legal domain classifier specializing in practice area identification.

Your task is to classify legal opinions into one of these categories:
- Personal Injury: Cases involving physical harm, accidents, negligence causing bodily injury, wrongful death, product liability, premises liability, auto accidents, slip and fall, etc.
- Medical Malpractice: Cases involving medical negligence, hospital errors, surgical mistakes, misdiagnosis, failure to diagnose, pharmaceutical errors, medical device failures, etc.
- NONE: All other cases (criminal, contract, family, real estate, employment, etc.)

Analyze the case content including parties, facts, legal issues, and injuries described. Focus on the primary legal claim.

Return ONLY a JSON object with this exact format:
{"practice_area": "Personal Injury|Medical Malpractice|NONE", "confidence": 0.0-1.0, "rationale": "brief explanation"}

Confidence guidelines:
- 0.9-1.0: Very clear indicators (explicit injury/medical terms, typical fact patterns)
- 0.7-0.8: Strong indicators but some ambiguity
- 0.5-0.6: Weak indicators, uncertain classification
- 0.0-0.4: No clear indicators or insufficient information"""

        try:
            # Prepare the prompt
            full_prompt = f"{system_prompt}\n\nCase Text:\n{text}"
            
            # Call Gemini with JSON mode
            response = self.gemini_client.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,
                    max_output_tokens=500,
                    response_mime_type="application/json"
                )
            )
            
            # Parse response
            result = json.loads(response.text)
            
            # Validate and normalize
            practice_area = result.get("practice_area", "NONE")
            if practice_area not in CATEGORIES and practice_area != "NONE":
                practice_area = "NONE"
            
            confidence = float(result.get("confidence", 0.0))
            confidence = max(0.0, min(1.0, confidence))  # Clamp to [0,1]

            # Apply keyword-based scoring adjustments
            keyword_scores = self.calculate_keyword_score(text)

            if practice_area == "Medical Malpractice":
                confidence += keyword_scores["med_mal_boost"]
                confidence -= keyword_scores["neg_penalty"]
                # Apply higher threshold for Med Mal
                if confidence < threshold_medmal:
                    practice_area = "NONE"
                    confidence = 0.95
                    rationale = f"Below Med Mal threshold ({threshold_medmal}): " + result.get("rationale", "")
            elif practice_area == "Personal Injury":
                confidence += keyword_scores["pi_boost"]
                confidence -= keyword_scores["neg_penalty"]
                # Apply standard threshold for PI
                if confidence < threshold_pi:
                    practice_area = "NONE"
                    confidence = 0.95
                    rationale = f"Below PI threshold ({threshold_pi}): " + result.get("rationale", "")

            confidence = max(0.0, min(1.0, confidence))  # Re-clamp after adjustments
            rationale = result.get("rationale", "")[:500]  # Limit length

            return {
                "practice_area": practice_area,
                "confidence": confidence,
                "rationale": rationale
            }
            
        except Exception as e:
            LOG.error(f"Error in LLM classification: {e}")
            return {"practice_area": "NONE", "confidence": 0.0, "rationale": f"Classification error: {str(e)}"}
    
    def update_case_practice_area(self, case_id: str, classification: Dict, threshold: float, dry_run: bool = False) -> bool:
        """Update case with practice area classification"""
        practice_area = classification["practice_area"]
        confidence = classification["confidence"]
        
        update_data = {
            "practice_area_checked_at": datetime.utcnow().isoformat() + "Z"
        }

        # Only set practice area if confidence meets threshold
        if practice_area in CATEGORIES and confidence >= threshold:
            update_data["primary_practice_area"] = practice_area
            update_data["practice_area_confidence"] = confidence
            updated = True
        else:
            updated = False
        
        if dry_run:
            if updated:
                LOG.info(f"Would set case {case_id} -> {practice_area} (confidence: {confidence:.2f})")
            else:
                LOG.info(f"Would mark case {case_id} as checked (confidence: {confidence:.2f} < {threshold})")
            return updated
        
        try:
            self.supabase.table("cases").update(update_data).eq("id", case_id).execute()
            return updated
        except Exception as e:
            LOG.error(f"Error updating case {case_id}: {e}")
            return False
    
    def run_classification(self, args) -> None:
        """Run the classification process"""
        LOG.info(f"Starting practice area classification for {args.state}")
        LOG.info(f"Threshold: {args.threshold}, Limit: {args.limit}, Dry-run: {args.dry_run}")

        # Parse court filters
        include_courts = [c.strip() for c in args.include_courts.split(",") if c.strip()] if args.include_courts else None
        exclude_courts = [c.strip() for c in args.exclude_courts.split(",") if c.strip()] if args.exclude_courts else None

        # Parse keywords for pre-filtering
        keywords = [k.strip() for k in args.keywords.split(",") if k.strip()] if args.keywords else None
        if keywords:
            LOG.info(f"Using keyword pre-filter: {keywords}")
        
        # Get cases to classify
        cases = self.get_cases_to_classify(
            state=args.state,
            limit=args.limit,
            since_days=args.since_days,
            include_courts=include_courts,
            exclude_courts=exclude_courts,
            force=args.force
        )
        
        LOG.info(f"Found {len(cases)} cases to classify")
        
        checked = 0
        updated = 0
        low_confidence_positives = []  # Track lowest confidence positives for review

        for case in cases:
            case_id = case["id"]
            gcs_path = case["gcs_path"]
            gcs_cluster_path = case.get("gcs_cluster_path")
            
            LOG.debug(f"Processing case {case_id}: {case.get('docket_number', 'N/A')}")
            
            # Download opinion text
            text = self.download_opinion_text(gcs_path, gcs_cluster_path)
            if not text:
                LOG.warning(f"No text found for case {case_id}")
                continue
            
            # Classify
            classification = self.classify_text(text, keywords, args.threshold_medmal, args.threshold)
            checked += 1

            # Track low confidence positives for review
            if (classification['practice_area'] in CATEGORIES and
                0.70 <= classification['confidence'] <= 0.79):
                low_confidence_positives.append({
                    'case_id': case_id,
                    'practice_area': classification['practice_area'],
                    'confidence': classification['confidence'],
                    'rationale': classification['rationale'][:200],
                    'docket_number': case.get('docket_number', 'N/A')
                })

            # Update database
            was_updated = self.update_case_practice_area(case_id, classification, args.threshold, args.dry_run)
            if was_updated:
                updated += 1

            # Log result
            LOG.info(f"Case {case_id}: {classification['practice_area']} (confidence: {classification['confidence']:.2f}) - {classification['rationale'][:100]}")

        # Log low confidence positives for review
        if low_confidence_positives:
            LOG.info(f"Low confidence positives (0.70-0.79) for review:")
            for item in sorted(low_confidence_positives, key=lambda x: x['confidence'])[:10]:
                LOG.info(f"  {item['case_id']} ({item['docket_number']}): {item['practice_area']} ({item['confidence']:.2f}) - {item['rationale']}")

        LOG.info(f"Practice-area classification complete: checked={checked} updated={updated}")


def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Practice Area Classification Worker")
    parser.add_argument("--state", required=True, help="State jurisdiction (e.g., TX)")
    parser.add_argument("--limit", type=int, default=500, help="Maximum cases to process")
    parser.add_argument("--since-days", type=int, default=3650, help="Days back to consider")
    parser.add_argument("--include-courts", default="", help="Comma-separated court slugs to include")
    parser.add_argument("--exclude-courts", default="", help="Comma-separated court slugs to exclude")
    parser.add_argument("--threshold", type=float, default=0.70, help="Confidence threshold for PI classification")
    parser.add_argument("--threshold-medmal", type=float, default=0.75, help="Confidence threshold for Medical Malpractice classification")
    parser.add_argument("--force", action="store_true", help="Reclassify cases that already have practice areas")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")
    parser.add_argument("--llm", choices=["gemini", "openai"], default="gemini", help="LLM provider to use")
    parser.add_argument("--keywords", default="", help="Comma-separated keywords for pre-filtering (empty = no filter)")

    return parser.parse_args()


def main():
    """Main entry point"""
    args = parse_args()
    
    try:
        classifier = PracticeAreaClassifier()
        classifier.run_classification(args)
    except Exception as e:
        LOG.error(f"Classification failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    raise SystemExit(main())
