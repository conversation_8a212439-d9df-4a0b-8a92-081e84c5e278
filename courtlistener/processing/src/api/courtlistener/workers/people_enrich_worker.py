#!/usr/bin/env python3
"""
People Enrichment Worker - Three-Stage Pipeline
Extracts author, panel, concurrences, dissents from opinions using:
1. Cluster JSON (high trust)
2. Regex/heuristics on opinion text (medium trust) 
3. LLM fallback (controlled, low/medium trust)
"""

import argparse
import hashlib
import json
import logging
import os
import re
import sys
import unicodedata
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import gzip
from dataclasses import dataclass

from dotenv import load_dotenv

# Supabase
from supabase import create_client, Client as SupabaseClient

# GCS
from google.cloud import storage

LOG = logging.getLogger(__name__)

def _parse_court_list(s: str) -> List[str]:
    """Parse comma-separated court slugs, normalize to lowercase"""
    return [x.strip().lower() for x in s.split(",") if x.strip()]

def _resolve_courts(state: str, include_arg: str, exclude_arg: str) -> Tuple[List[str], List[str]]:
    """Resolve court include/exclude lists based on state and arguments"""
    include = _parse_court_list(include_arg)
    exclude = _parse_court_list(exclude_arg)

    # For FED: require include courts
    if state == "FED" and not include:
        raise ValueError("For --state FED you must pass --include-courts with one or more slugs (e.g., ca5).")

    # For TX: use default courts if none specified
    if state == "TX" and not include:
        # Default TX appellate + CCA courts
        include = ["txctapp1", "txctapp2", "txctapp3", "txctapp4", "txctapp5", "txctapp6",
                  "txctapp7", "txctapp8", "txctapp9", "txctapp10", "txctapp11", "txctapp12",
                  "txctapp13a", "txctapp13b", "txctapp14", "texcrimapp"]

    return include, exclude


def normalize_name(name: str) -> str:
    """Normalize a person name for consistent matching"""
    if not name:
        return ""

    # Remove unicode accents and normalize
    name = unicodedata.normalize('NFKD', name)
    name = ''.join(c for c in name if not unicodedata.combining(c))

    # Remove punctuation, collapse spaces, lowercase
    name = re.sub(r'[^\w\s]', ' ', name)
    name = re.sub(r'\s+', ' ', name)
    return name.strip().lower()


def create_synthetic_id(court_slug: str, normalized_name: str) -> str:
    """Create deterministic synthetic ID for name-only people"""
    if not normalized_name:
        return ""

    # Create SHA1 hash of normalized name
    name_hash = hashlib.sha1(normalized_name.encode('utf-8')).hexdigest()
    return f"nx:{court_slug}:{name_hash}"


def is_generic_title(name: str) -> bool:
    """Check if name is a generic title that should be skipped"""
    if not name:
        return True

    # Strip common prefixes/suffixes before checking
    cleaned_name = strip_judicial_titles(name)
    normalized = normalize_name(cleaned_name)

    # Skip if too short
    if len(normalized) < 3:
        return True

    # Stoplist of generic titles
    stoplist = {
        'c.j.', 'pj', 'p.j.', 'cj', 'c j', 'j.', 'j', 'chief judge',
        'presiding judge', 'per curiam', 'chief', 'presiding', 'senior',
        'acting', 'justice', 'judge'
    }

    if normalized in stoplist:
        return True

    # Skip all-caps initials only (e.g., "A.B.", "C.J.K.")
    if re.match(r'^[a-z](\s[a-z])*$', normalized):
        return True

    return False


def strip_judicial_titles(name: str) -> str:
    """Strip judicial titles and suffixes from name"""
    if not name:
        return ""

    # Common prefixes and suffixes to remove
    prefixes = ['J.', 'J', 'C.J.', 'P.J.', 'Justice', 'Judge', 'Chief', 'Presiding', 'Senior', 'Acting']
    suffixes = ['J.', 'J', 'C.J.', 'P.J.', 'Justice', 'Judge']

    cleaned = name.strip()

    # Remove prefixes
    for prefix in prefixes:
        if cleaned.startswith(prefix + ' '):
            cleaned = cleaned[len(prefix):].strip()
        elif cleaned.startswith(prefix) and len(cleaned) == len(prefix):
            cleaned = cleaned[len(prefix):].strip()

    # Remove suffixes
    for suffix in suffixes:
        if cleaned.endswith(' ' + suffix):
            cleaned = cleaned[:-len(suffix)].strip()
        elif cleaned.endswith(suffix) and len(cleaned) == len(suffix):
            cleaned = cleaned[:-len(suffix)].strip()

    return cleaned

@dataclass
class PersonMatch:
    name: str
    raw: str
    confidence: float
    char_start: int = 0
    char_end: int = 0

@dataclass
class EnrichmentResult:
    per_curiam: bool = False
    author: Optional[PersonMatch] = None
    panel: List[PersonMatch] = None
    concurrences: List[PersonMatch] = None
    dissents: List[PersonMatch] = None
    extraction_source: str = ""
    confidence: float = 0.0
    evidence: Dict[str, Any] = None
    # New fields for database integration
    author_person_id: Optional[int] = None
    panel_person_ids: List[int] = None
    case_people_links: List[Dict] = None

    def __post_init__(self):
        if self.panel is None:
            self.panel = []
        if self.concurrences is None:
            self.concurrences = []
        if self.dissents is None:
            self.dissents = []
        if self.evidence is None:
            self.evidence = {}
        if self.panel_person_ids is None:
            self.panel_person_ids = []
        if self.case_people_links is None:
            self.case_people_links = []

# Compiled regex patterns for Stage 2
REGEX_PATTERNS = {
    'per_curiam': re.compile(r'\bper\s+curiam\b', re.IGNORECASE),
    'author_delivered': re.compile(
        r'(?i)\b([A-Z][A-Za-z.\-\'\s]+?),\s*(?:J\.|Justice|C\.J\.|P\.J\.)\s*,?\s*(delivered|authored)\s+the\s+opinion\b'
    ),
    'author_opinion_by': re.compile(
        r'(?i)\b(?:opinion|majority opinion|memorandum)\s+by\s+([A-Z][A-Za-z.\-\'\s]+?)(?:,\s*(?:J\.|Justice|C\.J\.|P\.J\.))?\b'
    ),
    'panel_before': re.compile(
        r'(?i)^\s*(before|panel(?:\s+consists\s+of)?)\s*[:\-]?\s*(.+)$', re.MULTILINE
    ),
    'concur_dissent': re.compile(
        r'(?i)\b([A-Z][A-Za-z.\-\'\s]+?),\s*(?:J\.|Justice|C\.J\.|P\.J\.)\b[^.\n]{0,60}\b(concurring|dissenting)\b'
    )
}

class PeopleEnrichmentWorker:
    def __init__(self, supabase_client: SupabaseClient, gcs_client: storage.Client,
                 bucket_name: str, llm_mode: str = "assist", llm_provider: str = "gemini",
                 llm_max_calls: int = 50, llm_threshold: float = 0.7,
                 snippet_window: int = 600, dry_run: bool = False):
        self.supabase = supabase_client
        self.gcs_client = gcs_client
        self.bucket_name = bucket_name
        self.llm_mode = llm_mode
        self.llm_provider = llm_provider
        self.llm_max_calls = llm_max_calls
        self.llm_threshold = llm_threshold
        self.snippet_window = snippet_window
        self.dry_run = dry_run
        
        # Stats
        self.stats = {
            'cases_checked': 0,
            'cases_enriched': 0,
            'cluster_hits': 0,
            'regex_hits': 0,
            'llm_calls': 0,
            'llm_cache_hits': 0,
            'per_curiam_count': 0,
            'total_confidence': 0.0
        }
        
        # LLM setup
        self.llm_client = None
        if llm_mode != "off":
            self._setup_llm()

    def _setup_llm(self):
        """Initialize LLM client based on provider"""
        if self.llm_provider == "gemini":
            try:
                import google.generativeai as genai
                api_key = os.getenv("GEMINI_API_KEY")
                if not api_key:
                    LOG.warning("GEMINI_API_KEY not found, LLM mode disabled")
                    self.llm_mode = "off"
                    return
                genai.configure(api_key=api_key)
                self.llm_client = genai.GenerativeModel('gemini-pro')
                LOG.info("Initialized Gemini client")
            except ImportError:
                LOG.warning("google-generativeai not installed, LLM mode disabled")
                self.llm_mode = "off"
        elif self.llm_provider == "openai":
            try:
                import openai
                api_key = os.getenv("OPENAI_API_KEY")
                if not api_key:
                    LOG.warning("OPENAI_API_KEY not found, LLM mode disabled")
                    self.llm_mode = "off"
                    return
                self.llm_client = openai.OpenAI(api_key=api_key)
                LOG.info("Initialized OpenAI client")
            except ImportError:
                LOG.warning("openai not installed, LLM mode disabled")
                self.llm_mode = "off"

    def enrich_case(self, case_id: str, gcs_opinion_path: str,
                   gcs_cluster_path: Optional[str], court_slug: str,
                   decision_date: Optional[str] = None) -> EnrichmentResult:
        """Main enrichment pipeline for a single case"""
        LOG.info(f"Enriching case {case_id} from court {court_slug}")

        result = EnrichmentResult()

        # Stage 1: Cluster JSON (preferred source - if available)
        if gcs_cluster_path and self.llm_mode != "only":
            result = self.stage1_cluster_extract(case_id, gcs_cluster_path, court_slug)
            if result.confidence > 0:
                LOG.info(f"Case {case_id}: Stage 1 (cluster) succeeded, confidence={result.confidence:.2f}")
                self.stats['cluster_hits'] += 1
                return result
        
        # Stage 2: Regex on opinion text
        if self.llm_mode != "only":
            opinion_text = self._load_opinion_text(gcs_opinion_path)
            if opinion_text:
                result = self._enrich_from_regex(opinion_text, court_slug)
                if result.confidence >= 0.7:  # Accept medium confidence regex
                    LOG.info(f"Case {case_id}: Stage 2 (regex) succeeded, confidence={result.confidence:.2f}")
                    self.stats['regex_hits'] += 1
                    return result
        
        # Stage 3: LLM fallback
        if self.llm_mode in ["assist", "only"] and self.stats['llm_calls'] < self.llm_max_calls:
            if not opinion_text:
                opinion_text = self._load_opinion_text(gcs_opinion_path)
            if opinion_text:
                llm_result = self._enrich_from_llm(opinion_text, case_id, court_slug, decision_date)
                if llm_result.confidence >= self.llm_threshold:
                    LOG.info(f"Case {case_id}: Stage 3 (LLM) succeeded, confidence={llm_result.confidence:.2f}")
                    return llm_result
                else:
                    LOG.info(f"Case {case_id}: Stage 3 (LLM) low confidence={llm_result.confidence:.2f}, rejected")
        
        # No enrichment succeeded
        LOG.info(f"Case {case_id}: No enrichment succeeded")
        return EnrichmentResult(extraction_source="none", confidence=0.0)

    def stage1_cluster_extract(self, case_id: str, gcs_cluster_path: str, court_slug: str) -> EnrichmentResult:
        """
        Stage 1: Extract judge data from cluster JSON (preferred source)
        Returns ExtractResult with author_person_id, panel_person_ids, per_curiam, links[], evidence
        """
        try:
            bucket = self.gcs_client.bucket(self.bucket_name)
            blob = bucket.blob(gcs_cluster_path)
            content = blob.download_as_bytes()

            if gcs_cluster_path.endswith('.gz'):
                content = gzip.decompress(content)

            cluster_data = json.loads(content.decode('utf-8'))
            result = EnrichmentResult(extraction_source="cluster")

            # Track which fields we found for evidence
            fields_used = []
            cluster_keys_present = list(cluster_data.keys())

            # 1. Extract author information (preference: ID > name)
            author_id = cluster_data.get('author_id')
            author_str = cluster_data.get('author_str') or cluster_data.get('opinion_author')

            if author_id:
                # High confidence - structured ID from CourtListener
                result.author_person_id = int(author_id)
                result.confidence = 0.98
                fields_used.append('author_id')
                LOG.info(f"Case {case_id}: Found author_id={author_id}")

            elif author_str:
                # Medium confidence - resolve by name using new synthetic ID logic
                person_id = self._resolve_or_create_person(author_str, court_slug, 'cluster', 0.90)
                if person_id:
                    result.author_person_id = person_id
                    result.confidence = 0.90
                    LOG.info(f"Case {case_id}: Resolved/created author '{author_str}' to person_id={person_id}")

                fields_used.append('author_str' if 'author_str' in cluster_data else 'opinion_author')

            # 2. Extract panel information
            joined_by = cluster_data.get('joined_by', [])
            joined_by_str = cluster_data.get('joined_by_str') or cluster_data.get('panel') or cluster_data.get('judges')

            if joined_by and isinstance(joined_by, list):
                # High confidence - structured IDs
                for judge_id in joined_by:
                    if isinstance(judge_id, (int, str)) and str(judge_id).isdigit():
                        result.panel_person_ids.append(int(judge_id))
                if result.panel_person_ids:
                    result.confidence = max(result.confidence, 0.98)
                    fields_used.append('joined_by')
                    LOG.info(f"Case {case_id}: Found panel IDs: {result.panel_person_ids}")

            elif joined_by_str:
                # Medium confidence - resolve by names using new synthetic ID logic
                if isinstance(joined_by_str, str):
                    names = self._split_panel_names(joined_by_str)
                elif isinstance(joined_by_str, list):
                    names = [str(name).strip() for name in joined_by_str if str(name).strip()]
                else:
                    names = []

                for name in names:
                    person_id = self._resolve_or_create_person(name, court_slug, 'cluster', 0.85)
                    if person_id:
                        result.panel_person_ids.append(person_id)

                if result.panel_person_ids:
                    result.confidence = max(result.confidence, 0.85)
                    fields_used.append('joined_by_str' if 'joined_by_str' in cluster_data else 'panel')
                    LOG.info(f"Case {case_id}: Resolved panel names to IDs: {result.panel_person_ids}")

            # 3. Extract per curiam
            per_curiam = cluster_data.get('per_curiam')
            if per_curiam is not None:
                result.per_curiam = bool(per_curiam)
                result.confidence = max(result.confidence, 0.95)
                fields_used.append('per_curiam')
                LOG.info(f"Case {case_id}: Found per_curiam={result.per_curiam}")

            # Build evidence
            result.evidence = {
                'stage': 'cluster',
                'fields_used': fields_used,
                'cluster_keys_present': cluster_keys_present[:10],  # Limit for storage
                'content_hash': hashlib.sha256(content).hexdigest()[:16],
                'excerpt': str(cluster_data)[:500] + '...' if len(str(cluster_data)) > 500 else str(cluster_data)
            }

            # Create case_people links for database storage
            if result.author_person_id:
                result.case_people_links.append({
                    'person_id': result.author_person_id,
                    'role': 'author',
                    'source': 'cluster',
                    'confidence': result.confidence,
                    'raw': result.evidence
                })

            for person_id in result.panel_person_ids:
                result.case_people_links.append({
                    'person_id': person_id,
                    'role': 'panel',
                    'source': 'cluster',
                    'confidence': result.confidence,
                    'raw': result.evidence
                })

            LOG.info(f"Case {case_id}: Stage 1 (cluster) extracted {len(result.case_people_links)} people links")
            return result

        except Exception as e:
            LOG.warning(f"Case {case_id}: Stage 1 (cluster) failed: {e}")
            return EnrichmentResult(extraction_source="cluster", confidence=0.0)

    def _load_opinion_text(self, gcs_path: str) -> Optional[str]:
        """Load and decompress opinion text from GCS"""
        try:
            bucket = self.gcs_client.bucket(self.bucket_name)
            blob = bucket.blob(gcs_path)
            content = blob.download_as_bytes()

            if gcs_path.endswith('.gz'):
                content = gzip.decompress(content)

            opinion_data = json.loads(content.decode('utf-8'))
            return opinion_data.get('plain_text', '')
        except Exception as e:
            LOG.warning(f"Failed to load opinion from {gcs_path}: {e}")
            return None

    def _enrich_from_cluster(self, gcs_cluster_path: str, court_slug: str) -> EnrichmentResult:
        """Stage 1: Extract from cluster JSON"""
        try:
            bucket = self.gcs_client.bucket(self.bucket_name)
            blob = bucket.blob(gcs_cluster_path)
            content = blob.download_as_bytes()

            if gcs_cluster_path.endswith('.gz'):
                content = gzip.decompress(content)
            
            cluster_data = json.loads(content.decode('utf-8'))
            result = EnrichmentResult(extraction_source="cluster_json")
            
            # Check for structured author data
            author_id = cluster_data.get('author_id')
            author_str = cluster_data.get('author_str', '')
            
            if author_id:
                # High confidence - structured ID
                result.author = PersonMatch(
                    name=author_str or f"Person_{author_id}",
                    raw=f"author_id:{author_id}",
                    confidence=1.0
                )
                result.confidence = 1.0
            elif author_str:
                # Medium confidence - name only
                result.author = PersonMatch(
                    name=self._normalize_name(author_str),
                    raw=author_str,
                    confidence=0.9
                )
                result.confidence = 0.9
            
            # Check for per curiam
            if cluster_data.get('per_curiam'):
                result.per_curiam = True
                result.confidence = max(result.confidence, 0.95)
            
            # Check for panel/joined_by
            joined_by = cluster_data.get('joined_by', [])
            if joined_by:
                for judge in joined_by:
                    if isinstance(judge, dict):
                        name = judge.get('name_full', judge.get('name', ''))
                        if name:
                            result.panel.append(PersonMatch(
                                name=self._normalize_name(name),
                                raw=str(judge),
                                confidence=0.9
                            ))
                result.confidence = max(result.confidence, 0.9)
            
            # Evidence
            result.evidence = {
                'source': 'cluster_json',
                'fields_used': list(cluster_data.keys()),
                'content_hash': hashlib.sha256(content).hexdigest()[:16],
                'note': 'cluster fields present'
            }

            # Resolve PersonMatch objects to person IDs
            self._resolve_person_matches(result, court_slug, 'cluster')

            return result
            
        except Exception as e:
            LOG.warning(f"Failed to process cluster {gcs_cluster_path}: {e}")
            return EnrichmentResult(extraction_source="cluster_json", confidence=0.0)

    def _enrich_from_regex(self, opinion_text: str, court_slug: str) -> EnrichmentResult:
        """Stage 2: Extract using regex patterns"""
        result = EnrichmentResult(extraction_source="opinion_text_regex")
        header_region = opinion_text[:3000]  # Focus on header

        # Per curiam check
        per_curiam_match = REGEX_PATTERNS['per_curiam'].search(header_region)
        if per_curiam_match:
            result.per_curiam = True
            result.confidence = 0.95
            result.evidence['per_curiam_match'] = {
                'raw': per_curiam_match.group(0),
                'start': per_curiam_match.start(),
                'end': per_curiam_match.end()
            }

        # Author patterns
        for pattern_name in ['author_delivered', 'author_opinion_by']:
            matches = REGEX_PATTERNS[pattern_name].finditer(header_region)
            for match in matches:
                name = self._normalize_name(match.group(1))
                if name:
                    confidence = 0.85 if match.start() < 2000 else 0.7  # Header region bonus
                    result.author = PersonMatch(
                        name=name,
                        raw=match.group(0),
                        confidence=confidence,
                        char_start=match.start(),
                        char_end=match.end()
                    )
                    result.confidence = max(result.confidence, confidence)
                    break

        # Panel pattern
        panel_matches = REGEX_PATTERNS['panel_before'].finditer(header_region)
        for match in panel_matches:
            panel_text = match.group(2)
            names = self._parse_panel_text(panel_text)
            for name in names:
                result.panel.append(PersonMatch(
                    name=name,
                    raw=match.group(0),
                    confidence=0.8,
                    char_start=match.start(),
                    char_end=match.end()
                ))
            if names:
                result.confidence = max(result.confidence, 0.8)

        # Concurrences/Dissents
        concur_dissent_matches = REGEX_PATTERNS['concur_dissent'].finditer(opinion_text)
        for match in concur_dissent_matches:
            name = self._normalize_name(match.group(1))
            opinion_type = match.group(2).lower()

            person_match = PersonMatch(
                name=name,
                raw=match.group(0),
                confidence=0.75,
                char_start=match.start(),
                char_end=match.end()
            )

            if 'concur' in opinion_type:
                result.concurrences.append(person_match)
            else:
                result.dissents.append(person_match)

            result.confidence = max(result.confidence, 0.75)

        # Evidence
        result.evidence = {
            'source': 'opinion_text_regex',
            'content_hash': hashlib.sha256(opinion_text.encode()).hexdigest()[:16],
            'header_length': len(header_region),
            'patterns_used': list(REGEX_PATTERNS.keys())
        }

        # Resolve PersonMatch objects to person IDs
        self._resolve_person_matches(result, court_slug, 'regex')

        return result

    def _enrich_from_llm(self, opinion_text: str, case_id: str, court_slug: str,
                        decision_date: Optional[str]) -> EnrichmentResult:
        """Stage 3: LLM fallback extraction"""
        if not self.llm_client:
            return EnrichmentResult(extraction_source="opinion_text_llm", confidence=0.0)

        # Build snippets
        snippets = self._build_llm_snippets(opinion_text)
        if not snippets:
            return EnrichmentResult(extraction_source="opinion_text_llm", confidence=0.0)

        # Check cache
        cache_key = self._get_llm_cache_key(case_id, opinion_text, snippets)
        cached_result = self._get_llm_cache(cache_key)
        if cached_result:
            self.stats['llm_cache_hits'] += 1
            return cached_result

        # Build prompt
        prompt = self._build_llm_prompt(snippets, case_id, court_slug, decision_date)

        try:
            self.stats['llm_calls'] += 1

            if self.llm_provider == "gemini":
                response = self.llm_client.generate_content(prompt)
                response_text = response.text
            else:  # openai
                response = self.llm_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1
                )
                response_text = response.choices[0].message.content

            # Parse and validate response
            result = self._parse_llm_response(response_text, snippets, opinion_text)

            # Cache result
            self._set_llm_cache(cache_key, result)

            return result

        except Exception as e:
            LOG.warning(f"LLM call failed for case {case_id}: {e}")
            return EnrichmentResult(extraction_source="opinion_text_llm", confidence=0.0)

    def _normalize_name(self, name: str) -> str:
        """Normalize judge name"""
        if not name:
            return ""

        # Remove judicial suffixes
        name = re.sub(r',\s*(?:J\.|Justice|C\.J\.|P\.J\.)\s*$', '', name.strip())

        # Title case
        name = name.title()

        # Handle common patterns
        name = re.sub(r'\s+', ' ', name)  # Multiple spaces

        return name.strip()

    def _parse_panel_text(self, panel_text: str) -> List[str]:
        """Parse panel text into individual judge names"""
        if not panel_text:
            return []

        # Split on commas and "and"
        parts = re.split(r',|\band\b', panel_text)
        names = []

        for part in parts:
            part = part.strip()
            if part:
                # Remove trailing judicial suffixes
                name = self._normalize_name(part)
                if name and len(name) > 2:  # Avoid single letters
                    names.append(name)

        return names

    def _build_llm_snippets(self, opinion_text: str) -> List[str]:
        """Build focused snippets for LLM processing"""
        snippets = []

        # Header region
        header = opinion_text[:2000]
        if header.strip():
            snippets.append(header)

        # Lines with trigger words
        trigger_words = ['before', 'panel', 'delivered', 'opinion by', 'per curiam',
                        'concurring', 'dissenting', 'authored']

        lines = opinion_text.split('\n')
        for i, line in enumerate(lines):
            line_lower = line.lower()
            if any(trigger in line_lower for trigger in trigger_words):
                # Add context around trigger line
                start_idx = max(0, i - 2)
                end_idx = min(len(lines), i + 3)
                context_lines = lines[start_idx:end_idx]
                snippet = '\n'.join(context_lines)

                # Limit snippet size
                if len(snippet) <= self.snippet_window * 2:
                    snippets.append(snippet)

        return snippets[:5]  # Limit to 5 snippets

    def _build_llm_prompt(self, snippets: List[str], case_id: str,
                         court_slug: str, decision_date: Optional[str]) -> str:
        """Build LLM prompt with snippets"""
        prompt = """You extract judicial authorship and panel membership from U.S. court opinions.
Use only the provided text snippets. Never infer from outside knowledge.
Return only valid JSON per the schema. If uncertain, return nulls and confidence 0.

"""

        prompt += f"court_slug: {court_slug}\n"
        prompt += f"case_id: {case_id}\n"
        if decision_date:
            prompt += f"decision_date: {decision_date}\n"
        prompt += "\n"

        for i, snippet in enumerate(snippets, 1):
            prompt += f"=== SNIPPET {i} START ===\n"
            prompt += snippet
            prompt += f"\n=== SNIPPET {i} END ===\n\n"

        prompt += """Required JSON output:

{
  "per_curiam": false,
  "author": {"name": "string|null", "raw": "string|null"},
  "panel": [{"name": "string", "raw": "string"}],
  "concurrences": [{"name": "string", "raw": "string"}],
  "dissents": [{"name": "string", "raw": "string"}],
  "supporting_quotes": ["string", "..."],
  "confidence": 0.0
}"""

        return prompt

    def _parse_llm_response(self, response_text: str, snippets: List[str],
                           opinion_text: str) -> EnrichmentResult:
        """Parse and validate LLM JSON response"""
        try:
            # Extract JSON from response
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in response")

            json_text = response_text[json_start:json_end]
            data = json.loads(json_text)

            result = EnrichmentResult(extraction_source="opinion_text_llm")
            result.confidence = float(data.get('confidence', 0.0))
            result.per_curiam = bool(data.get('per_curiam', False))

            # Validate and extract author
            author_data = data.get('author')
            if author_data and author_data.get('name') and author_data.get('raw'):
                raw_text = author_data['raw']
                if self._validate_raw_in_snippets(raw_text, snippets):
                    result.author = PersonMatch(
                        name=self._normalize_name(author_data['name']),
                        raw=raw_text,
                        confidence=result.confidence
                    )

            # Validate and extract panel
            for panel_member in data.get('panel', []):
                if panel_member.get('name') and panel_member.get('raw'):
                    raw_text = panel_member['raw']
                    if self._validate_raw_in_snippets(raw_text, snippets):
                        result.panel.append(PersonMatch(
                            name=self._normalize_name(panel_member['name']),
                            raw=raw_text,
                            confidence=result.confidence
                        ))

            # Validate and extract concurrences/dissents
            for concur in data.get('concurrences', []):
                if concur.get('name') and concur.get('raw'):
                    raw_text = concur['raw']
                    if self._validate_raw_in_snippets(raw_text, snippets):
                        result.concurrences.append(PersonMatch(
                            name=self._normalize_name(concur['name']),
                            raw=raw_text,
                            confidence=result.confidence
                        ))

            for dissent in data.get('dissents', []):
                if dissent.get('name') and dissent.get('raw'):
                    raw_text = dissent['raw']
                    if self._validate_raw_in_snippets(raw_text, snippets):
                        result.dissents.append(PersonMatch(
                            name=self._normalize_name(dissent['name']),
                            raw=raw_text,
                            confidence=result.confidence
                        ))

            # Evidence
            result.evidence = {
                'source': 'opinion_text_llm',
                'content_hash': hashlib.sha256(opinion_text.encode()).hexdigest()[:16],
                'supporting_quotes': data.get('supporting_quotes', []),
                'llm_provider': self.llm_provider,
                'raw_response': json_text
            }

            return result

        except Exception as e:
            LOG.warning(f"Failed to parse LLM response: {e}")
            return EnrichmentResult(extraction_source="opinion_text_llm", confidence=0.0)

    def _validate_raw_in_snippets(self, raw_text: str, snippets: List[str]) -> bool:
        """Validate that raw text appears in provided snippets"""
        for snippet in snippets:
            if raw_text in snippet:
                return True
        return False

    def _get_llm_cache_key(self, case_id: str, opinion_text: str, snippets: List[str]) -> str:
        """Generate cache key for LLM results"""
        content = f"{case_id}:{self.llm_provider}:{hashlib.sha256(opinion_text.encode()).hexdigest()[:16]}"
        return hashlib.sha256(content.encode()).hexdigest()[:32]

    def _get_llm_cache(self, cache_key: str) -> Optional[EnrichmentResult]:
        """Get cached LLM result"""
        cache_dir = Path.home() / ".texlaw_cache" / "llm_people"
        cache_file = cache_dir / f"{cache_key}.json"

        if cache_file.exists():
            try:
                with open(cache_file, 'r') as f:
                    data = json.load(f)
                # Reconstruct result from cache
                # This is a simplified version - full implementation would reconstruct PersonMatch objects
                return EnrichmentResult(
                    extraction_source="opinion_text_llm",
                    confidence=data.get('confidence', 0.0)
                )
            except Exception:
                pass
        return None

    def _set_llm_cache(self, cache_key: str, result: EnrichmentResult):
        """Cache LLM result"""
        cache_dir = Path.home() / ".texlaw_cache" / "llm_people"
        cache_dir.mkdir(parents=True, exist_ok=True)
        cache_file = cache_dir / f"{cache_key}.json"

        try:
            with open(cache_file, 'w') as f:
                json.dump({
                    'confidence': result.confidence,
                    'extraction_source': result.extraction_source,
                    'evidence': result.evidence
                }, f)
        except Exception as e:
            LOG.warning(f"Failed to cache LLM result: {e}")

    def process_cases(self, state: str, limit: int = 100, include_courts: List[str] = None, exclude_courts: List[str] = None):
        """Process cases for people enrichment"""
        LOG.info(f"Starting people enrichment for {state}, limit={limit}")

        # Build query based on state and court filters
        query = self.supabase.table('cases').select('id,court_slug,gcs_path,gcs_cluster_path,date_filed,matter_uid,docket_id')

        if state == "TX":
            query = query.eq('jurisdiction', 'TX')
            if include_courts:
                query = query.in_('court_slug', include_courts)
            if exclude_courts:
                query = query.not_.in_('court_slug', exclude_courts)
        else:  # FED
            # No jurisdiction constraint; use court_slug filters
            if include_courts:
                query = query.in_('court_slug', include_courts)
            if exclude_courts:
                query = query.not_.in_('court_slug', exclude_courts)

        # Get cases that need people enrichment
        cases = query.order('id', desc=True).limit(limit).execute().data
        LOG.info(f"Found {len(cases)} cases to process")

        for case in cases:
            self.stats['cases_checked'] += 1
            case_id = case['id']

            try:
                # Run enrichment pipeline
                result = self.enrich_case(
                    case_id=case_id,
                    gcs_opinion_path=case['gcs_path'],
                    gcs_cluster_path=case.get('gcs_cluster_path'),
                    court_slug=case['court_slug'],
                    decision_date=case.get('date_filed')
                )

                # Update database
                if not self.dry_run:
                    self._update_case_people(case, result)

                # Update stats
                if result.confidence > 0:
                    self.stats['cases_enriched'] += 1
                    self.stats['total_confidence'] += result.confidence

                if result.per_curiam:
                    self.stats['per_curiam_count'] += 1

                # Debug: show extracted data
                debug_info = []
                if result.per_curiam:
                    debug_info.append("PER_CURIAM")
                if result.author:
                    debug_info.append(f"AUTHOR:{result.author.name}")
                if result.panel:
                    debug_info.append(f"PANEL:{len(result.panel)} judges")
                if result.concurrences:
                    debug_info.append(f"CONCUR:{len(result.concurrences)}")
                if result.dissents:
                    debug_info.append(f"DISSENT:{len(result.dissents)}")

                debug_str = " | ".join(debug_info) if debug_info else "NO_DATA"
                LOG.info(f"Case {case_id}: {result.extraction_source}, confidence={result.confidence:.2f} | {debug_str}")

            except Exception as e:
                LOG.error(f"Failed to process case {case_id}: {e}")

        self._log_summary()

    def _update_case_people(self, case: Dict, result: EnrichmentResult):
        """Update database with enrichment results"""
        case_id = case['id']
        court_slug = case['court_slug']
        matter_uid = case['matter_uid']
        docket_id = case['docket_id']

        # Always update people_checked_at
        self.supabase.table('cases').update({'people_checked_at': 'now()'}).eq('id', case_id).execute()

        if result.confidence == 0:
            LOG.debug(f"Case {case_id}: No people data found")
            return

        # Update case convenience fields
        update_data = {
            'per_curiam': result.per_curiam,
            'people_enriched_at': 'now()'
        }

        if result.author_person_id:
            update_data['author_person_id'] = result.author_person_id

        if result.panel_person_ids:
            update_data['panel_person_ids'] = result.panel_person_ids

        # Update the case
        self.supabase.table('cases').update(update_data).eq('id', case_id).execute()

        # Create case_people links (with idempotent upserts)
        for link in result.case_people_links:
            link_data = {
                'case_id': case_id,
                'person_id': link['person_id'],
                'role': link['role'],
                'source': link['source'],
                'confidence': link['confidence'],
                'raw': link['raw'],
                'matter_uid': matter_uid,
                'court_slug': court_slug,
                'docket_id': docket_id
            }

            # Upsert to avoid duplicates (unique constraint on case_id, person_id, role)
            try:
                self.supabase.table('case_people').upsert(
                    link_data,
                    on_conflict='case_id,person_id,role'
                ).execute()
            except Exception as e:
                LOG.warning(f"Failed to upsert case_people link: {e}")

        LOG.info(f"Case {case_id}: Updated with {len(result.case_people_links)} people links from {result.extraction_source}")

    def _resolve_person_by_name(self, normalized_name: str, court_slug: str) -> Optional[int]:
        """Resolve person by normalized name and court slug"""
        try:
            # Try exact match with court slug
            result = (self.supabase.table('people')
                     .select('id')
                     .eq('display_name', normalized_name)
                     .contains('court_slugs', [court_slug])
                     .limit(1)
                     .execute())

            if result.data:
                return result.data[0]['id']

            # Try exact match without court constraint
            result = (self.supabase.table('people')
                     .select('id')
                     .eq('display_name', normalized_name)
                     .limit(1)
                     .execute())

            if result.data:
                return result.data[0]['id']

        except Exception as e:
            LOG.warning(f"Person lookup failed for '{normalized_name}': {e}")

        return None

    def _resolve_or_create_person(self, display_name: str, court_slug: str, source: str, confidence: float, cl_person_id: Optional[int] = None) -> Optional[int]:
        """Resolve existing person or create new one (with CL ID or synthetic ID)"""
        try:
            # Skip generic titles
            if is_generic_title(display_name):
                LOG.debug(f"Skipping generic title: {display_name}")
                return None

            normalized_name = normalize_name(display_name)
            if not normalized_name:
                return None

            # Case 1: We have a CourtListener person ID
            if cl_person_id:
                # Try to find existing person with this CL ID
                result = (self.supabase.table('people')
                         .select('id')
                         .eq('cl_person_id', cl_person_id)
                         .limit(1)
                         .execute())

                if result.data:
                    person_id = result.data[0]['id']
                    # Update existing person
                    self._update_person(person_id, display_name, court_slug, source)
                    return person_id
                else:
                    # Check if we have a synthetic person that could be upgraded
                    synthetic_id = create_synthetic_id(court_slug, normalized_name)
                    synthetic_result = (self.supabase.table('people')
                                      .select('id')
                                      .eq('synthetic_id', synthetic_id)
                                      .limit(1)
                                      .execute())

                    if synthetic_result.data:
                        # Upgrade synthetic person to CL person
                        person_id = synthetic_result.data[0]['id']
                        return self._upgrade_synthetic_to_cl(person_id, cl_person_id, display_name, court_slug, source)
                    else:
                        # Create new person with CL ID
                        return self._create_person_with_cl_id(cl_person_id, display_name, normalized_name, court_slug, source, confidence)

            # Case 2: Name-only person (no CL ID)
            synthetic_id = create_synthetic_id(court_slug, normalized_name)

            # Try to find existing person with this synthetic ID
            result = (self.supabase.table('people')
                     .select('id')
                     .eq('synthetic_id', synthetic_id)
                     .limit(1)
                     .execute())

            if result.data:
                person_id = result.data[0]['id']
                # Update existing person
                self._update_person(person_id, display_name, court_slug, source)
                return person_id
            else:
                # Create new synthetic person
                return self._create_synthetic_person(synthetic_id, display_name, normalized_name, court_slug, source, confidence)

        except Exception as e:
            LOG.warning(f"Error resolving/creating person '{display_name}': {e}")
            return None

    def _create_person_with_cl_id(self, cl_person_id: int, display_name: str, normalized_name: str, court_slug: str, source: str, confidence: float) -> Optional[int]:
        """Create person with CourtListener ID"""
        try:
            person_data = {
                'cl_person_id': cl_person_id,
                'display_name': display_name,
                'normalized_name': normalized_name,
                'court_slug': court_slug,
                'court_slugs': [court_slug],
                'sources': [source],
                'is_judge': True,
                'raw': {'confidence': confidence, 'source': source}
            }

            result = self.supabase.table('people').insert(person_data).execute()
            if result.data:
                LOG.info(f"Created person with CL ID {cl_person_id}: {display_name}")
                return result.data[0]['id']
            return None
        except Exception as e:
            LOG.warning(f"Failed to create person with CL ID {cl_person_id}: {e}")
            return None

    def _create_synthetic_person(self, synthetic_id: str, display_name: str, normalized_name: str, court_slug: str, source: str, confidence: float) -> Optional[int]:
        """Create person with synthetic ID"""
        try:
            person_data = {
                'synthetic_id': synthetic_id,
                'display_name': display_name,
                'normalized_name': normalized_name,
                'court_slug': court_slug,
                'court_slugs': [court_slug],
                'sources': [source],
                'is_judge': True,
                'raw': {'confidence': confidence, 'source': source, 'synthetic': True}
            }

            result = self.supabase.table('people').insert(person_data).execute()
            if result.data:
                LOG.info(f"Created synthetic person {synthetic_id}: {display_name}")
                return result.data[0]['id']
            return None
        except Exception as e:
            LOG.warning(f"Failed to create synthetic person {synthetic_id}: {e}")
            return None

    def _update_person(self, person_id: int, display_name: str, court_slug: str, source: str):
        """Update existing person with new information"""
        try:
            # Get current data
            current = self.supabase.table('people').select('court_slugs,sources').eq('id', person_id).execute()
            if not current.data:
                return

            current_data = current.data[0]
            court_slugs = current_data.get('court_slugs', []) or []
            sources = current_data.get('sources', []) or []

            # Add new court slug if not present
            if court_slug not in court_slugs:
                court_slugs.append(court_slug)

            # Add new source if not present
            if source not in sources:
                sources.append(source)

            # Update
            update_data = {
                'display_name': display_name,
                'court_slugs': court_slugs,
                'sources': sources,
                'updated_at': 'now()'
            }

            self.supabase.table('people').update(update_data).eq('id', person_id).execute()
            LOG.debug(f"Updated person {person_id}: {display_name}")

        except Exception as e:
            LOG.warning(f"Failed to update person {person_id}: {e}")

    def _upgrade_synthetic_to_cl(self, person_id: int, cl_person_id: int, display_name: str, court_slug: str, source: str) -> Optional[int]:
        """Upgrade a synthetic person to have a CourtListener ID"""
        try:
            # Check if another person already has this CL ID
            existing = (self.supabase.table('people')
                       .select('id')
                       .eq('cl_person_id', cl_person_id)
                       .limit(1)
                       .execute())

            if existing.data:
                LOG.warning(f"Cannot upgrade person {person_id}: CL ID {cl_person_id} already exists on person {existing.data[0]['id']}")
                return person_id  # Return original person ID

            # Upgrade the synthetic person
            update_data = {
                'cl_person_id': cl_person_id,
                'display_name': display_name,
                'updated_at': 'now()'
            }

            # Update court_slugs and sources
            current = self.supabase.table('people').select('court_slugs,sources').eq('id', person_id).execute()
            if current.data:
                current_data = current.data[0]
                court_slugs = current_data.get('court_slugs', []) or []
                sources = current_data.get('sources', []) or []

                if court_slug not in court_slugs:
                    court_slugs.append(court_slug)
                if source not in sources:
                    sources.append(source)

                update_data['court_slugs'] = court_slugs
                update_data['sources'] = sources

            self.supabase.table('people').update(update_data).eq('id', person_id).execute()
            LOG.info(f"Upgraded synthetic person {person_id} to CL person {cl_person_id}: {display_name}")
            return person_id

        except Exception as e:
            LOG.warning(f"Failed to upgrade synthetic person {person_id} to CL ID {cl_person_id}: {e}")
            return person_id

    def _resolve_person_matches(self, result: EnrichmentResult, court_slug: str, source: str):
        """Resolve PersonMatch objects to person IDs and create case_people links"""
        # Resolve author
        if result.author:
            # Extract CL person ID if present in raw data
            cl_person_id = None
            if result.author.raw.startswith('author_id:'):
                try:
                    cl_person_id = int(result.author.raw.split(':')[1])
                except (ValueError, IndexError):
                    pass

            person_id = self._resolve_or_create_person(
                result.author.name,
                court_slug,
                source,
                result.author.confidence,
                cl_person_id
            )

            if person_id:
                result.author_person_id = person_id
                result.case_people_links.append({
                    'person_id': person_id,
                    'role': 'author',
                    'source': source,
                    'confidence': result.author.confidence,
                    'raw': {'name': result.author.name, 'raw_text': result.author.raw, 'is_synthetic': cl_person_id is None}
                })

        # Resolve panel members
        panel_ids = []
        for panel_member in result.panel:
            # Split panel text if it contains multiple names
            names = self._split_panel_names(panel_member.name)

            for name in names:
                person_id = self._resolve_or_create_person(
                    name,
                    court_slug,
                    source,
                    panel_member.confidence
                )

                if person_id:
                    panel_ids.append(person_id)
                    result.case_people_links.append({
                        'person_id': person_id,
                        'role': 'panel',
                        'source': source,
                        'confidence': panel_member.confidence,
                        'raw': {'name': name, 'raw_text': panel_member.raw, 'is_synthetic': True}
                    })

        result.panel_person_ids = panel_ids

    def _split_panel_names(self, panel_text: str) -> List[str]:
        """Split panel text into individual names"""
        if not panel_text:
            return []

        # Split on common separators
        names = re.split(r'[;,]|\sand\s', panel_text)
        return [name.strip() for name in names if name.strip() and not is_generic_title(name.strip())]

    def _create_person_from_cluster(self, normalized_name: str, court_slug: str, source: str) -> Optional[int]:
        """Create a new person record from cluster data"""
        try:
            person_data = {
                'display_name': normalized_name,
                'is_judge': True,
                'court_slugs': [court_slug],
                'sources': {
                    'derived_from': source,
                    'created_by': 'people_enrich_worker'
                }
            }

            result = self.supabase.table('people').insert(person_data).execute()
            if result.data:
                person_id = result.data[0]['id']
                LOG.info(f"Created new person: '{normalized_name}' (id={person_id})")
                return person_id

        except Exception as e:
            LOG.warning(f"Failed to create person '{normalized_name}': {e}")

        return None

    def _log_summary(self):
        """Log processing summary"""
        avg_confidence = (self.stats['total_confidence'] / max(1, self.stats['cases_enriched']))

        LOG.info("=== People Enrichment Summary ===")
        LOG.info(f"Cases checked: {self.stats['cases_checked']}")
        LOG.info(f"Cases enriched: {self.stats['cases_enriched']}")
        LOG.info(f"Cluster hits: {self.stats['cluster_hits']}")
        LOG.info(f"Regex hits: {self.stats['regex_hits']}")
        LOG.info(f"LLM calls: {self.stats['llm_calls']}")
        LOG.info(f"LLM cache hits: {self.stats['llm_cache_hits']}")
        LOG.info(f"Per curiam count: {self.stats['per_curiam_count']}")
        LOG.info(f"Average confidence: {avg_confidence:.2f}")
        LOG.info("=== End Summary ===")


def parse_args():
    parser = argparse.ArgumentParser(description="People Enrichment Worker")
    parser.add_argument("--state", default="TX", choices=["TX", "FED"], help="State to process")
    parser.add_argument("--limit", type=int, default=100, help="Max cases to process")
    parser.add_argument("--llm-mode", choices=["off", "assist", "only"], default="assist")
    parser.add_argument("--llm-provider", choices=["gemini", "openai"], default="gemini")
    parser.add_argument("--llm-max-calls-per-run", type=int, default=50)
    parser.add_argument("--llm-threshold", type=float, default=0.7)
    parser.add_argument("--snippet-window", type=int, default=600)
    parser.add_argument("--dry-run", action="store_true", help="Log only, no DB writes")
    parser.add_argument("--force", action="store_true", help="Re-enrich already processed cases")
    parser.add_argument("--include-courts", type=str, default="", help="Comma-separated court slugs to include")
    parser.add_argument("--exclude-courts", type=str, default="", help="Comma-separated court slugs to exclude")
    return parser.parse_args()


def main():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    args = parse_args()

    # Load environment
    load_dotenv()

    # Initialize Supabase client
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    if not supabase_url or not supabase_key:
        LOG.error("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
        return 1

    supabase_client = create_client(supabase_url, supabase_key)

    # Initialize GCS client
    gcs_client = storage.Client()
    bucket_name = os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")

    # Create worker
    worker = PeopleEnrichmentWorker(
        supabase_client=supabase_client,
        gcs_client=gcs_client,
        bucket_name=bucket_name,
        llm_mode=args.llm_mode,
        llm_provider=args.llm_provider,
        llm_max_calls=args.llm_max_calls_per_run,
        llm_threshold=args.llm_threshold,
        snippet_window=args.snippet_window,
        dry_run=args.dry_run
    )

    # Resolve court filters
    try:
        include_courts, exclude_courts = _resolve_courts(args.state, args.include_courts, args.exclude_courts)
        LOG.info(f"People worker court filter: state={args.state} include={include_courts or 'ALL'} exclude={exclude_courts or 'NONE'}")
    except ValueError as e:
        LOG.error(str(e))
        return 1

    # Process cases
    worker.process_cases(args.state, args.limit, include_courts, exclude_courts)


if __name__ == "__main__":
    main()
