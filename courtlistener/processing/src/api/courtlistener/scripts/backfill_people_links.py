#!/usr/bin/env python3
"""
Backfill script to enrich existing cases with people data (authors, panels).

This script processes existing cases that lack people enrichment and:
1. Downloads opinion JSON from GCS
2. Extracts author and panel information
3. Upserts people records to the people table
4. Creates case_people link records
5. Updates cases with convenience fields

Usage:
    python scripts/backfill_people_links.py --limit 50 --dry-run
    python scripts/backfill_people_links.py --limit 100 --state TX
"""

import argparse
import gzip
import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional

import requests
from dotenv import load_dotenv

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))
from client import _upsert_person, _upsert_case_people_links

# Setup logging
LOG = logging.getLogger("backfill_people")
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")

# Load environment
load_dotenv()
API_TOKEN = os.getenv("COURTLISTENER_API_KEY")


def get_supabase_connector():
    """Get Supabase connector"""
    try:
        processing_dir = Path(__file__).parent.parent.parent / "processing"
        sys.path.insert(0, str(processing_dir))
        from storage.supabase_connector import SupabaseConnector
        return SupabaseConnector()
    except Exception as e:
        LOG.error(f"Failed to initialize Supabase connector: {e}")
        return None


def get_gcs_client():
    """Get GCS client"""
    try:
        from google.cloud import storage
        return storage.Client()
    except Exception as e:
        LOG.error(f"Failed to initialize GCS client: {e}")
        return None


def download_opinion_from_gcs(gcs_client, bucket_name: str, gcs_path: str) -> Optional[dict]:
    """Download and parse opinion JSON from GCS"""
    try:
        bucket = gcs_client.bucket(bucket_name)
        blob = bucket.blob(gcs_path)
        
        if not blob.exists():
            LOG.warning(f"GCS blob does not exist: {gcs_path}")
            return None
        
        # Download and decompress
        compressed_data = blob.download_as_bytes()
        json_data = gzip.decompress(compressed_data).decode('utf-8')
        return json.loads(json_data)
        
    except Exception as e:
        LOG.error(f"Failed to download opinion from GCS {gcs_path}: {e}")
        return None


def process_case_people_enrichment(supabase, gcs_client, case_row: dict, dry_run: bool = False) -> bool:
    """Process people enrichment for a single case"""
    case_id = case_row["id"]
    gcs_path = case_row.get("gcs_path")
    
    if not gcs_path:
        LOG.warning(f"Case {case_id} has no GCS path, skipping")
        return False
    
    # Download opinion JSON
    opinion_data = download_opinion_from_gcs(gcs_client, "texas-laws-personalinjury", gcs_path)
    if not opinion_data:
        return False
    
    # Extract people information
    author_id = None
    panel_ids = []
    per_curiam = False
    
    # Extract author
    author_data = opinion_data.get("author")
    if author_data and isinstance(author_data, dict):
        author_id = author_data.get("id")
    elif isinstance(author_data, str) and author_data.isdigit():
        author_id = int(author_data)
    
    # Extract panel
    panel_data = opinion_data.get("panel", [])
    if panel_data:
        for panel_member in panel_data:
            if isinstance(panel_member, dict) and panel_member.get("id"):
                panel_ids.append(panel_member.get("id"))
            elif isinstance(panel_member, str) and panel_member.isdigit():
                panel_ids.append(int(panel_member))
    
    # Check for per curiam
    per_curiam = opinion_data.get("per_curiam", False) or (not author_id and opinion_data.get("type") == "per_curiam")
    
    if not author_id and not panel_ids:
        LOG.debug(f"Case {case_id} has no people data, skipping")
        return False
    
    if dry_run:
        LOG.info(f"[DRY-RUN] Would enrich case {case_id} with author_id={author_id}, panel_ids={panel_ids}, per_curiam={per_curiam}")
        return True
    
    try:
        # Upsert people records
        successful_author_id = None
        if author_id and _upsert_person(supabase, author_id):
            successful_author_id = author_id
        
        successful_panel_ids = []
        for panel_id in panel_ids:
            if _upsert_person(supabase, panel_id):
                successful_panel_ids.append(panel_id)
        
        # Create case_people links
        matter_uid = case_row.get("matter_uid")
        docket_id = case_row.get("docket_id")
        court_slug = case_row.get("court_slug")
        
        _upsert_case_people_links(supabase, case_id, successful_author_id, successful_panel_ids,
                                matter_uid, docket_id, court_slug)
        
        # Update cases table with convenience fields
        case_updates = {
            "author_person_id": successful_author_id,
            "panel_person_ids": successful_panel_ids if successful_panel_ids else None,
            "per_curiam": per_curiam,
            "people_enriched_at": "now()"
        }
        
        supabase.table("cases").update(case_updates).eq("id", case_id).execute()
        
        LOG.info(f"Enriched case {case_id} with {len(successful_panel_ids)} panel members" + 
                (f" and author {successful_author_id}" if successful_author_id else ""))
        return True
        
    except Exception as e:
        LOG.error(f"Failed to enrich case {case_id}: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Backfill people enrichment for existing cases")
    parser.add_argument("--state", default="TX", help="State jurisdiction to process")
    parser.add_argument("--limit", type=int, default=50, help="Maximum number of cases to process")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")
    parser.add_argument("--verbose", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        LOG.setLevel(logging.DEBUG)
    
    # Initialize clients
    supabase = get_supabase_connector()
    if not supabase:
        return 1
    
    gcs_client = get_gcs_client()
    if not gcs_client:
        return 1
    
    # Query cases that need people enrichment
    query = supabase.table("cases").select("*").eq("jurisdiction", args.state).is_("people_enriched_at", "null").not_.is_("gcs_path", "null").limit(args.limit)
    
    result = query.execute()
    cases = result.data or []
    
    LOG.info(f"Found {len(cases)} cases to process for people enrichment")
    
    if args.dry_run:
        LOG.info("DRY-RUN mode: no changes will be made")
    
    # Process each case
    processed = 0
    successful = 0
    
    for case_row in cases:
        processed += 1
        if process_case_people_enrichment(supabase, gcs_client, case_row, args.dry_run):
            successful += 1
    
    LOG.info(f"Processed {processed} cases, {successful} successful enrichments")
    return 0


if __name__ == "__main__":
    sys.exit(main())
