#!/usr/bin/env python3
"""
Validate whether /parties/ is callable (without 403) for a given TX court.

What this checks
----------------
- Iterates opinions for one court (default: txctapp1) from a start date.
- Collects unique dockets until --sample is reached.
- For each docket:
    * Strictly decides if it is a RECAP/Federal docket.
      STRICT RULE: (pacer_case_id or recap_id) AND court_slug in TX federal/BNK set.
    * If RECAP → call /parties/?docket=... and bucket the result:
        - parties_hit (200 + results>0)
        - parties_empty (200 + results==0)
        - parties_403 (HTTPError 403)
        - parties_other (any other error)
      If NOT RECAP → skipped_non_recap
- Prints a CSV line for the court and a TOTAL block with hit rate.

Expected healthy result for a *state* appellate court (e.g., txctapp1):
- recap=0, skipped_non_recap=sample, parties_403=0

If you see recap>0 + parties_403 spikes on a state court, your RECAP detector is too permissive.
"""

from __future__ import annotations

import argparse
import json
import logging
import os
import re
import sys
import time
from pathlib import Path
from typing import Dict, Iterable, Iterator, List, Optional, Set, Tuple

import requests

# -----------------------------------------------------------------------------
# Config & logging
# -----------------------------------------------------------------------------

BASE_URL = "https://www.courtlistener.com/api/rest/v4"
DEFAULT_START_DATE = "2018-01-01"
DEFAULT_COURTS_JSON = "courtlistener_data/courts_full.json"

LOG = logging.getLogger("validate_parties_coverage")
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")

API_TOKEN = os.getenv("COURTLISTENER_API_KEY")
if not API_TOKEN:
    LOG.warning("Environment variable COURTLISTENER_API_KEY is not set.")

SESSION = requests.Session()
SESSION.headers.update({
    "Authorization": f"Token {API_TOKEN}" if API_TOKEN else "",
    "User-Agent": "ai-lex-validator/1.0 (+<EMAIL>)",
})

# TX federal + bankruptcy court slugs (v4)
FEDERAL_TX: Set[str] = {
    "txed", "txnd", "txsd", "txwd",  # District courts
    "txeb", "txnb", "txsb", "txwb",  # Bankruptcy courts
}

# -----------------------------------------------------------------------------
# HTTP utils
# -----------------------------------------------------------------------------

def http_get(url: str, params: Optional[dict] = None, retries: int = 3, backoff: float = 0.7) -> requests.Response:
    for attempt in range(retries):
        r = SESSION.get(url, params=params, timeout=60)
        if r.status_code == 200:
            return r
        if r.status_code in (429, 502, 503, 504) and attempt < retries - 1:
            wait = backoff * (2 ** attempt)
            LOG.warning("HTTP %s on %s; retrying in %.1fs", r.status_code, url, wait)
            time.sleep(wait)
            continue
        r.raise_for_status()
    raise RuntimeError("unreachable")

# -----------------------------------------------------------------------------
# Courts list (re-uses the JSON you already have)
# -----------------------------------------------------------------------------

def load_court_slugs(state: str, courts_json: str) -> List[str]:
    """
    Load court slugs from a pre-fetched JSON.
    Accepts either:
      - {"courts": [{...}]}  OR
      - {"results": [{"slug": "tx..."}, ...]}  OR
      - [ { "slug": "...", "state": "TX", ...}, ... ]
    Uses heuristic: state match OR slug starts with state code.
    """
    p = Path(courts_json)
    low = state.lower()
    slugs: List[str] = []
    if p.exists():
        try:
            data = json.loads(p.read_text())
            if isinstance(data, dict) and "courts" in data:
                courts_list = data["courts"]
            elif isinstance(data, dict) and "results" in data:
                courts_list = data["results"]
            else:
                courts_list = data

            for c in courts_list:
                slug = c.get("slug") or c.get("id") or c.get("court_id")
                if not slug or not isinstance(slug, str):
                    continue
                c_state = (c.get("state") or c.get("state_code") or "").lower()
                if c_state == low or slug.lower().startswith(low):
                    slugs.append(slug)
        except Exception as e:
            LOG.error("Failed reading %s (%s)", p, e)
            slugs = []
    return sorted(set(slugs))

# -----------------------------------------------------------------------------
# Opinion iterator (just enough for validation)
# -----------------------------------------------------------------------------

def iter_opinions_for_court(slug: str, start_date: str) -> Iterator[dict]:
    base = f"{BASE_URL}/opinions/"
    params = {
        "court": slug,
        "decision_date__gte": start_date,
        "page_size": 1000,
        # plain_text not needed for validation; keep response light
    }
    url = base
    while url:
        r = http_get(url, params=params)
        params = None
        data = r.json()
        for op in data.get("results", []):
            yield op
        url = data.get("next")

# -----------------------------------------------------------------------------
# Docket & Parties helpers
# -----------------------------------------------------------------------------

def fetch_docket(docket_ref: Optional[str | int]) -> Optional[dict]:
    if not docket_ref:
        return None
    url = docket_ref if (isinstance(docket_ref, str) and str(docket_ref).startswith("http")) \
        else f"{BASE_URL}/dockets/{docket_ref}/"
    try:
        return http_get(url).json()
    except Exception as e:
        LOG.warning("Failed to fetch docket %s: %s", docket_ref, e)
        return None

def fetch_parties(docket_id_or_url: str | int) -> List[dict]:
    if not docket_id_or_url:
        return []
    if isinstance(docket_id_or_url, str) and docket_id_or_url.startswith("http"):
        m = re.search(r"/dockets/(\d+)/", docket_id_or_url)
        docket_id = m.group(1) if m else docket_id_or_url
    else:
        docket_id = docket_id_or_url

    url = f"{BASE_URL}/parties/"
    params = {"docket": docket_id, "page_size": 1000}
    out: List[dict] = []
    while url:
        r = http_get(url, params=params)
        params = None
        payload = r.json()
        out.extend(payload.get("results", []))
        url = payload.get("next")
    return out

def court_slug_from_docket(dk: dict) -> Optional[str]:
    """
    CourtListener dockets expose `court` as a URL like .../courts/txsd/.
    Extract the slug if present; fall back to dk.get("court_slug") if available.
    """
    c = dk.get("court")
    if isinstance(c, str):
        m = re.search(r"/courts/([^/]+)/", c)
        if m:
            return m.group(1)
    slug = dk.get("court_slug") or dk.get("court_id")
    if isinstance(slug, str):
        return slug
    return None

def is_recap_docket_strict(dk: Optional[dict]) -> bool:
    """
    STRICT gate for RECAP/PACER dockets:
      - Must have a RECAP marker: pacer_case_id or recap_id
      - AND the court must be a TX federal/bankruptcy slug
    This avoids false positives on state appellate courts (e.g., txctapp1).
    """
    if not dk:
        return False
    has_recap_marker = bool(dk.get("pacer_case_id") or dk.get("recap_id"))
    slug = court_slug_from_docket(dk)
    is_tx_federal = (slug in FEDERAL_TX) if slug else False
    return has_recap_marker and is_tx_federal

# -----------------------------------------------------------------------------
# Core validation
# -----------------------------------------------------------------------------

def validate_for_court(slug: str, start_date: str, sample: int, debug: bool = False) -> Dict[str, int]:
    counts = {
        "dockets_seen": 0,
        "recap": 0,
        "skipped_non_recap": 0,
        "parties_hit": 0,
        "parties_empty": 0,
        "parties_403": 0,
        "parties_other": 0,
    }

    seen_docket_ids: Set[int] = set()
    def _pick_docket_id(op: dict) -> Optional[int]:
        # Prefer opinion['docket']; otherwise cluster.docket if present
        dref = op.get("docket")
        if not dref:
            # Some opinions reference the cluster; cluster fetch is avoided here to keep it fast.
            return None
        # Extract numeric ID
        if isinstance(dref, int):
            return dref
        if isinstance(dref, str):
            m = re.search(r"/dockets/(\d+)/", dref)
            if m:
                return int(m.group(1))
        return None

    # Gather unique docket IDs
    for op in iter_opinions_for_court(slug, start_date):
        did = _pick_docket_id(op)
        if not did:
            continue
        if did in seen_docket_ids:
            continue
        seen_docket_ids.add(did)
        counts["dockets_seen"] += 1
        if counts["dockets_seen"] >= sample:
            break

    # Validate parties coverage on the sampled dockets
    for did in list(seen_docket_ids)[:sample]:
        dk = fetch_docket(did)
        if debug and dk:
            print("DBG",
                  dk.get("id"),
                  "pacer=", bool(dk.get("pacer_case_id")),
                  "recap=", bool(dk.get("recap_id")),
                  "court_slug=", court_slug_from_docket(dk))

        if not is_recap_docket_strict(dk):
            counts["skipped_non_recap"] += 1
            continue

        counts["recap"] += 1
        # Only now attempt /parties/
        try:
            parts = fetch_parties(dk.get("id") or did)
            if parts:
                counts["parties_hit"] += 1
            else:
                counts["parties_empty"] += 1
        except requests.HTTPError as e:
            if e.response is not None and e.response.status_code == 403:
                counts["parties_403"] += 1
            else:
                counts["parties_other"] += 1
        except Exception:
            counts["parties_other"] += 1

    return counts

# -----------------------------------------------------------------------------
# CLI
# -----------------------------------------------------------------------------

def parse_args(argv: Optional[List[str]] = None) -> argparse.Namespace:
    ap = argparse.ArgumentParser(description="Validate /parties coverage for a TX court")
    ap.add_argument("--state", default="TX", help="Two-letter state code (default TX)")
    ap.add_argument("--start-date", default=DEFAULT_START_DATE, help="YYYY-MM-DD (default 2018-01-01)")
    ap.add_argument("--sample", type=int, default=120, help="Number of unique dockets to sample (default 120)")
    ap.add_argument("--courts-json", default=DEFAULT_COURTS_JSON, help="Path to courts_full.json")
    ap.add_argument("--court", default="", help="Specific court slug to validate (e.g., txsd). If empty, try txctapp1.")
    ap.add_argument("--debug", action="store_true", help="Print per-docket recap markers & court slugs")
    return ap.parse_args(argv)

def main() -> int:
    args = parse_args()

    slugs = load_court_slugs(args.state, args.courts_json)
    if not slugs:
        LOG.error("No court slugs found for state=%s in %s", args.state, args.courts_json)
        return 1

    # Pick court: user-specified → else prefer txctapp1 for a state-court check → else first available
    court = args.court.strip() or ("txctapp1" if "txctapp1" in slugs else slugs[0])

    LOG.info("Loaded %d court slugs for %s from local JSON.", len(slugs), args.state)
    LOG.info("Validating courts: %s", court)

    totals = {
        "dockets_seen": 0,
        "recap": 0,
        "skipped_non_recap": 0,
        "parties_hit": 0,
        "parties_empty": 0,
        "parties_403": 0,
        "parties_other": 0,
    }

    # header
    print("\n=== Parties coverage (sampled) ===")
    print("court,dockets_seen,recap,skipped_non_recap,parties_hit,parties_empty,parties_403,parties_other")

    cstats = validate_for_court(court, args.start_date, args.sample, debug=args.debug)
    print("{court},{dockets_seen},{recap},{skipped_non_recap},{parties_hit},{parties_empty},{parties_403},{parties_other}".format(
        court=court, **cstats
    ))
    for k in totals:
        totals[k] += cstats[k]

    # totals block
    print("\nTOTAL")
    print(
        "dockets_seen={d}  recap={r}  skipped_non_recap={s}\n"
        "parties_hit={h}  empty={e}  403={f}  other={o}".format(
            d=totals["dockets_seen"], r=totals["recap"], s=totals["skipped_non_recap"],
            h=totals["parties_hit"], e=totals["parties_empty"],
            f=totals["parties_403"], o=totals["parties_other"],
        )
    )
    hit_rate = (totals["parties_hit"] / totals["recap"]) if totals["recap"] else 0.0
    print("hit_rate_on_recap={:.3f}".format(hit_rate))

    return 0

if __name__ == "__main__":
    sys.exit(main())
