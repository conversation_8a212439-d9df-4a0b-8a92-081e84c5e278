#!/usr/bin/env python3
"""
CaseSetManager: Systematic case selection and scaling
Extends existing pipeline with targeted case processing capabilities
"""

import os
import logging
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import json
from pathlib import Path

from supabase import create_client
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
load_dotenv()

@dataclass
class CaseSet:
    """Represents a systematically selected set of cases for processing"""
    set_id: str
    name: str
    description: str
    target_count: int
    selection_criteria: Dict[str, Any]
    case_ids: List[str]
    priority_order: List[str]  # Cases in processing priority order
    created_at: datetime = field(default_factory=datetime.utcnow)
    
    # Progress tracking
    processed_count: int = 0
    successful_count: int = 0
    four_system_count: int = 0  # Cases with complete 4-system coverage
    
    # System coverage tracking
    system_coverage: Dict[str, Set[str]] = field(default_factory=lambda: {
        'supabase': set(),
        'gcs': set(),
        'pinecone': set(),
        'neo4j': set()
    })

@dataclass
class ScalingPlan:
    """Defines systematic scaling strategy from 10 → 100+ cases"""
    phases: List[Dict[str, Any]]
    success_criteria: Dict[str, float]  # e.g., {'four_system_coverage': 0.75}
    quality_gates: Dict[str, Any]
    
    def get_next_target_count(self, current_successful: int) -> Optional[int]:
        """Get next target count based on current success"""
        for phase in self.phases:
            if current_successful < phase['target_count']:
                return phase['target_count']
        return None

class CaseSetManager:
    """
    Manages systematic case selection and scaling for the production pipeline
    Extends existing architecture with targeted processing capabilities
    """
    
    def __init__(self, 
                 supabase_url: str = None,
                 supabase_key: str = None,
                 cache_dir: str = "./case_sets_cache"):
        
        self.supabase = create_client(
            supabase_url or os.getenv("SUPABASE_URL"),
            supabase_key or os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        )
        
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # Define systematic scaling plan
        self.scaling_plan = ScalingPlan(
            phases=[
                {
                    'name': 'pilot_validation',
                    'target_count': 10,
                    'description': '4-system validation with high-quality cases',
                    'success_threshold': 0.75  # 75% must achieve 4-system coverage
                },
                {
                    'name': 'initial_scale',
                    'target_count': 20,
                    'description': 'Double the validated set',
                    'success_threshold': 0.70
                },
                {
                    'name': 'moderate_scale',
                    'target_count': 50,
                    'description': 'Medium-scale validation',
                    'success_threshold': 0.70
                },
                {
                    'name': 'large_scale',
                    'target_count': 100,
                    'description': 'Production-ready scale',
                    'success_threshold': 0.65
                }
            ],
            success_criteria={
                'four_system_coverage': 0.75,
                'substantial_content_rate': 0.80,
                'processing_success_rate': 0.90
            },
            quality_gates={
                'min_text_length': 10000,  # Minimum substantial text
                'max_error_rate': 0.10,
                'required_data_sources': ['api', 'csv', 'cluster']
            }
        )
        
        # Predefined high-quality case sets
        self.predefined_sets = self._initialize_predefined_sets()
    
    def _initialize_predefined_sets(self) -> Dict[str, CaseSet]:
        """Initialize predefined case sets for systematic processing"""
        
        # 10-case pilot set (manually validated)
        pilot_cases = CaseSet(
            set_id="pilot_10_cases",
            name="Pilot 10 Cases",
            description="Manually validated cases for 4-system validation",
            target_count=10,
            selection_criteria={
                'data_availability': 'multi_system',
                'content_quality': 'high',
                'diversity': 'practice_areas_and_sources'
            },
            case_ids=[
                # API cases with GCS files (highest priority)
                "11113702",  # Medical malpractice - already has 75 vectors
                "11113438",  # Civil case - has 3 vectors
                "11113451",  # Business dispute - has 15 vectors  
                "11113701",  # Detention case - no vectors yet
                "11113700",  # Construction dispute - no vectors yet
                
                # CSV cases (medium priority)
                "4697111",   # Historical Texas case
                "4697113",   # Historical Texas case
                "4697115",   # Historical Texas case
                
                # Cluster cases (integration priority)
                "cluster_10646628",  # TX cluster - has 1 vector
                "cluster_10646630"   # TX cluster - has 1 vector
            ],
            priority_order=[
                # Process API cases with existing vectors first
                "11113702", "11113451", "11113438",
                # Then API cases without vectors
                "11113701", "11113700",
                # Then CSV cases
                "4697111", "4697113", "4697115",
                # Finally cluster cases
                "cluster_10646628", "cluster_10646630"
            ]
        )
        
        return {
            "pilot_10_cases": pilot_cases
        }
    
    def get_case_set(self, set_id: str) -> Optional[CaseSet]:
        """Retrieve a specific case set"""
        if set_id in self.predefined_sets:
            return self.predefined_sets[set_id]
        
        # Try loading from cache
        cache_file = self.cache_dir / f"{set_id}.json"
        if cache_file.exists():
            return self._load_case_set_from_cache(cache_file)
        
        return None
    
    def create_expanded_case_set(self, 
                                base_set_id: str, 
                                target_count: int,
                                selection_strategy: str = "quality_prioritized") -> CaseSet:
        """Create expanded case set based on successful base set"""
        
        base_set = self.get_case_set(base_set_id)
        if not base_set:
            raise ValueError(f"Base set {base_set_id} not found")
        
        if target_count <= len(base_set.case_ids):
            return base_set
        
        additional_needed = target_count - len(base_set.case_ids)
        
        logger.info(f"Expanding {base_set_id} from {len(base_set.case_ids)} to {target_count} cases")
        
        # Select additional cases based on strategy
        additional_cases = self._select_additional_cases(
            existing_cases=set(base_set.case_ids),
            count_needed=additional_needed,
            strategy=selection_strategy
        )
        
        expanded_set = CaseSet(
            set_id=f"{base_set_id}_expanded_{target_count}",
            name=f"Expanded {base_set.name} ({target_count} cases)",
            description=f"Expanded from successful {base_set.name}",
            target_count=target_count,
            selection_criteria={
                **base_set.selection_criteria,
                'expansion_strategy': selection_strategy,
                'base_set': base_set_id
            },
            case_ids=base_set.case_ids + additional_cases,
            priority_order=self._create_priority_order(base_set.case_ids + additional_cases)
        )
        
        # Cache the expanded set
        self._save_case_set_to_cache(expanded_set)
        
        return expanded_set
    
    def _select_additional_cases(self, 
                               existing_cases: Set[str], 
                               count_needed: int,
                               strategy: str) -> List[str]:
        """Select additional cases using specified strategy"""
        
        logger.info(f"Selecting {count_needed} additional cases using {strategy} strategy")
        
        if strategy == "quality_prioritized":
            return self._select_quality_prioritized_cases(existing_cases, count_needed)
        elif strategy == "diverse_sources":
            return self._select_diverse_source_cases(existing_cases, count_needed)
        elif strategy == "practice_area_balanced":
            return self._select_practice_area_balanced_cases(existing_cases, count_needed)
        else:
            raise ValueError(f"Unknown selection strategy: {strategy}")
    
    def _select_quality_prioritized_cases(self, 
                                        existing_cases: Set[str], 
                                        count_needed: int) -> List[str]:
        """Select cases prioritizing data quality and availability"""
        
        try:
            # Query for high-quality cases not in existing set
            query = """
            SELECT id, case_name, source, gcs_path, 
                   CASE 
                       WHEN gcs_path IS NOT NULL THEN 3
                       WHEN source = 'courtlistener' THEN 2  
                       ELSE 1
                   END as quality_score
            FROM cases 
            WHERE id NOT IN ({}) 
            AND (
                (source = 'courtlistener' AND gcs_path IS NOT NULL) OR
                (source = 'courtlistener_csv') OR
                (source = 'cluster')
            )
            ORDER BY quality_score DESC, created_at DESC
            LIMIT {}
            """.format(
                ','.join(f"'{case_id}'" for case_id in existing_cases),
                count_needed * 2  # Get more than needed for filtering
            )
            
            response = self.supabase.rpc('execute_sql', {'query': query}).execute()
            
            if response.data:
                selected_cases = []
                for row in response.data:
                    if len(selected_cases) >= count_needed:
                        break
                    if row['id'] not in existing_cases:
                        selected_cases.append(str(row['id']))
                
                logger.info(f"Selected {len(selected_cases)} quality-prioritized cases")
                return selected_cases
            
        except Exception as e:
            logger.error(f"Quality prioritized selection failed: {e}")
        
        # Fallback: simple selection from courtlistener cases
        return self._fallback_case_selection(existing_cases, count_needed)
    
    def _fallback_case_selection(self, existing_cases: Set[str], count_needed: int) -> List[str]:
        """Fallback case selection method"""
        try:
            response = self.supabase.table('cases').select('id').not_.in_('id', list(existing_cases)).limit(count_needed).execute()
            
            if response.data:
                return [str(row['id']) for row in response.data]
        
        except Exception as e:
            logger.error(f"Fallback selection failed: {e}")
        
        return []
    
    def _create_priority_order(self, case_ids: List[str]) -> List[str]:
        """Create processing priority order for cases"""
        
        # Priority logic:
        # 1. API cases with existing vectors
        # 2. API cases with GCS files but no vectors  
        # 3. CSV cases
        # 4. Cluster cases
        
        api_with_vectors = []
        api_without_vectors = []
        csv_cases = []
        cluster_cases = []
        other_cases = []
        
        for case_id in case_ids:
            if case_id.startswith('cluster_'):
                cluster_cases.append(case_id)
            elif case_id in ['11113702', '11113438', '11113451']:  # Known to have vectors
                api_with_vectors.append(case_id)
            elif case_id in ['11113701', '11113700']:  # API without vectors
                api_without_vectors.append(case_id)
            elif case_id.startswith('4697'):  # CSV pattern
                csv_cases.append(case_id)
            else:
                other_cases.append(case_id)
        
        return api_with_vectors + api_without_vectors + csv_cases + cluster_cases + other_cases
    
    def update_case_set_progress(self, 
                               case_set: CaseSet, 
                               case_id: str,
                               processing_result: Dict[str, Any]) -> None:
        """Update case set progress after processing a case"""
        
        case_set.processed_count += 1
        
        if processing_result.get('status') == 'success':
            case_set.successful_count += 1
        
        # Update system coverage
        for system in ['supabase', 'gcs', 'pinecone', 'neo4j']:
            if processing_result.get(f'{system}_stored', False):
                case_set.system_coverage[system].add(case_id)
        
        # Check if this case achieved 4-system coverage
        systems_for_case = sum(1 for system in ['supabase', 'gcs', 'pinecone', 'neo4j']
                              if case_id in case_set.system_coverage[system])
        
        if systems_for_case >= 4:
            case_set.four_system_count += 1
        
        # Save updated progress
        self._save_case_set_to_cache(case_set)
    
    def get_scaling_recommendation(self, case_set: CaseSet) -> Dict[str, Any]:
        """Get recommendation for next scaling step"""
        
        current_success_rate = case_set.successful_count / max(case_set.processed_count, 1)
        four_system_rate = case_set.four_system_count / max(case_set.processed_count, 1)
        
        recommendation = {
            'current_phase': None,
            'ready_for_next_phase': False,
            'next_target_count': None,
            'blocking_issues': [],
            'metrics': {
                'success_rate': current_success_rate,
                'four_system_rate': four_system_rate,
                'processed_count': case_set.processed_count
            }
        }
        
        # Determine current phase
        for phase in self.scaling_plan.phases:
            if case_set.target_count <= phase['target_count']:
                recommendation['current_phase'] = phase['name']
                
                # Check if ready for next phase
                if (current_success_rate >= phase['success_threshold'] and
                    four_system_rate >= self.scaling_plan.success_criteria['four_system_coverage']):
                    recommendation['ready_for_next_phase'] = True
                    recommendation['next_target_count'] = self.scaling_plan.get_next_target_count(case_set.target_count)
                else:
                    # Identify blocking issues
                    if current_success_rate < phase['success_threshold']:
                        recommendation['blocking_issues'].append('Low processing success rate')
                    if four_system_rate < self.scaling_plan.success_criteria['four_system_coverage']:
                        recommendation['blocking_issues'].append('Insufficient 4-system coverage')
                
                break
        
        return recommendation
    
    def _save_case_set_to_cache(self, case_set: CaseSet) -> None:
        """Save case set to cache file"""
        cache_file = self.cache_dir / f"{case_set.set_id}.json"
        
        # Convert sets to lists for JSON serialization
        cache_data = {
            **case_set.__dict__,
            'system_coverage': {
                system: list(cases) for system, cases in case_set.system_coverage.items()
            }
        }
        
        with open(cache_file, 'w') as f:
            json.dump(cache_data, f, indent=2, default=str)
    
    def _load_case_set_from_cache(self, cache_file: Path) -> CaseSet:
        """Load case set from cache file"""
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        # Convert lists back to sets
        cache_data['system_coverage'] = {
            system: set(cases) for system, cases in cache_data['system_coverage'].items()
        }
        
        return CaseSet(**cache_data)

    def get_current_pilot_set(self) -> CaseSet:
        """Get the current pilot case set for immediate use"""
        return self.get_case_set("pilot_10_cases")