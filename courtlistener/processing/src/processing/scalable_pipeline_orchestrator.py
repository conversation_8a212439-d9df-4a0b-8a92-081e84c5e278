#!/usr/bin/env python3
"""
Scalable Pipeline Orchestrator: Complete 10→100+ Case Processing Architecture
Orchestrates systematic processing using existing sophisticated components
"""

import os
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import json
from pathlib import Path

from .case_set_manager import CaseSetManager, CaseSet
from .enhanced_cross_system_validator import EnhancedCrossSystemValidator, ValidationLevel
from .enhanced_graphrag_pipeline import EnhancedGraphRAGPipeline
from .cost_monitor import CostMonitor
from .storage.enhanced_storage_orchestrator import EnhancedStorageOrchestrator
from .storage.supabase_connector import SupabaseConnector
from .storage.gcs_connector import GCSConnector
from .storage.pinecone_connector import PineconeConnector
from .storage.neo4j_connector import Neo4jConnector

from supabase import create_client
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
load_dotenv()

@dataclass
class ProcessingResult:
    """Result of processing a single case through the complete pipeline"""
    case_id: str
    case_name: str
    status: str  # 'success', 'partial', 'error'
    
    # Processing metrics
    processing_time_seconds: float
    chunks_created: int
    entities_extracted: int
    relationships_extracted: int
    vectors_stored: int
    
    # System storage results
    supabase_stored: bool
    gcs_stored: bool
    pinecone_stored: bool
    neo4j_stored: bool
    
    # Quality metrics
    four_system_coverage: bool
    global_uid_registered: bool
    text_quality_score: float
    
    # Cost tracking
    api_costs: Dict[str, float]
    
    # Error tracking
    errors: List[str]
    warnings: List[str]
    
    def __post_init__(self):
        if not hasattr(self, 'api_costs') or self.api_costs is None:
            self.api_costs = {}
        if not hasattr(self, 'errors') or self.errors is None:
            self.errors = []
        if not hasattr(self, 'warnings') or self.warnings is None:
            self.warnings = []

@dataclass
class OrchestrationReport:
    """Complete orchestration report for scalable processing"""
    orchestration_id: str
    case_set_id: str
    timestamp: datetime
    
    # Processing summary
    total_cases: int
    successful_cases: int
    partial_cases: int
    failed_cases: int
    
    # System coverage
    four_system_coverage_count: int
    coverage_rate: float
    
    # Performance metrics
    total_processing_time: float
    average_time_per_case: float
    total_api_costs: Dict[str, float]
    
    # Quality metrics
    average_text_quality: float
    average_entities_per_case: float
    average_vectors_per_case: float
    
    # Scaling readiness
    ready_for_next_phase: bool
    next_recommended_count: Optional[int]
    blocking_issues: List[str]
    
    # Individual results
    case_results: List[ProcessingResult]
    
    # Validation results
    system_validation_passed: bool
    validation_issues: List[str]

class ScalablePipelineOrchestrator:
    """
    Orchestrates systematic case processing from 10→100+ cases
    Coordinates existing sophisticated components for scalable architecture
    """
    
    def __init__(self, 
                 supabase_url: str = None,
                 supabase_key: str = None,
                 enable_cost_monitoring: bool = True,
                 practice_area: str = "personal_injury"):
        
        logger.info("🚀 Initializing Scalable Pipeline Orchestrator...")
        
        self.practice_area = practice_area
        
        # Initialize core components using existing sophisticated infrastructure
        self.case_manager = CaseSetManager()
        
        # Initialize cost monitoring
        self.cost_monitor = CostMonitor() if enable_cost_monitoring else None
        
        # Initialize GraphRAG pipeline (uses existing sophisticated pipeline)
        self.graphrag_pipeline = EnhancedGraphRAGPipeline(
            cost_monitor=self.cost_monitor,
            neo4j_uri=os.getenv("NEO4J_URI"),
            neo4j_user=os.getenv("NEO4J_USER"),
            neo4j_password=os.getenv("NEO4J_PASSWORD"),
            gemini_api_key=os.getenv("GEMINI_API_KEY"),
            voyage_api_key=os.getenv("VOYAGE_API_KEY"),
            practice_area=practice_area,
            enable_schema_discovery=True
        )
        
        # Initialize storage orchestrator
        self.storage_orchestrator = EnhancedStorageOrchestrator(
            supabase_url=supabase_url or os.getenv("SUPABASE_URL"),
            supabase_key=supabase_key or os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        )
        
        # Initialize validation system
        supabase_client = create_client(
            supabase_url or os.getenv("SUPABASE_URL"),
            supabase_key or os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        )
        
        self.validator = EnhancedCrossSystemValidator(
            supabase_client=supabase_client,
            gcs_client=None,  # Will be initialized by storage orchestrator
            pinecone_client=None,  # Will be initialized by storage orchestrator  
            neo4j_client=self.graphrag_pipeline.driver,
            consistency_threshold=0.75,  # 75% consistency for pilot phase
            enable_auto_fix=False
        )
        
        logger.info("✅ Scalable Pipeline Orchestrator initialized")
        logger.info(f"   Practice Area: {practice_area}")
        logger.info(f"   Cost Monitoring: {enable_cost_monitoring}")
        
    async def process_pilot_cases(self, 
                                max_concurrent: int = 3,
                                validation_level: ValidationLevel = ValidationLevel.STANDARD) -> OrchestrationReport:
        """
        Process the pilot 10 cases with complete 4-system validation
        """
        logger.info("🎯 Starting pilot case processing (10 cases)")
        
        orchestration_id = f"pilot_orchestration_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.utcnow()
        
        # Step 1: Get pilot case set
        pilot_case_set = self.case_manager.get_current_pilot_set()
        logger.info(f"📋 Retrieved pilot case set: {len(pilot_case_set.case_ids)} cases")
        
        # Step 2: Pre-processing validation
        logger.info("🔍 Performing pre-processing system validation...")
        validation_report = await self.validator.validate_comprehensive(
            validation_level=validation_level
        )
        
        if not validation_report.passed:
            logger.warning("⚠️ Pre-processing validation issues detected:")
            for issue in validation_report.issues[:3]:
                logger.warning(f"   - {issue.description}")
        
        # Step 3: Process cases systematically
        logger.info(f"⚙️ Processing {len(pilot_case_set.case_ids)} cases with {max_concurrent} workers...")
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_with_semaphore(case_id: str) -> ProcessingResult:
            async with semaphore:
                return await self._process_single_case_complete(case_id, pilot_case_set)
        
        # Process all cases concurrently
        tasks = [process_with_semaphore(case_id) for case_id in pilot_case_set.priority_order]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions and create results
        case_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Case {pilot_case_set.priority_order[i]} failed: {result}")
                error_result = ProcessingResult(
                    case_id=pilot_case_set.priority_order[i],
                    case_name="Error",
                    status="error",
                    processing_time_seconds=0.0,
                    chunks_created=0,
                    entities_extracted=0,
                    relationships_extracted=0,
                    vectors_stored=0,
                    supabase_stored=False,
                    gcs_stored=False,
                    pinecone_stored=False,
                    neo4j_stored=False,
                    four_system_coverage=False,
                    global_uid_registered=False,
                    text_quality_score=0.0,
                    errors=[str(result)]
                )
                case_results.append(error_result)
            else:
                case_results.append(result)
                
                # Update case set progress
                processing_result_dict = {
                    'status': result.status,
                    'supabase_stored': result.supabase_stored,
                    'gcs_stored': result.gcs_stored,
                    'pinecone_stored': result.pinecone_stored,
                    'neo4j_stored': result.neo4j_stored
                }
                self.case_manager.update_case_set_progress(
                    pilot_case_set, result.case_id, processing_result_dict
                )
        
        # Step 4: Post-processing validation
        logger.info("🔍 Performing post-processing validation...")
        post_validation = await self.validator.validate_comprehensive(
            validation_level=validation_level
        )
        
        # Step 5: Generate comprehensive report
        total_time = (datetime.utcnow() - start_time).total_seconds()
        report = self._generate_orchestration_report(
            orchestration_id, pilot_case_set, case_results, total_time,
            validation_report, post_validation
        )
        
        # Step 6: Get scaling recommendation
        scaling_rec = self.case_manager.get_scaling_recommendation(pilot_case_set)
        report.ready_for_next_phase = scaling_rec['ready_for_next_phase']
        report.next_recommended_count = scaling_rec.get('next_target_count')
        report.blocking_issues = scaling_rec.get('blocking_issues', [])
        
        # Step 7: Log comprehensive summary
        self._log_orchestration_summary(report)
        
        return report
    
    async def _process_single_case_complete(self, 
                                          case_id: str, 
                                          case_set: CaseSet) -> ProcessingResult:
        """
        Process a single case through the complete 4-system pipeline
        """
        logger.info(f"🔄 Processing case: {case_id}")
        
        start_time = datetime.utcnow()
        
        try:
            # Step 1: Get case data from Supabase
            case_data = await self._get_case_data(case_id)
            
            if not case_data:
                return ProcessingResult(
                    case_id=case_id,
                    case_name="Not Found",
                    status="error",
                    processing_time_seconds=0.0,
                    chunks_created=0,
                    entities_extracted=0,
                    relationships_extracted=0,
                    vectors_stored=0,
                    supabase_stored=False,
                    gcs_stored=False,
                    pinecone_stored=False,
                    neo4j_stored=False,
                    four_system_coverage=False,
                    global_uid_registered=False,
                    text_quality_score=0.0,
                    errors=["Case not found in Supabase"]
                )
            
            # Step 2: Process through GraphRAG pipeline (uses existing sophisticated pipeline)
            document_for_graphrag = self._prepare_document_for_graphrag(case_data)
            
            logger.info(f"🧠 Processing {case_id} through sophisticated GraphRAG pipeline...")
            graphrag_result = await self.graphrag_pipeline.process_documents(
                documents=[document_for_graphrag],
                batch_size=1,
                enable_caching=True
            )
            
            # Step 3: Store in all systems through orchestrator
            logger.info(f"💾 Storing {case_id} across all 4 systems...")
            storage_result = await self.storage_orchestrator.store_complete_case(
                case_data=case_data,
                graphrag_result=graphrag_result
            )
            
            # Step 4: Validate 4-system storage
            four_system_coverage = (
                storage_result.get('supabase_stored', False) and
                storage_result.get('gcs_stored', False) and  
                storage_result.get('pinecone_stored', False) and
                storage_result.get('neo4j_stored', False)
            )
            
            # Step 5: Calculate quality metrics
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            text_quality = self._calculate_text_quality(case_data)
            
            # Step 6: Extract costs from cost monitor
            api_costs = {}
            if self.cost_monitor:
                current_metrics = self.cost_monitor.get_current_metrics()
                api_costs = {
                    'gemini': current_metrics.get('gemini', {}).get('total_cost', 0.0),
                    'voyage': current_metrics.get('voyage', {}).get('total_cost', 0.0),
                    'total': current_metrics.get('overall', {}).get('total_cost', 0.0)
                }
            
            result = ProcessingResult(
                case_id=case_id,
                case_name=case_data.get('case_name', 'Unknown'),
                status='success',
                processing_time_seconds=processing_time,
                chunks_created=graphrag_result.get('chunks_created', 0),
                entities_extracted=graphrag_result.get('entities_extracted', 0),
                relationships_extracted=graphrag_result.get('relationships_extracted', 0),
                vectors_stored=storage_result.get('vectors_stored', 0),
                supabase_stored=storage_result.get('supabase_stored', False),
                gcs_stored=storage_result.get('gcs_stored', False),
                pinecone_stored=storage_result.get('pinecone_stored', False),
                neo4j_stored=storage_result.get('neo4j_stored', False),
                four_system_coverage=four_system_coverage,
                global_uid_registered=storage_result.get('global_uid_registered', False),
                text_quality_score=text_quality,
                api_costs=api_costs
            )
            
            logger.info(f"✅ Successfully processed {case_id}: "
                       f"{result.entities_extracted} entities, "
                       f"{result.vectors_stored} vectors, "
                       f"4-system: {result.four_system_coverage}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error processing case {case_id}: {e}")
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            return ProcessingResult(
                case_id=case_id,
                case_name="Error",
                status="error",
                processing_time_seconds=processing_time,
                chunks_created=0,
                entities_extracted=0,
                relationships_extracted=0,
                vectors_stored=0,
                supabase_stored=False,
                gcs_stored=False,
                pinecone_stored=False,
                neo4j_stored=False,
                four_system_coverage=False,
                global_uid_registered=False,
                text_quality_score=0.0,
                errors=[str(e)]
            )
    
    async def _get_case_data(self, case_id: str) -> Optional[Dict[str, Any]]:
        """Get case data from Supabase"""
        try:
            # Use the storage orchestrator's Supabase client
            supabase_client = self.storage_orchestrator.supabase
            response = supabase_client.table('cases').select('*').eq('id', case_id).execute()
            
            if response.data:
                return response.data[0]
                
        except Exception as e:
            logger.error(f"Error getting case data for {case_id}: {e}")
        
        return None
    
    def _prepare_document_for_graphrag(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare case data for GraphRAG processing"""
        return {
            'id': case_data.get('id'),
            'case_name': case_data.get('case_name', ''),
            'court': case_data.get('court', {}),
            'date_filed': case_data.get('date_created'),
            'docket_number': case_data.get('docket_number'),
            'plain_text': case_data.get('plain_text', ''),
            'html_lawbox': case_data.get('html_lawbox', ''),
            'panel': case_data.get('panel', []),
            'source': case_data.get('source', 'unknown'),
            'gcs_path': case_data.get('gcs_path')
        }
    
    def _calculate_text_quality(self, case_data: Dict[str, Any]) -> float:
        """Calculate text quality score"""
        text = case_data.get('plain_text', '') or case_data.get('html_lawbox', '')
        
        if not text:
            return 0.0
        
        # Length score
        length_score = min(len(text) / 5000, 1.0) * 0.4
        
        # Legal keywords score
        legal_keywords = ['court', 'opinion', 'judgment', 'plaintiff', 'defendant', 'appeal']
        text_lower = text.lower()
        keyword_count = sum(1 for keyword in legal_keywords if keyword in text_lower)
        keyword_score = min(keyword_count / len(legal_keywords), 1.0) * 0.4
        
        # Structure score (paragraphs)
        paragraph_score = min(text.count('\n\n') / 10, 1.0) * 0.2
        
        return length_score + keyword_score + paragraph_score
    
    def _generate_orchestration_report(self,
                                     orchestration_id: str,
                                     case_set: CaseSet,
                                     case_results: List[ProcessingResult],
                                     total_time: float,
                                     pre_validation: Any,
                                     post_validation: Any) -> OrchestrationReport:
        """Generate comprehensive orchestration report"""
        
        successful_results = [r for r in case_results if r.status == 'success']
        partial_results = [r for r in case_results if r.status == 'partial'] 
        failed_results = [r for r in case_results if r.status == 'error']
        
        four_system_cases = [r for r in case_results if r.four_system_coverage]
        coverage_rate = len(four_system_cases) / len(case_results) if case_results else 0.0
        
        # Calculate averages
        avg_time = total_time / len(case_results) if case_results else 0.0
        avg_quality = sum(r.text_quality_score for r in case_results) / len(case_results) if case_results else 0.0
        avg_entities = sum(r.entities_extracted for r in case_results) / len(case_results) if case_results else 0.0
        avg_vectors = sum(r.vectors_stored for r in case_results) / len(case_results) if case_results else 0.0
        
        # Calculate total costs
        total_costs = {}
        for result in case_results:
            for api, cost in result.api_costs.items():
                total_costs[api] = total_costs.get(api, 0) + cost
        
        return OrchestrationReport(
            orchestration_id=orchestration_id,
            case_set_id=case_set.set_id,
            timestamp=datetime.utcnow(),
            total_cases=len(case_results),
            successful_cases=len(successful_results),
            partial_cases=len(partial_results),
            failed_cases=len(failed_results),
            four_system_coverage_count=len(four_system_cases),
            coverage_rate=coverage_rate,
            total_processing_time=total_time,
            average_time_per_case=avg_time,
            total_api_costs=total_costs,
            average_text_quality=avg_quality,
            average_entities_per_case=avg_entities,
            average_vectors_per_case=avg_vectors,
            ready_for_next_phase=False,  # Will be updated by scaling recommendation
            next_recommended_count=None,
            blocking_issues=[],
            case_results=case_results,
            system_validation_passed=post_validation.passed,
            validation_issues=[issue.description for issue in post_validation.issues]
        )
    
    def _log_orchestration_summary(self, report: OrchestrationReport):
        """Log comprehensive orchestration summary"""
        logger.info("=" * 80)
        logger.info("🎯 SCALABLE PIPELINE ORCHESTRATION SUMMARY")
        logger.info("=" * 80)
        logger.info(f"📋 Orchestration ID: {report.orchestration_id}")
        logger.info(f"📊 Case Set: {report.case_set_id}")
        logger.info(f"📈 Results: {report.successful_cases}S / {report.partial_cases}P / {report.failed_cases}F")
        logger.info(f"⏱️  Total Time: {report.total_processing_time:.1f}s")
        logger.info(f"🎯 4-System Coverage: {report.four_system_coverage_count}/{report.total_cases} ({report.coverage_rate:.1%})")
        logger.info("")
        logger.info("📊 Quality Metrics:")
        logger.info(f"   Average Text Quality: {report.average_text_quality:.2f}")
        logger.info(f"   Average Entities/Case: {report.average_entities_per_case:.1f}")
        logger.info(f"   Average Vectors/Case: {report.average_vectors_per_case:.1f}")
        logger.info(f"   Average Time/Case: {report.average_time_per_case:.1f}s")
        
        if report.total_api_costs:
            logger.info("")
            logger.info("💰 API Costs:")
            for api, cost in report.total_api_costs.items():
                logger.info(f"   {api}: ${cost:.4f}")
        
        logger.info("")
        logger.info("🔄 Scaling Status:")
        if report.ready_for_next_phase:
            logger.info(f"   ✅ Ready for next phase: {report.next_recommended_count} cases")
        else:
            logger.info("   ⏳ Not ready for scaling")
            if report.blocking_issues:
                for issue in report.blocking_issues:
                    logger.info(f"      - {issue}")
        
        if not report.system_validation_passed:
            logger.info("")
            logger.info("⚠️ Validation Issues:")
            for issue in report.validation_issues[:3]:
                logger.info(f"   - {issue}")
        
        logger.info("=" * 80)
    
    async def save_orchestration_report(self, 
                                      report: OrchestrationReport, 
                                      filename: Optional[str] = None) -> str:
        """Save orchestration report to JSON file"""
        
        if filename is None:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"scalable_orchestration_report_{timestamp}.json"
        
        # Convert to JSON-serializable format
        report_dict = asdict(report)
        report_dict['timestamp'] = report.timestamp.isoformat()
        
        with open(filename, 'w') as f:
            json.dump(report_dict, f, indent=2, default=str)
        
        logger.info(f"📄 Orchestration report saved: {filename}")
        return filename
    
    async def close(self):
        """Clean up resources"""
        if hasattr(self.graphrag_pipeline, 'close'):
            self.graphrag_pipeline.close()
        if hasattr(self.storage_orchestrator, 'close'):
            await self.storage_orchestrator.close()
        logger.info("🔒 Scalable Pipeline Orchestrator closed")

# Main execution function
async def run_pilot_orchestration():
    """Run the pilot case orchestration"""
    logger.info("🚀 Starting Pilot Case Orchestration")
    
    try:
        orchestrator = ScalablePipelineOrchestrator(
            practice_area="personal_injury",
            enable_cost_monitoring=True
        )
        
        # Process pilot cases
        report = await orchestrator.process_pilot_cases(
            max_concurrent=3,
            validation_level=ValidationLevel.STANDARD
        )
        
        # Save report
        report_file = await orchestrator.save_orchestration_report(report)
        
        logger.info(f"✅ Pilot orchestration completed!")
        logger.info(f"📄 Report saved: {report_file}")
        logger.info(f"🎯 4-system coverage: {report.coverage_rate:.1%}")
        logger.info(f"🔄 Ready for scaling: {report.ready_for_next_phase}")
        
        # Clean up
        await orchestrator.close()
        
        return report
        
    except Exception as e:
        logger.error(f"❌ Pilot orchestration failed: {e}")
        import traceback
        traceback.print_exc()
        return None