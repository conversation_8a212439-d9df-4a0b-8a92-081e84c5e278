#!/usr/bin/env python3
"""
Legal Entity Role Classifier
Post-processing role refinement for coarse-grained GraphRAG entities

This module implements Phase 2 of the two-phase entity extraction strategy:
1. Phase 1: Coarse extraction (Person, Organization, Case) - COMPLETED
2. Phase 2: Role classification (Judge, Attorney, Plaintiff, etc.) - THIS MODULE

The classifier uses context-aware analysis and lightweight LLM assistance
to assign specific legal roles while maintaining high recall from Phase 1.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from neo4j import GraphDatabase
import re
import json

logger = logging.getLogger(__name__)

class ConfidenceLevel(Enum):
    """Classification confidence levels"""
    HIGH = 0.9      # Rule-based patterns with high certainty
    MEDIUM = 0.7    # Context-based inference
    LOW = 0.5       # LLM-assisted classification
    UNKNOWN = 0.0   # Unable to classify

@dataclass
class RoleClassification:
    """Result of role classification for an entity"""
    entity_name: str
    entity_type: str  # Person, Organization, Case
    assigned_role: str
    confidence: float
    evidence: List[str]  # Supporting evidence from text
    context: str  # Surrounding text that informed classification
    fallback_used: bool = False

@dataclass
class ClassificationPattern:
    """Pattern for rule-based role classification"""
    role: str
    keywords: List[str]
    context_patterns: List[str]
    confidence: float
    exclusion_patterns: List[str] = None

class LegalRoleClassifier:
    """
    Context-aware legal role classifier for GraphRAG entities
    
    Uses a hybrid approach:
    1. Rule-based classification for high-confidence patterns
    2. Context analysis for medium-confidence inference
    3. LLM assistance for ambiguous cases
    """
    
    # Rule-based classification patterns
    PERSON_ROLE_PATTERNS = {
        "Judge": ClassificationPattern(
            role="Judge",
            keywords=["judge", "judicial", "court", "presided", "honorable", "justice"],
            context_patterns=[
                r"judge\s+\w+\s+presided",
                r"honorable\s+\w+",
                r"\w+\s+presided\s+over",
                r"judicial\s+district",
                r"before\s+judge\s+\w+"
            ],
            confidence=0.9
        ),
        "Attorney": ClassificationPattern(
            role="Attorney",
            keywords=["attorney", "lawyer", "counsel", "represented", "law firm", "legal"],
            context_patterns=[
                r"attorney\s+\w+\s+represented",
                r"\w+\s+represented\s+\w+",
                r"counsel\s+for",
                r"law\s+firm",
                r"legal\s+representation"
            ],
            confidence=0.9
        ),
        "Plaintiff": ClassificationPattern(
            role="Plaintiff",
            keywords=["plaintiff", "petitioner", "claimant", "sued", "filed suit"],
            context_patterns=[
                r"plaintiff\s+\w+",
                r"\w+\s+sued\s+\w+",
                r"\w+\s+v\.\s+\w+",  # Case name format
                r"petitioner\s+\w+",
                r"claimant\s+\w+"
            ],
            confidence=0.9
        ),
        "Defendant": ClassificationPattern(
            role="Defendant",
            keywords=["defendant", "respondent", "sued by", "against"],
            context_patterns=[
                r"defendant\s+\w+",
                r"\w+\s+v\.\s+(\w+)",  # Defendant in case name
                r"respondent\s+\w+",
                r"against\s+\w+",
                r"sued\s+\w+"
            ],
            confidence=0.9
        ),
        "Expert": ClassificationPattern(
            role="Expert",
            keywords=["doctor", "dr.", "expert", "specialist", "testified", "medical", "physician"],
            context_patterns=[
                r"dr\.?\s*\w+",  # Dr. or Dr followed by name
                r"doctor\s+\w+",
                r"expert\s+witness",
                r"testified\s+that",
                r"medical\s+expert",
                r"specialist\s+\w+",
                r"\w+\s+testified",  # Name + testified
                r"gastroenterology",  # Medical specialties
                r"surgical\s+\w+"
            ],
            confidence=0.8
        ),
        "Other": ClassificationPattern(
            role="Other",
            keywords=[],
            context_patterns=[],
            confidence=0.5
        )
    }
    
    ORGANIZATION_ROLE_PATTERNS = {
        "Court": ClassificationPattern(
            role="Court",
            keywords=["court", "judicial", "district", "tribunal", "appellate"],
            context_patterns=[
                r"\w+\s+court",
                r"district\s+court",
                r"judicial\s+district",
                r"appellate\s+court",
                r"supreme\s+court"
            ],
            confidence=0.9
        ),
        "Hospital": ClassificationPattern(
            role="Hospital",
            keywords=["hospital", "medical center", "clinic", "healthcare", "health system"],
            context_patterns=[
                r"\w+\s+hospital",
                r"medical\s+center",
                r"healthcare\s+system",
                r"clinic",
                r"health\s+system"
            ],
            confidence=0.9
        ),
        "LawFirm": ClassificationPattern(
            role="LawFirm",
            keywords=["law firm", "legal group", "attorneys", "law office", "legal services", "defense group"],
            context_patterns=[
                r"law\s+firm",
                r"legal\s+group",
                r"attorneys",
                r"law\s+office",
                r"legal\s+services",
                r"defense\s+group",
                r"\w+\s+law\s+firm",
                r"legal\s+representation"
            ],
            confidence=0.9
        ),
        "Insurance": ClassificationPattern(
            role="Insurance",
            keywords=["insurance", "coverage", "policy", "insurer", "underwriter"],
            context_patterns=[
                r"insurance\s+company",
                r"coverage",
                r"policy\s+holder",
                r"insurer",
                r"underwriter"
            ],
            confidence=0.8
        ),
        "Other": ClassificationPattern(
            role="Other",
            keywords=[],
            context_patterns=[],
            confidence=0.5
        )
    }
    
    # Classification patterns for new entity types
    MONETARY_AMOUNT_PATTERNS = {
        "Award": ClassificationPattern(
            role="Award",
            keywords=["awarded", "verdict", "judgment", "jury", "damages"],
            context_patterns=[
                r"jury\s+awarded",
                r"verdict\s+for",
                r"judgment\s+of",
                r"damages\s+in\s+the\s+amount",
                r"court\s+awards"
            ],
            confidence=0.9
        ),
        "Settlement": ClassificationPattern(
            role="Settlement",
            keywords=["settlement", "settled", "agreed", "negotiated"],
            context_patterns=[
                r"settlement\s+of",
                r"settled\s+for",
                r"agreed\s+to\s+pay",
                r"negotiated\s+settlement"
            ],
            confidence=0.9
        ),
        "Damages": ClassificationPattern(
            role="Damages",
            keywords=["damages", "compensation", "compensatory", "punitive"],
            context_patterns=[
                r"actual\s+damages",
                r"punitive\s+damages",
                r"compensatory\s+damages",
                r"damages\s+of"
            ],
            confidence=0.8
        ),
        "Other": ClassificationPattern(
            role="Other",
            keywords=[],
            context_patterns=[],
            confidence=0.5
        )
    }
    
    DATE_PATTERNS = {
        "Filing": ClassificationPattern(
            role="Filing",
            keywords=["filed", "filing", "petition", "complaint"],
            context_patterns=[
                r"filed\s+on",
                r"filing\s+date",
                r"petition\s+filed",
                r"complaint\s+was\s+filed"
            ],
            confidence=0.9
        ),
        "Incident": ClassificationPattern(
            role="Incident",
            keywords=["incident", "accident", "surgery", "treatment", "occurred"],
            context_patterns=[
                r"accident\s+on",
                r"incident\s+occurred",
                r"surgery\s+on",
                r"treatment\s+on",
                r"performed\s+on"
            ],
            confidence=0.9
        ),
        "Decision": ClassificationPattern(
            role="Decision",
            keywords=["verdict", "decision", "judgment", "ruling", "decided"],
            context_patterns=[
                r"verdict\s+on",
                r"decided\s+on",
                r"judgment\s+entered",
                r"court\s+ruled\s+on"
            ],
            confidence=0.9
        ),
        "Proceeding": ClassificationPattern(
            role="Proceeding",
            keywords=["hearing", "trial", "court", "testimony"],
            context_patterns=[
                r"hearing\s+on",
                r"trial\s+began",
                r"court\s+session",
                r"testimony\s+on"
            ],
            confidence=0.8
        ),
        "Other": ClassificationPattern(
            role="Other",
            keywords=[],
            context_patterns=[],
            confidence=0.5
        )
    }
    
    VERDICT_PATTERNS = {
        "Liability": ClassificationPattern(
            role="Liability",
            keywords=["liable", "fault", "negligent", "responsible"],
            context_patterns=[
                r"found\s+liable",
                r"held\s+responsible",
                r"negligent\s+in",
                r"at\s+fault"
            ],
            confidence=0.9
        ),
        "Award": ClassificationPattern(
            role="Award",
            keywords=["awarded", "compensation", "damages"],
            context_patterns=[
                r"jury\s+awarded",
                r"court\s+awards",
                r"entitled\s+to",
                r"compensation\s+of"
            ],
            confidence=0.9
        ),
        "Dismissal": ClassificationPattern(
            role="Dismissal",
            keywords=["dismissed", "denied", "rejected", "unsuccessful"],
            context_patterns=[
                r"case\s+dismissed",
                r"motion\s+denied",
                r"claim\s+rejected",
                r"unsuccessful\s+in"
            ],
            confidence=0.9
        ),
        "Settlement": ClassificationPattern(
            role="Settlement",
            keywords=["settled", "agreement", "resolved"],
            context_patterns=[
                r"case\s+settled",
                r"parties\s+agreed",
                r"dispute\s+resolved",
                r"settlement\s+reached"
            ],
            confidence=0.8
        ),
        "Other": ClassificationPattern(
            role="Other",
            keywords=[],
            context_patterns=[],
            confidence=0.5
        )
    }
    
    def __init__(
        self,
        driver: GraphDatabase.driver,
        llm: Optional[Any] = None,
        enable_llm_fallback: bool = True
    ):
        self.driver = driver
        self.llm = llm
        self.enable_llm_fallback = enable_llm_fallback
        
        # Classification statistics
        self.classification_stats = {
            "rule_based": 0,
            "context_based": 0,
            "llm_assisted": 0,
            "unknown": 0
        }
        
    async def classify_entities_for_run(self, run_id: str) -> List[RoleClassification]:
        """
        Classify all entities from a specific GraphRAG run
        
        Args:
            run_id: GraphRAG run identifier
            
        Returns:
            List of role classifications
        """
        logger.info(f"🏷️  Starting role classification for run: {run_id}")
        
        # Get entities and their context from Neo4j
        entities_with_context = self._get_entities_with_context(run_id)
        
        if not entities_with_context:
            logger.warning(f"No entities found for run {run_id}")
            return []
        
        logger.info(f"Found {len(entities_with_context)} entities to classify")
        
        # Classify each entity
        classifications = []
        for entity_data in entities_with_context:
            classification = await self._classify_single_entity(entity_data)
            classifications.append(classification)
            
            # Update statistics
            if classification.confidence >= 0.9:
                self.classification_stats["rule_based"] += 1
            elif classification.confidence >= 0.7:
                self.classification_stats["context_based"] += 1
            elif classification.confidence >= 0.5:
                self.classification_stats["llm_assisted"] += 1
            else:
                self.classification_stats["unknown"] += 1
        
        # Store classifications back to Neo4j
        self._store_classifications(classifications)
        
        logger.info(f"✅ Completed role classification for {len(classifications)} entities")
        logger.info(f"📊 Classification breakdown: {self.classification_stats}")
        
        return classifications
    
    def _get_entities_with_context(self, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get entities with their surrounding text context for classification"""
        
        with self.driver.session() as session:
            # First, try to get entities with chunk relationships (ideal)
            # Then fallback to using all available chunk text as shared context
            
            # Get all entities
            entity_query = """
            MATCH (entity:__KGBuilder__)
            WHERE NOT entity:Chunk 
            AND entity.name IS NOT NULL
            
            // Get relationships for role inference
            OPTIONAL MATCH (entity)-[r]-(related:__KGBuilder__)
            WHERE NOT related:Chunk AND related.name IS NOT NULL
            
            RETURN DISTINCT 
                entity.name as entity_name,
                [label IN labels(entity) WHERE label <> '__KGBuilder__'][0] as entity_type,
                collect(DISTINCT {
                    type: type(r),
                    related_name: related.name,
                    related_type: [label IN labels(related) WHERE label <> '__KGBuilder__'][0]
                }) as relationships
            ORDER BY entity.name
            LIMIT 50
            """
            
            entities_result = session.run(entity_query)
            entities = []
            
            for record in entities_result:
                entities.append({
                    "name": record["entity_name"],
                    "type": record["entity_type"],
                    "relationships": record["relationships"]
                })
            
            # Get all chunk text as shared context (since FROM_CHUNK links are missing)
            chunks_query = """
            MATCH (chunk:Chunk)
            WHERE chunk.text IS NOT NULL
            RETURN chunk.text as text
            ORDER BY chunk.text
            LIMIT 10
            """
            
            chunks_result = session.run(chunks_query)
            all_chunks = []
            for record in chunks_result:
                if record["text"]:
                    all_chunks.append(record["text"])
            
            # Combine all chunk text as shared context
            shared_context = " ".join(all_chunks)
            
            # Assign shared context to all entities
            for entity in entities:
                entity["context"] = shared_context
            
            return entities
    
    async def _classify_single_entity(self, entity_data: Dict[str, Any]) -> RoleClassification:
        """Classify a single entity using the hybrid approach"""
        
        entity_name = entity_data["name"]
        entity_type = entity_data["type"]
        context = entity_data["context"]
        relationships = entity_data["relationships"]
        
        logger.debug(f"Classifying {entity_type}: {entity_name}")
        
        # Step 1: Try rule-based classification
        rule_classification = self._classify_by_rules(entity_name, entity_type, context)
        if rule_classification.confidence >= 0.8:
            logger.debug(f"Rule-based classification: {rule_classification.assigned_role} (confidence: {rule_classification.confidence})")
            return rule_classification
        
        # Step 2: Try context-based classification using relationships
        context_classification = self._classify_by_context(entity_name, entity_type, context, relationships)
        if context_classification.confidence >= 0.7:
            logger.debug(f"Context-based classification: {context_classification.assigned_role} (confidence: {context_classification.confidence})")
            return context_classification
        
        # Step 3: LLM-assisted classification for ambiguous cases
        if self.enable_llm_fallback and self.llm:
            llm_classification = await self._classify_by_llm(entity_name, entity_type, context, relationships)
            if llm_classification.confidence >= 0.5:
                logger.debug(f"LLM-assisted classification: {llm_classification.assigned_role} (confidence: {llm_classification.confidence})")
                return llm_classification
        
        # Fallback: Use default "Other" classification
        fallback_role = "Other"
        return RoleClassification(
            entity_name=entity_name,
            entity_type=entity_type,
            assigned_role=fallback_role,
            confidence=0.3,
            evidence=["No classification pattern matched"],
            context=context[:200] + "..." if len(context) > 200 else context,
            fallback_used=True
        )
    
    def _classify_by_rules(self, entity_name: str, entity_type: str, context: str) -> RoleClassification:
        """Rule-based classification using keyword patterns"""
        
        # Get appropriate patterns for entity type
        # Handle both "Person" and any organization-like types (Organization, __Entity__, etc.)
        if entity_type == "Person":
            patterns = self.PERSON_ROLE_PATTERNS
        elif entity_type in ["Organization", "__Entity__"]:  # Handle GraphRAG's __Entity__ label
            patterns = self.ORGANIZATION_ROLE_PATTERNS
        elif entity_type == "Case":
            # For Case entities, no role classification needed
            return RoleClassification(
                entity_name=entity_name,
                entity_type=entity_type,
                assigned_role="Case",  # Cases keep their type as role
                confidence=1.0,
                evidence=["Entity type is Case"],
                context=context[:200] + "..." if len(context) > 200 else context
            )
        elif entity_type == "MonetaryAmount":
            patterns = self.MONETARY_AMOUNT_PATTERNS
        elif entity_type == "Date":
            patterns = self.DATE_PATTERNS
        elif entity_type == "Verdict":
            patterns = self.VERDICT_PATTERNS
        else:
            # For unknown entity types, try to infer from name and context
            logger.debug(f"Unknown entity type '{entity_type}' for {entity_name}, attempting inference")
            
            # Check if it's likely a person (has name patterns)
            if self._looks_like_person_name(entity_name):
                patterns = self.PERSON_ROLE_PATTERNS
            else:
                patterns = self.ORGANIZATION_ROLE_PATTERNS
        
        # Combine entity name and context for pattern matching
        text_to_analyze = f"{entity_name.lower()} {context.lower()}"
        
        best_match = None
        best_confidence = 0.0
        best_evidence = []
        
        # Check each pattern
        for role, pattern in patterns.items():
            if role == "Other":  # Skip "Other" in initial matching
                continue
                
            evidence = []
            confidence_score = 0.0
            
            # Check keyword matches
            keyword_matches = 0
            for keyword in pattern.keywords:
                if keyword.lower() in text_to_analyze:
                    keyword_matches += 1
                    evidence.append(f"Keyword: {keyword}")
            
            if keyword_matches > 0:
                confidence_score += (keyword_matches / len(pattern.keywords)) * 0.6
            
            # Check regex pattern matches
            pattern_matches = 0
            for regex_pattern in pattern.context_patterns:
                if re.search(regex_pattern, text_to_analyze, re.IGNORECASE):
                    pattern_matches += 1
                    evidence.append(f"Pattern: {regex_pattern}")
            
            if pattern_matches > 0:
                confidence_score += (pattern_matches / len(pattern.context_patterns)) * 0.4
            
            # Apply base confidence
            final_confidence = confidence_score * pattern.confidence
            
            if final_confidence > best_confidence:
                best_match = role
                best_confidence = final_confidence
                best_evidence = evidence
        
        # Return best match or default to "Other"
        assigned_role = best_match if best_match else "Other"
        final_confidence = best_confidence if best_match else 0.3
        
        return RoleClassification(
            entity_name=entity_name,
            entity_type=entity_type,
            assigned_role=assigned_role,
            confidence=final_confidence,
            evidence=best_evidence if best_evidence else ["Default classification"],
            context=context[:200] + "..." if len(context) > 200 else context
        )
    
    def _looks_like_person_name(self, name: str) -> bool:
        """Determine if a name looks like a person's name"""
        
        # Common person name patterns
        person_indicators = [
            # Title patterns
            r"\b(dr\.?|doctor|judge|attorney|mr\.?|ms\.?|mrs\.?)\s+\w+",
            # First Last name pattern (two capitalized words)
            r"^[A-Z][a-z]+\s+[A-Z][a-z]+$",
            # First Middle Last pattern
            r"^[A-Z][a-z]+\s+[A-Z]\.?\s+[A-Z][a-z]+$",
            # Name with suffix
            r"^[A-Z][a-z]+\s+[A-Z][a-z]+\s+(Jr\.?|Sr\.?|III|II)$"
        ]
        
        name_lower = name.lower().strip()
        
        # Check patterns
        for pattern in person_indicators:
            if re.search(pattern, name, re.IGNORECASE):
                return True
        
        # Check for common organizational words (negative indicators)
        org_words = ["hospital", "court", "firm", "group", "company", "insurance", 
                     "center", "clinic", "system", "services", "district", "county"]
        
        if any(org_word in name_lower for org_word in org_words):
            return False
        
        # If it has 2-3 capitalized words with no org words, likely a person
        words = name.split()
        if 2 <= len(words) <= 3:
            capitalized_words = [w for w in words if w and w[0].isupper()]
            if len(capitalized_words) == len(words):
                return True
        
        return False
    
    def _classify_by_context(self, entity_name: str, entity_type: str, context: str, relationships: List[Dict]) -> RoleClassification:
        """Context-based classification using relationship patterns"""
        
        evidence = []
        confidence_boost = 0.0
        suggested_role = None
        
        # Analyze relationships for role clues
        for rel in relationships:
            rel_type = rel.get("type", "")
            related_name = rel.get("related_name", "")
            related_type = rel.get("related_type", "")
            
            # Judge indicators
            if rel_type == "PRESIDED_OVER":
                suggested_role = "Judge"
                confidence_boost = 0.8
                evidence.append(f"Presided over relationship with {related_name}")
            
            # Attorney indicators
            elif rel_type == "REPRESENTED":
                suggested_role = "Attorney"
                confidence_boost = 0.8
                evidence.append(f"Represented {related_name}")
            
            # Plaintiff indicators
            elif rel_type == "SUED" and entity_type == "Person":
                suggested_role = "Plaintiff"
                confidence_boost = 0.7
                evidence.append(f"Sued {related_name}")
            
            # Hospital/Medical organization indicators
            elif "hospital" in entity_name.lower() or "medical" in entity_name.lower():
                if entity_type in ["Organization", "__Entity__"]:
                    suggested_role = "Hospital"
                    confidence_boost = 0.7
                    evidence.append("Hospital-related organization name")
            
            # Law firm indicators
            elif any(word in entity_name.lower() for word in ["law firm", "legal", "defense group"]):
                if entity_type in ["Organization", "__Entity__"]:
                    suggested_role = "LawFirm"
                    confidence_boost = 0.8
                    evidence.append("Law firm related organization name")
            
            # Insurance indicators 
            elif "insurance" in entity_name.lower():
                if entity_type in ["Organization", "__Entity__"]:
                    suggested_role = "Insurance"
                    confidence_boost = 0.8
                    evidence.append("Insurance company name")
            
            # Court indicators
            elif any(word in entity_name.lower() for word in ["court", "judicial", "district"]):
                if entity_type in ["Organization", "__Entity__"]:
                    suggested_role = "Court"
                    confidence_boost = 0.8
                    evidence.append("Court organization name")
        
        # Check for deterministically-injected entities (highest confidence)
        with self.driver.session() as session:
            # Check for metadata-injected courts
            metadata_court_check = session.run("""
                MATCH (n:__KGBuilder__)
                WHERE n.name = $entity_name 
                AND n.entity_source = 'metadata_injection'
                RETURN n.entity_source as source
                LIMIT 1
            """, entity_name=entity_name)
            
            metadata_result = metadata_court_check.single()
            if metadata_result:
                suggested_role = "Court"
                confidence_boost = 0.95  # Very high confidence for metadata injection
                evidence.append("Deterministic court extraction from metadata")
            
            # Check for regex-injected entities (monetary amounts, dates)
            regex_check = session.run("""
                MATCH (n:__KGBuilder__)
                WHERE n.name = $entity_name 
                AND n.entity_source = 'regex_injection'
                RETURN n.entity_source as source, 
                       n.monetary_type as monetary_type,
                       n.date_type as date_type,
                       [label IN labels(n) WHERE label <> '__KGBuilder__'][0] as entity_label
                LIMIT 1
            """, entity_name=entity_name)
            
            regex_result = regex_check.single()
            if regex_result:
                entity_label = regex_result["entity_label"]
                if entity_label == "MonetaryAmount":
                    monetary_type = regex_result["monetary_type"]
                    suggested_role = monetary_type.capitalize() if monetary_type else "Award"
                    confidence_boost = 0.95  # Very high confidence for regex extraction
                    evidence.append(f"Deterministic monetary amount extraction ({monetary_type})")
                elif entity_label == "Date":
                    date_type = regex_result["date_type"]
                    suggested_role = date_type.capitalize() if date_type else "Event"
                    confidence_boost = 0.95  # Very high confidence for regex extraction  
                    evidence.append(f"Deterministic date extraction ({date_type})")
        
        # Additional name-based classification for experts/doctors
        if not suggested_role and entity_type == "Person":
            # Check for doctor/expert indicators in the name or context
            name_and_context = f"{entity_name.lower()} {context.lower()}"
            
            # Expert/Doctor patterns
            if any(pattern in name_and_context for pattern in [
                "dr.", "doctor", "testified", "expert witness", "specialist", 
                "gastroenterology", "medical", "physician"
            ]):
                suggested_role = "Expert"
                confidence_boost = 0.7
                evidence.append("Medical/expert terminology in name or context")
            
            # Additional expert indicators from context
            elif any(phrase in context.lower() for phrase in [
                "testified that", "expert witness", "specialist from", 
                "performed by", "standard of care"
            ]):
                suggested_role = "Expert"
                confidence_boost = 0.6
                evidence.append("Expert context indicators")
        
        # If no relationship-based suggestion, fall back to rule-based
        if not suggested_role:
            return self._classify_by_rules(entity_name, entity_type, context)
        
        return RoleClassification(
            entity_name=entity_name,
            entity_type=entity_type,
            assigned_role=suggested_role,
            confidence=confidence_boost,
            evidence=evidence,
            context=context[:200] + "..." if len(context) > 200 else context
        )
    
    async def _classify_by_llm(self, entity_name: str, entity_type: str, context: str, relationships: List[Dict]) -> RoleClassification:
        """LLM-assisted classification for ambiguous cases"""
        
        # Create prompt for LLM classification
        rel_summary = ", ".join([f"{r['type']}: {r['related_name']}" for r in relationships[:3]])
        
        prompt = f"""
        Classify the legal role of this entity in a Texas court case:
        
        Entity: {entity_name}
        Type: {entity_type}
        Context: {context[:300]}
        Relationships: {rel_summary}
        
        For Person entities, classify as: Judge, Attorney, Plaintiff, Defendant, Expert, Other
        For Organization entities, classify as: Court, Hospital, LawFirm, Insurance, Other
        For Case entities, classify as: Case
        
        Respond with JSON: {{"role": "role_name", "confidence": 0.0-1.0, "reasoning": "explanation"}}
        """
        
        try:
            # Use LLM to classify (implementation depends on LLM interface)
            # This is a placeholder for actual LLM integration
            response = await self._call_llm_for_classification(prompt)
            
            if response and "role" in response:
                return RoleClassification(
                    entity_name=entity_name,
                    entity_type=entity_type,
                    assigned_role=response["role"],
                    confidence=min(response.get("confidence", 0.5), 0.8),  # Cap LLM confidence
                    evidence=[f"LLM reasoning: {response.get('reasoning', 'No reasoning provided')}"],
                    context=context[:200] + "..." if len(context) > 200 else context,
                    fallback_used=True
                )
        
        except Exception as e:
            logger.warning(f"LLM classification failed for {entity_name}: {e}")
        
        # Fallback if LLM fails
        return RoleClassification(
            entity_name=entity_name,
            entity_type=entity_type,
            assigned_role="Other",
            confidence=0.3,
            evidence=["LLM classification failed"],
            context=context[:200] + "..." if len(context) > 200 else context,
            fallback_used=True
        )
    
    async def _call_llm_for_classification(self, prompt: str) -> Optional[Dict[str, Any]]:
        """Call LLM for entity classification (placeholder implementation)"""
        
        # This would integrate with the actual LLM interface
        # For now, return None to use fallback classification
        
        # Example implementation if using VertexAI LLM:
        # if hasattr(self.llm, 'ainvoke'):
        #     response = await self.llm.ainvoke(prompt)
        #     return json.loads(response.content)
        
        return None
    
    def _store_classifications(self, classifications: List[RoleClassification]) -> None:
        """Store role classifications back to Neo4j as additional properties"""
        
        with self.driver.session() as session:
            for classification in classifications:
                # Add role and confidence as properties to the entity
                session.run("""
                    MATCH (entity:__KGBuilder__)
                    WHERE entity.name = $entity_name
                    AND NOT entity:Chunk
                    SET entity.legal_role = $role,
                        entity.role_confidence = $confidence,
                        entity.role_evidence = $evidence,
                        entity.classification_method = $method
                """, 
                    entity_name=classification.entity_name,
                    role=classification.assigned_role,
                    confidence=classification.confidence,
                    evidence=classification.evidence,
                    method="rule_based" if classification.confidence >= 0.8 else 
                           "context_based" if classification.confidence >= 0.7 else 
                           "llm_assisted" if not classification.fallback_used else "fallback"
                )
        
        logger.info(f"✅ Stored {len(classifications)} role classifications to Neo4j")
    
    def get_classification_summary(self) -> Dict[str, Any]:
        """Get summary of classification results"""
        
        total_classified = sum(self.classification_stats.values())
        
        return {
            "total_entities_classified": total_classified,
            "classification_breakdown": self.classification_stats,
            "confidence_distribution": {
                "high_confidence": self.classification_stats["rule_based"],
                "medium_confidence": self.classification_stats["context_based"], 
                "low_confidence": self.classification_stats["llm_assisted"],
                "unknown": self.classification_stats["unknown"]
            },
            "success_rate": (total_classified - self.classification_stats["unknown"]) / max(total_classified, 1)
        }
    
    async def validate_classifications(self, run_id: Optional[str] = None) -> Dict[str, Any]:
        """Validate classification results and return quality metrics"""
        
        with self.driver.session() as session:
            # Get classified entities
            query = """
            MATCH (entity:__KGBuilder__)
            WHERE entity.legal_role IS NOT NULL
            AND NOT entity:Chunk
            RETURN entity.name as name,
                   [label IN labels(entity) WHERE label <> '__KGBuilder__'][0] as type,
                   entity.legal_role as role,
                   entity.role_confidence as confidence,
                   entity.classification_method as method
            ORDER BY entity.role_confidence DESC
            LIMIT 50
            """
            
            result = session.run(query)
            classified_entities = []
            
            for record in result:
                classified_entities.append({
                    "name": record["name"],
                    "type": record["type"], 
                    "role": record["role"],
                    "confidence": record["confidence"],
                    "method": record["method"]
                })
        
        # Calculate validation metrics
        high_confidence_count = len([e for e in classified_entities if e["confidence"] >= 0.8])
        medium_confidence_count = len([e for e in classified_entities if 0.7 <= e["confidence"] < 0.8])
        low_confidence_count = len([e for e in classified_entities if 0.5 <= e["confidence"] < 0.7])
        
        return {
            "total_classified": len(classified_entities),
            "confidence_breakdown": {
                "high": high_confidence_count,
                "medium": medium_confidence_count,
                "low": low_confidence_count
            },
            "average_confidence": sum(e["confidence"] for e in classified_entities) / max(len(classified_entities), 1),
            "method_breakdown": {
                method: len([e for e in classified_entities if e["method"] == method])
                for method in set(e["method"] for e in classified_entities if e["method"])
            },
            "sample_classifications": classified_entities[:10]
        }


async def main():
    """Test the role classifier with existing GraphRAG entities"""
    
    from dotenv import load_dotenv
    load_dotenv()
    
    # Initialize Neo4j connection
    driver = GraphDatabase.driver(
        os.getenv("NEO4J_URI"),
        auth=(os.getenv("NEO4J_USERNAME", "neo4j"), os.getenv("NEO4J_PASSWORD"))
    )
    
    # Create classifier
    classifier = LegalRoleClassifier(driver=driver, enable_llm_fallback=False)
    
    try:
        # Classify entities (no specific run_id since SimpleKGPipeline doesn't use them)
        classifications = await classifier.classify_entities_for_run(None)
        
        print(f"✅ Classified {len(classifications)} entities")
        
        # Show sample results
        print("\n🏷️  Sample Classifications:")
        for classification in classifications[:10]:
            print(f"   {classification.entity_name} ({classification.entity_type}) → {classification.assigned_role}")
            print(f"      Confidence: {classification.confidence:.2f}")
            print(f"      Evidence: {', '.join(classification.evidence[:2])}")
            print()
        
        # Get summary
        summary = classifier.get_classification_summary()
        print(f"📊 Classification Summary:")
        print(f"   Total classified: {summary['total_entities_classified']}")
        print(f"   Success rate: {summary['success_rate']:.1%}")
        print(f"   High confidence: {summary['confidence_distribution']['high_confidence']}")
        print(f"   Medium confidence: {summary['confidence_distribution']['medium_confidence']}")
        
        # Validate results
        validation = await classifier.validate_classifications()
        print(f"\n🎯 Validation Results:")
        print(f"   Average confidence: {validation['average_confidence']:.2f}")
        print(f"   Method breakdown: {validation['method_breakdown']}")
        
    finally:
        driver.close()

if __name__ == "__main__":
    import os
    asyncio.run(main())