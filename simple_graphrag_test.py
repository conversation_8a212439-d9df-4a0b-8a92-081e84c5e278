#!/usr/bin/env python3
"""
Simple GraphRAG Test
Bypass the validation and see what the raw pipeline returns
"""

import asyncio
import os
import logging
from dotenv import load_dotenv

load_dotenv()

async def test_raw_graphrag_pipeline():
    """Test the raw GraphRAG pipeline without our wrapper validation"""
    print("=== Testing Raw GraphRAG Pipeline ===\n")
    
    try:
        # Enable debug logging
        logging.basicConfig(level=logging.INFO)
        
        from neo4j_graphrag.experimental.pipeline import Pipeline
        from neo4j_graphrag.llm import VertexAILLM
        from neo4j_graphrag.experimental.components.kg_builder import SimpleKGBuilder
        from neo4j_graphrag.experimental.components.text_splitters.langchain import LangChainTextSplitterAdapter
        from neo4j_graphrag.experimental.components.embedder import VertexAIEmbedder
        from neo4j_graphrag.experimental.components.resolver import SinglePropertyExactMatchResolver
        from neo4j_graphrag.experimental.components.writer import Neo4jWriter
        from neo4j_graphrag.embeddings import VertexAIEmbeddings
        from neo4j import GraphDatabase
        from langchain.text_splitter import RecursiveCharacterTextSplitter
        
        # Initialize components directly
        print("1. Initializing LLM...")
        llm = VertexAILLM(
            model_name="gemini-2.5-pro",
            project_id=os.getenv("VERTEX_PROJECT_ID"),
            location="us-central1"
        )
        
        print("2. Initializing embedder...")
        embedder = VertexAIEmbeddings(
            project_id=os.getenv("VERTEX_PROJECT_ID"),
            location="us-central1"
        )
        
        print("3. Initializing Neo4j driver...")
        driver = GraphDatabase.driver(
            os.getenv("NEO4J_URI"),
            auth=(os.getenv("NEO4J_USER"), os.getenv("NEO4J_PASSWORD"))
        )
        
        print("4. Creating text splitter...")
        text_splitter = LangChainTextSplitterAdapter(
            text_splitter=RecursiveCharacterTextSplitter(
                chunk_size=1000,
                chunk_overlap=100
            )
        )
        
        print("5. Setting up schema...")
        from neo4j_graphrag.experimental.components.schema import SchemaConfig
        from neo4j_graphrag.experimental.components.types import NodeType, RelationshipType
        
        # Simple schema
        schema = SchemaConfig(
            node_types=[
                NodeType(label="Case", description="Legal cases"),
                NodeType(label="Judge", description="Judges"),
                NodeType(label="Attorney", description="Attorneys"),
                NodeType(label="Plaintiff", description="Plaintiffs"),
                NodeType(label="Defendant", description="Defendants"),
                NodeType(label="Damages", description="Damages")
            ],
            relationship_types=[
                RelationshipType(label="PRESIDED_OVER", description="Judge presided over case"),
                RelationshipType(label="REPRESENTED", description="Attorney represented party"),
                RelationshipType(label="AWARDED", description="Damages awarded"),
                RelationshipType(label="OPPOSED", description="Parties opposed")
            ]
        )
        
        print("6. Creating KG builder...")
        kg_builder = SimpleKGBuilder(
            llm=llm,
            create_lexical_graph=True
        )
        
        print("7. Creating entity resolver...")
        resolver = SinglePropertyExactMatchResolver()
        
        print("8. Creating Neo4j writer...")
        writer = Neo4jWriter(driver=driver, neo4j_database="neo4j")
        
        print("9. Building pipeline...")
        pipeline = Pipeline()
        pipeline.add_component(text_splitter, "splitter")
        pipeline.add_component(kg_builder, "kg_builder")
        pipeline.add_component(resolver, "resolver")
        pipeline.add_component(writer, "writer")
        
        # Connect components
        pipeline.connect("splitter", "kg_builder", input_config={"chunks": "splitter.chunks"})
        pipeline.connect("kg_builder", "resolver", input_config={"graph": "kg_builder.graph"})
        pipeline.connect("resolver", "writer", input_config={"graph": "resolver.graph"})
        
        print("10. Running pipeline...")
        
        # Test text
        test_text = """
        In Smith v. Jones Auto Repair, plaintiff Mary Smith filed suit against Jones Auto Repair 
        for negligent vehicle maintenance. Judge Robert Wilson presided over the case. 
        Attorney Sarah Davis represented the plaintiff. The jury awarded $35,000 in damages.
        """
        
        # Run pipeline
        result = await pipeline.run(
            text=test_text,
            schema=schema
        )
        
        print(f"\n✅ Raw Pipeline Results:")
        print(f"   Result type: {type(result)}")
        print(f"   Result keys: {list(result.keys()) if hasattr(result, 'keys') else 'No keys'}")
        
        # Try to extract entities and relationships
        if hasattr(result, 'graph') or 'graph' in result:
            graph = result.graph if hasattr(result, 'graph') else result['graph']
            print(f"   Graph: {graph}")
            
            if hasattr(graph, 'nodes'):
                print(f"   Raw nodes: {len(graph.nodes)}")
                for node in graph.nodes[:5]:  # Show first 5
                    print(f"      - {node}")
            
            if hasattr(graph, 'relationships'):
                print(f"   Raw relationships: {len(graph.relationships)}")
                for rel in graph.relationships[:5]:  # Show first 5
                    print(f"      - {rel}")
        
        return result
        
    except Exception as e:
        print(f"❌ Raw pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_minimal_graphrag():
    """Test minimal GraphRAG setup"""
    print("\n=== Testing Minimal GraphRAG ===\n")
    
    try:
        # Just test the KG builder component by itself
        from neo4j_graphrag.experimental.components.kg_builder import SimpleKGBuilder
        from neo4j_graphrag.llm import VertexAILLM
        from neo4j_graphrag.experimental.components.schema import SchemaConfig
        from neo4j_graphrag.experimental.components.types import NodeType, RelationshipType
        
        print("1. Setting up minimal components...")
        
        llm = VertexAILLM(
            model_name="gemini-2.5-pro",
            project_id=os.getenv("VERTEX_PROJECT_ID"),
            location="us-central1"
        )
        
        schema = SchemaConfig(
            node_types=[
                NodeType(label="Person", description="People"),
                NodeType(label="Organization", description="Organizations"),
                NodeType(label="Case", description="Legal cases")
            ],
            relationship_types=[
                RelationshipType(label="WORKS_FOR", description="Person works for organization"),
                RelationshipType(label="INVOLVED_IN", description="Person involved in case")
            ]
        )
        
        kg_builder = SimpleKGBuilder(llm=llm)
        
        print("2. Testing KG builder directly...")
        
        # Create a simple chunk
        from neo4j_graphrag.experimental.components.types import LexicalGraphConfig, TextChunk
        
        chunk = TextChunk(
            text="Judge Robert Wilson presided over Smith v. Jones. Attorney Sarah Davis represented the plaintiff.",
            index=0
        )
        
        # Test the KG builder
        result = await kg_builder.run(
            chunks=[chunk],
            schema=schema,
            lexical_graph_config=LexicalGraphConfig()
        )
        
        print(f"✅ KG Builder Results:")
        print(f"   Result type: {type(result)}")
        print(f"   Has graph: {hasattr(result, 'graph')}")
        
        if hasattr(result, 'graph') and result.graph:
            graph = result.graph
            print(f"   Nodes: {len(graph.nodes) if hasattr(graph, 'nodes') else 'Unknown'}")
            print(f"   Relationships: {len(graph.relationships) if hasattr(graph, 'relationships') else 'Unknown'}")
            
            # Show nodes
            if hasattr(graph, 'nodes'):
                for node in graph.nodes:
                    print(f"      Node: {node}")
            
            # Show relationships
            if hasattr(graph, 'relationships'):
                for rel in graph.relationships:
                    print(f"      Relationship: {rel}")
        
        return result
        
    except Exception as e:
        print(f"❌ Minimal GraphRAG test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # Test raw pipeline
    asyncio.run(test_raw_graphrag_pipeline())
    
    # Test minimal setup
    asyncio.run(test_minimal_graphrag())