#!/usr/bin/env python3
"""
Quick bucket structure check - just top-level folders
"""

from google.cloud import storage
from collections import defaultdict

def quick_structure_check():
    try:
        client = storage.Client()
        bucket = client.bucket("texas-laws-personalinjury")
        
        print("🔍 Quick bucket structure check...")
        
        # Get just first 50 files to understand structure
        folders = set()
        sample_files = []
        
        count = 0
        for blob in bucket.list_blobs():
            count += 1
            sample_files.append(blob.name)
            
            # Extract top-level folder
            if '/' in blob.name:
                top_folder = blob.name.split('/')[0]
                folders.add(top_folder)
            
            if count >= 50:
                break
        
        print(f"📊 Analyzed {count} files")
        print(f"📁 Top-level folders found: {sorted(folders)}")
        print(f"📋 Sample files:")
        for file in sample_files[:10]:
            print(f"   - {file}")
            
        # Check specifically for TX/ files
        tx_count = 0
        for blob in bucket.list_blobs(prefix="TX/", max_results=10):
            tx_count += 1
            print(f"🎯 TX/ sample {tx_count}: {blob.name}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    quick_structure_check()