#!/usr/bin/env python3
"""
Test Suite for Hybrid Enhanced Loader
====================================

Progressive testing from small to large scale:
1. Unit tests for individual components
2. Integration tests with sample data
3. Performance benchmarking
4. Production readiness validation
"""

import os
import sys
import json
import time
import tempfile
import bz2
import csv
from io import StringIO
from unittest.mock import Mock, patch
from typing import Dict, Optional
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from hybrid_enhanced_loader import HybridEnhancedLoader, BulkGCSUploader, BulkDatabaseInserter

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HybridLoaderTestSuite:
    """Comprehensive test suite for hybrid loader."""
    
    def __init__(self):
        self.test_results = []
        self.temp_files = []
    
    def cleanup(self):
        """Clean up temporary files."""
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass
    
    def create_test_csv(self, num_records: int = 100) -> str:
        """Create a test CSV file with sample data."""
        temp_fd, temp_path = tempfile.mkstemp(suffix='.csv.bz2')
        self.temp_files.append(temp_path)
        
        # Sample Texas cluster IDs (these would normally be in your pickle file)
        texas_cluster_ids = [12345, 23456, 34567, 45678, 56789]
        
        with bz2.open(temp_path, 'wt', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow([
                'id', 'cluster_id', 'plain_text', 'date_created', 'author_str'
            ])
            
            # Write test records
            for i in range(num_records):
                # Mix of Texas and non-Texas cases
                cluster_id = texas_cluster_ids[i % len(texas_cluster_ids)] if i % 3 == 0 else 99999 + i
                
                writer.writerow([
                    f'case_{i}',
                    cluster_id,
                    f'This is the full text of legal case {i}. It contains personal injury claims and medical malpractice issues.',
                    '2023-01-01T12:00:00Z',
                    f'Judge Smith {i}'
                ])
        
        logger.info(f"Created test CSV with {num_records} records: {temp_path}")
        return temp_path
    
    def test_bulk_gcs_uploader(self) -> bool:
        """Test bulk GCS uploader component."""
        logger.info("🧪 Testing BulkGCSUploader...")
        
        try:
            # Mock GCS client
            mock_gcs_client = Mock()
            mock_bucket = Mock()
            mock_blob = Mock()
            
            mock_gcs_client.bucket.return_value = mock_bucket
            mock_bucket.blob.return_value = mock_blob
            
            uploader = BulkGCSUploader(mock_gcs_client, "test-bucket")
            
            # Test data
            test_records = [
                {
                    'gcs_path': 'test/case_1.txt',
                    'plain_text': 'Test case content 1'
                },
                {
                    'gcs_path': 'test/case_2.txt',
                    'plain_text': 'Test case content 2'
                }
            ]
            
            # Test upload
            success, count = uploader.upload_batch_as_tarball(test_records, batch_id=1)
            
            # Verify
            assert success == True
            assert count == 2
            assert mock_bucket.blob.called
            assert mock_blob.upload_from_file.called
            
            logger.info("✅ BulkGCSUploader test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ BulkGCSUploader test failed: {e}")
            return False
    
    def test_bulk_database_inserter(self) -> bool:
        """Test bulk database inserter component."""
        logger.info("🧪 Testing BulkDatabaseInserter...")
        
        try:
            # Mock Supabase client
            mock_supabase = Mock()
            mock_table = Mock()
            mock_response = Mock()
            
            mock_supabase.table.return_value = mock_table
            mock_table.upsert.return_value = mock_response
            mock_response.execute.return_value = mock_response
            mock_response.data = [{'id': 1}, {'id': 2}]  # Mock successful insert
            
            inserter = BulkDatabaseInserter(mock_supabase)
            
            # Test data
            test_records = [
                {
                    'id': 'case_1',
                    'case_name': 'Test Case 1',
                    'jurisdiction': 'TX',
                    'content_hash': 'hash1'
                },
                {
                    'id': 'case_2',
                    'case_name': 'Test Case 2',
                    'jurisdiction': 'TX',
                    'content_hash': 'hash2'
                }
            ]
            
            # Test insert (will use fallback since no direct PostgreSQL URL)
            success, count = inserter.bulk_insert_via_copy(test_records)
            
            # Verify
            assert success == True
            assert count == 2
            
            logger.info("✅ BulkDatabaseInserter test passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ BulkDatabaseInserter test failed: {e}")
            return False
    
    def test_small_scale_processing(self) -> bool:
        """Test processing with small dataset."""
        logger.info("🧪 Testing small-scale processing...")
        
        try:
            # Create small test CSV
            test_csv = self.create_test_csv(50)
            
            # Mock the required pickle file
            mock_texas_ids = {12345, 23456, 34567, 45678, 56789}
            
            with patch('pickle.load', return_value=mock_texas_ids), \
                 patch('os.path.exists', return_value=True), \
                 patch.object(HybridEnhancedLoader, '_init_supabase') as mock_supabase, \
                 patch.object(HybridEnhancedLoader, '_init_gcs') as mock_gcs, \
                 patch.object(HybridEnhancedLoader, '_init_gemini') as mock_gemini, \
                 patch.object(HybridEnhancedLoader, '_load_existing_hashes', return_value=set()):
                
                # Mock the clients
                mock_supabase.return_value = Mock()
                mock_gcs.return_value = Mock()
                mock_gemini.return_value = Mock()
                
                # Initialize loader
                loader = HybridEnhancedLoader(
                    batch_size=10,
                    skip_classification=True,  # Skip for faster testing
                    csv_file=test_csv,
                    enable_bulk_gcs=False,  # Disable for testing
                    enable_bulk_db=False    # Disable for testing
                )
                
                # Override methods to avoid actual uploads
                loader._upload_to_gcs = Mock(return_value=True)
                loader._insert_batch = Mock(return_value=True)
                
                # Process the test file
                stats = loader.process_csv_file_hybrid(test_csv, limit=50)
                
                # Verify results
                assert stats['rows_read'] > 0
                assert stats['texas_matches'] > 0  # Should find some Texas cases
                assert stats['total_processed'] >= 0
                
                logger.info(f"✅ Small-scale test passed: {stats['total_processed']} records processed")
                return True
                
        except Exception as e:
            logger.error(f"❌ Small-scale processing test failed: {e}")
            return False
    
    def test_performance_benchmark(self) -> bool:
        """Run performance benchmark."""
        logger.info("🧪 Running performance benchmark...")
        
        try:
            # Create larger test dataset
            test_csv = self.create_test_csv(1000)
            
            mock_texas_ids = {12345, 23456, 34567, 45678, 56789}
            
            with patch('pickle.load', return_value=mock_texas_ids), \
                 patch('os.path.exists', return_value=True), \
                 patch.object(HybridEnhancedLoader, '_init_supabase') as mock_supabase, \
                 patch.object(HybridEnhancedLoader, '_init_gcs') as mock_gcs, \
                 patch.object(HybridEnhancedLoader, '_init_gemini') as mock_gemini, \
                 patch.object(HybridEnhancedLoader, '_load_existing_hashes', return_value=set()):
                
                mock_supabase.return_value = Mock()
                mock_gcs.return_value = Mock()
                mock_gemini.return_value = Mock()
                
                loader = HybridEnhancedLoader(
                    batch_size=100,
                    skip_classification=True,
                    csv_file=test_csv,
                    enable_bulk_gcs=False,
                    enable_bulk_db=False
                )
                
                # Override methods
                loader._upload_to_gcs = Mock(return_value=True)
                loader._insert_batch = Mock(return_value=True)
                
                # Run benchmark
                start_time = time.time()
                stats = loader.process_csv_file_hybrid(test_csv, limit=1000)
                elapsed = time.time() - start_time
                
                # Calculate metrics
                if stats['total_processed'] > 0 and elapsed > 0:
                    rate = stats['total_processed'] / elapsed
                    logger.info(f"✅ Benchmark: {rate:.0f} records/sec")
                    
                    # Project to full dataset
                    estimated_full_time = (680000 / rate) / 3600  # hours
                    logger.info(f"📊 Estimated time for 680K records: {estimated_full_time:.1f} hours")
                    
                    return True
                else:
                    logger.warning("⚠️ No records processed in benchmark")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Performance benchmark failed: {e}")
            return False
    
    def run_all_tests(self) -> Dict:
        """Run all tests and return results."""
        logger.info("🚀 Starting Hybrid Loader Test Suite")
        
        test_methods = [
            ("Bulk GCS Uploader", self.test_bulk_gcs_uploader),
            ("Bulk Database Inserter", self.test_bulk_database_inserter),
            ("Small Scale Processing", self.test_small_scale_processing),
            ("Performance Benchmark", self.test_performance_benchmark)
        ]
        
        results = {
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'details': []
        }
        
        for test_name, test_method in test_methods:
            logger.info(f"\n--- Running: {test_name} ---")
            
            try:
                start_time = time.time()
                passed = test_method()
                elapsed = time.time() - start_time
                
                results['tests_run'] += 1
                if passed:
                    results['tests_passed'] += 1
                    status = "PASSED"
                else:
                    results['tests_failed'] += 1
                    status = "FAILED"
                
                results['details'].append({
                    'test': test_name,
                    'status': status,
                    'elapsed': elapsed
                })
                
                logger.info(f"Result: {status} ({elapsed:.2f}s)")
                
            except Exception as e:
                logger.error(f"Test error: {e}")
                results['tests_run'] += 1
                results['tests_failed'] += 1
                results['details'].append({
                    'test': test_name,
                    'status': "ERROR",
                    'error': str(e)
                })
        
        # Cleanup
        self.cleanup()
        
        return results

def main():
    """Run the test suite."""
    suite = HybridLoaderTestSuite()
    results = suite.run_all_tests()
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"HYBRID LOADER TEST RESULTS")
    print(f"{'='*50}")
    print(f"Tests Run: {results['tests_run']}")
    print(f"Passed: {results['tests_passed']}")
    print(f"Failed: {results['tests_failed']}")
    print(f"Success Rate: {(results['tests_passed']/results['tests_run']*100):.1f}%")
    
    print(f"\nDetailed Results:")
    for detail in results['details']:
        status_emoji = "✅" if detail['status'] == "PASSED" else "❌"
        print(f"  {status_emoji} {detail['test']}: {detail['status']}")
        if 'elapsed' in detail:
            print(f"    Duration: {detail['elapsed']:.2f}s")
        if 'error' in detail:
            print(f"    Error: {detail['error']}")
    
    # Exit code
    exit_code = 0 if results['tests_failed'] == 0 else 1
    sys.exit(exit_code)

if __name__ == "__main__":
    main()