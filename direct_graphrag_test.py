#!/usr/bin/env python3
"""
Direct GraphRAG Test
Test the Neo4j GraphRAG pipeline directly with minimal configuration
"""

import asyncio
import os
import logging
from dotenv import load_dotenv
import vertexai
from vertexai.generative_models import GenerationConfig

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# GraphRAG Response Schema
GRAPHRAG_RESPONSE_SCHEMA = {
    "type": "object",
    "properties": {
        "nodes": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "label": {"type": "string"},
                    "properties": {"type": "object"}
                },
                "required": ["id", "label", "properties"]
            }
        },
        "relationships": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "type": {"type": "string"},
                    "start_node_id": {"type": "string"},
                    "end_node_id": {"type": "string"},
                    "properties": {"type": "object"}
                },
                "required": ["type", "start_node_id", "end_node_id"]
            }
        }
    },
    "required": ["nodes", "relationships"]
}

async def test_direct_graphrag():
    """Test GraphRAG pipeline directly without our wrapper"""
    logger.info("🧪 Testing Direct GraphRAG Pipeline")
    
    try:
        from neo4j import GraphDatabase
        from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
        from neo4j_graphrag.llm import VertexAILLM
        from neo4j_graphrag.experimental.components.text_splitters.langchain import LangChainTextSplitterAdapter
        from langchain_text_splitters import RecursiveCharacterTextSplitter
        import voyageai
        from neo4j_graphrag.embeddings.base import Embedder
        from typing import List
        
        # Sample text to process
        test_text = """
        Case: Smith v. Jones
        Court: Harris County District Court
        Date: 2023-03-15
        
        This is a personal injury case where Plaintiff John Smith sued Defendant Mary Jones 
        for damages resulting from a motor vehicle accident. Judge William Brown presided over 
        the case. The jury awarded $50,000 in actual damages. Attorney Sarah Wilson represented 
        the plaintiff, while Attorney Robert Davis represented the defendant.
        """
        
        # Initialize Vertex AI
        vertex_project_id = os.getenv("VERTEX_PROJECT_ID")
        vertex_location = os.getenv("VERTEX_LOCATION", "us-central1")
        vertexai.init(project=vertex_project_id, location=vertex_location)
        
        # Configure LLM with structured output
        generation_config = GenerationConfig(
            temperature=0.0,
            response_mime_type="application/json",
            response_schema=GRAPHRAG_RESPONSE_SCHEMA
        )
        
        llm = VertexAILLM(
            model_name="gemini-2.0-flash-exp",
            generation_config=generation_config
        )
        
        logger.info("✅ LLM initialized")
        
        # Custom Voyage embeddings
        class VoyageEmbeddings(Embedder):
            def __init__(self):
                self.client = voyageai.Client(api_key=os.getenv("VOYAGE_API_KEY"))
                
            def embed_query(self, text: str) -> List[float]:
                result = self.client.embed(texts=[text], model="voyage-3-large", input_type="query")
                return result.embeddings[0]
                
            def embed_documents(self, texts: List[str]) -> List[List[float]]:
                result = self.client.embed(texts=texts, model="voyage-3-large", input_type="document")
                return result.embeddings
        
        embedder = VoyageEmbeddings()
        logger.info("✅ Embeddings initialized")
        
        # Initialize Neo4j driver
        driver = GraphDatabase.driver(
            os.getenv("NEO4J_URI"),
            auth=(os.getenv("NEO4J_USERNAME", "neo4j"), os.getenv("NEO4J_PASSWORD"))
        )
        logger.info("✅ Neo4j driver initialized")
        
        # Create text splitter
        text_splitter = LangChainTextSplitterAdapter(
            text_splitter=RecursiveCharacterTextSplitter(
                chunk_size=2000,
                chunk_overlap=200
            )
        )
        logger.info("✅ Text splitter initialized")
        
        # Create proper schema for GraphRAG
        from neo4j_graphrag.experimental.components.schema import GraphSchema, NodeType, RelationshipType
        
        node_types = [
            NodeType(label="Case", description="Legal cases and proceedings"),
            NodeType(label="Judge", description="Presiding judges"),
            NodeType(label="Court", description="Courts and jurisdictions"),
            NodeType(label="Attorney", description="Legal representatives"),
            NodeType(label="Plaintiff", description="Plaintiffs and petitioners"),
            NodeType(label="Defendant", description="Defendants and respondents"),
            NodeType(label="Damages", description="Monetary awards and damages"),
        ]
        
        relationship_types = [
            RelationshipType(label="PRESIDED_OVER", description="Judge presided over case"),
            RelationshipType(label="REPRESENTED", description="Attorney represented party"),
            RelationshipType(label="FILED_IN", description="Case filed in court"),
            RelationshipType(label="AWARDED", description="Damages awarded"),
            RelationshipType(label="OPPOSED", description="Parties in opposition"),
        ]
        
        schema = GraphSchema(
            node_types=tuple(node_types),
            relationship_types=tuple(relationship_types),
            patterns=()  # Empty patterns
        )
        
        logger.info("✅ Schema created")
        
        # Test different SimpleKGPipeline configurations
        configs = [
            {"llm": llm, "embedder": embedder, "driver": driver, "from_pdf": False},  # Without schema
            {"llm": llm, "embedder": embedder, "driver": driver, "text_splitter": text_splitter, "from_pdf": False},  # Without schema
            {"llm": llm, "embedder": embedder, "driver": driver, "from_pdf": False, "schema": schema},  # With schema
            {"llm": llm, "embedder": embedder, "driver": driver, "text_splitter": text_splitter, "from_pdf": False, "schema": schema},  # Full with schema
        ]
        
        for i, config in enumerate(configs):
            logger.info(f"Testing configuration {i+1}: {list(config.keys())}")
            
            try:
                kg_pipeline = SimpleKGPipeline(**config)
                logger.info(f"✅ Pipeline {i+1} created successfully")
                
                # Test processing
                logger.info(f"Processing text with pipeline {i+1}...")
                
                # Try different API call methods
                try:
                    # Method 1: text parameter
                    result = await kg_pipeline.run_async(text=test_text)
                    logger.info(f"✅ Method 1 (text param) succeeded for pipeline {i+1}")
                    
                except Exception as method1_error:
                    logger.warning(f"Method 1 failed: {method1_error}")
                    
                    try:
                        # Method 2: file_path=None, text parameter
                        result = await kg_pipeline.run_async(file_path=None, text=test_text)
                        logger.info(f"✅ Method 2 (file_path=None) succeeded for pipeline {i+1}")
                        
                    except Exception as method2_error:
                        logger.warning(f"Method 2 failed: {method2_error}")
                        
                        try:
                            # Method 3: documents parameter
                            result = await kg_pipeline.run_async(documents=[{"text": test_text}])
                            logger.info(f"✅ Method 3 (documents) succeeded for pipeline {i+1}")
                            
                        except Exception as method3_error:
                            logger.error(f"All methods failed for pipeline {i+1}: {method3_error}")
                            continue
                
                # Check results
                if hasattr(result, 'nodes'):
                    logger.info(f"   Nodes: {len(result.nodes)}")
                    logger.info(f"   Relationships: {len(result.relationships) if hasattr(result, 'relationships') else 0}")
                    
                    if len(result.nodes) > 0:
                        logger.info("🎉 SUCCESS: Found working configuration!")
                        return {"config_index": i+1, "config": config, "result": result}
                else:
                    logger.warning(f"   Result has no nodes attribute: {type(result)}")
                    
            except Exception as e:
                logger.error(f"❌ Pipeline {i+1} failed to initialize: {e}")
        
        logger.error("❌ No working configuration found")
        return None
        
    except Exception as e:
        logger.error(f"❌ Direct GraphRAG test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_direct_graphrag())
    if result:
        print(f"\n🎉 Working Configuration Found: {result['config_index']}")
    else:
        print("\n❌ No Working Configuration Found")