#!/usr/bin/env python3
"""
Inspect the real data structure in texas-laws-personalinjury bucket
to understand how to adapt the enhanced GCS client
"""

import os
import json
import gzip
import asyncio
from google.cloud import storage
from dotenv import load_dotenv

load_dotenv()

async def inspect_real_data_structure():
    print("=== Inspecting Real Texas Legal Data Structure ===\n")
    
    try:
        # Initialize GCS client
        client = storage.Client()
        bucket = client.bucket("texas-laws-personalinjury")
        
        # Get first few files to understand structure
        blobs = list(bucket.list_blobs(prefix="FED/clusters/", max_results=5))
        
        print(f"📋 Found {len(blobs)} sample files:")
        for i, blob in enumerate(blobs, 1):
            print(f"   {i}. {blob.name} ({blob.size:,} bytes)")
        
        if blobs:
            # Download and inspect first file
            first_blob = blobs[0]
            print(f"\n🔍 Inspecting: {first_blob.name}")
            
            # Download content
            content = first_blob.download_as_bytes()
            
            # Decompress if gzipped
            if first_blob.name.endswith('.gz'):
                content = gzip.decompress(content)
                print("✅ Decompressed gzipped content")
            
            # Parse JSON
            try:
                data = json.loads(content.decode('utf-8'))
                print(f"✅ Successfully parsed JSON")
                
                # Analyze structure
                print(f"\n📊 Data Structure Analysis:")
                print(f"   Type: {type(data)}")
                
                if isinstance(data, dict):
                    print(f"   Keys: {list(data.keys())}")
                    
                    # Look for case/opinion data
                    for key, value in data.items():
                        if isinstance(value, (str, int, float)):
                            print(f"   {key}: {value}")
                        elif isinstance(value, list):
                            print(f"   {key}: list with {len(value)} items")
                            if value and isinstance(value[0], dict):
                                print(f"      First item keys: {list(value[0].keys())}")
                        elif isinstance(value, dict):
                            print(f"   {key}: dict with keys: {list(value.keys())}")
                
                elif isinstance(data, list):
                    print(f"   List with {len(data)} items")
                    if data and isinstance(data[0], dict):
                        print(f"   First item keys: {list(data[0].keys())}")
                
                # Look for Texas-specific content
                data_str = str(data).lower()
                texas_indicators = ['texas', 'tx', 'houston', 'dallas', 'austin', 'san antonio']
                found_indicators = [ind for ind in texas_indicators if ind in data_str]
                
                if found_indicators:
                    print(f"\n🎯 Texas indicators found: {found_indicators}")
                else:
                    print(f"\n⚠️  No obvious Texas indicators found in this file")
                
                # Sample first few characters of content fields
                def find_content_fields(obj, path=""):
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            current_path = f"{path}.{key}" if path else key
                            if isinstance(value, str) and len(value) > 100:
                                print(f"   Content field '{current_path}': '{value[:200]}...'")
                            elif isinstance(value, (dict, list)):
                                find_content_fields(value, current_path)
                    elif isinstance(obj, list) and obj:
                        find_content_fields(obj[0], f"{path}[0]")
                
                print(f"\n📄 Content Preview:")
                find_content_fields(data)
                
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON: {e}")
                print(f"   Raw content preview: {content[:500]}")
            
            except UnicodeDecodeError as e:
                print(f"❌ Failed to decode content: {e}")
                print(f"   Binary content size: {len(content)} bytes")
                
        else:
            print("❌ No files found in FED/clusters/")
            
    except Exception as e:
        print(f"❌ Inspection failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(inspect_real_data_structure())