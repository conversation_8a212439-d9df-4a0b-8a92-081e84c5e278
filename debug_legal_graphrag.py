#!/usr/bin/env python3
"""
Debug version of Legal GraphRAG Pipeline
"""

import os
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from neo4j import GraphDatabase
from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
from neo4j_graphrag.llm import VertexAILLM
import vertexai
from vertexai.generative_models import GenerationConfig
from neo4j_graphrag.embeddings.base import Embedder
from neo4j_graphrag.experimental.components.text_splitters.langchain import LangChainTextSplitterAdapter
from langchain_text_splitters import RecursiveCharacterTextSplitter
import voyageai

# Configure logging for debug
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# GraphRAG response schema (same as verify_vertex.py)
GRAPHRAG_RESPONSE_SCHEMA = {
    "type": "object",
    "properties": {
        "nodes": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "label": {"type": "string"},
                    "properties": {"type": "object"}
                },
                "required": ["id", "label", "properties"]
            }
        },
        "relationships": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "type": {"type": "string"},
                    "start_node_id": {"type": "string"},
                    "end_node_id": {"type": "string"},
                    "properties": {"type": "object"}
                },
                "required": ["type", "start_node_id", "end_node_id"]
            }
        }
    },
    "required": ["nodes", "relationships"]
}

class VoyageAIEmbeddings(Embedder):
    """Enhanced Voyage AI embedder for legal documents"""
    
    def __init__(self, model: str = "voyage-context-3", api_key: Optional[str] = None):
        self.model = model
        self.client = voyageai.Client(api_key=api_key or os.getenv("VOYAGE_API_KEY"))
        self.output_dimension = 1024
        
    def embed_query(self, text: str) -> List[float]:
        """Embed query text with legal context awareness"""
        try:
            if self.model == "voyage-context-3":
                result = self.client.contextualized_embed(
                    inputs=[[text]],
                    model=self.model,
                    input_type="query",
                    output_dimension=self.output_dimension
                )
                return result.results[0].embeddings[0]
            else:
                result = self.client.embed(
                    texts=[text],
                    model=self.model,
                    input_type="query",
                    truncation=True
                )
                return result.embeddings[0]
        except Exception as e:
            logger.error(f"Error embedding query: {e}")
            return [0.0] * self.output_dimension
        
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed documents using contextualized embeddings for voyage-context-3"""
        try:
            if self.model == "voyage-context-3":
                inputs = [[text] for text in texts]
                result = self.client.contextualized_embed(
                    inputs=inputs,
                    model=self.model,
                    input_type="document",
                    output_dimension=self.output_dimension
                )
                return [result_item.embeddings[0] for result_item in result.results]
            else:
                result = self.client.embed(
                    texts=texts,
                    model=self.model,
                    input_type="document",
                    truncation=True
                )
                return result.embeddings
        except Exception as e:
            logger.error(f"Error embedding documents: {e}")
            return [[0.0] * self.output_dimension for _ in texts]

async def debug_legal_graphrag():
    """Debug the legal GraphRAG setup step by step"""
    
    # Sample legal document
    sample_document = {
        "id": "debug_case_001",
        "case_name": "Smith v. Jones",
        "court": "Harris County District Court",
        "plain_text": """
        This is a personal injury case where Plaintiff John Smith sued Defendant Mary Jones 
        for damages resulting from a motor vehicle accident. Judge William Brown presided over 
        the case. The jury awarded $50,000 in actual damages and $25,000 in punitive damages. 
        The case was decided on March 15, 2023, in the 55th Judicial District Court of Harris County, Texas.
        Attorney Sarah Wilson represented the plaintiff, while Attorney Robert Davis represented the defendant.
        The court cited precedent from Doe v. Roe, 123 S.W.3d 456 (Tex. 2020).
        """
    }
    
    try:
        # Initialize connections
        neo4j_uri = os.getenv("NEO4J_URI")
        neo4j_user = os.getenv("NEO4J_USERNAME", "neo4j")
        neo4j_password = os.getenv("NEO4J_PASSWORD")
        voyage_api_key = os.getenv("VOYAGE_API_KEY")
        
        # Vertex AI configuration
        vertex_project_id = os.getenv("VERTEX_PROJECT_ID")
        vertex_location = os.getenv("VERTEX_LOCATION", "us-central1")
        vertex_model = os.getenv("VERTEX_MODEL", "gemini-2.5-pro")
        
        logger.info(f"Initializing with project: {vertex_project_id}")
        
        # Initialize Vertex AI
        vertexai.init(project=vertex_project_id, location=vertex_location)
        
        # Configure Vertex AI Gemini with structured output
        generation_config = GenerationConfig(
            temperature=0.0,
            response_mime_type="application/json",
            response_schema=GRAPHRAG_RESPONSE_SCHEMA
        )
        
        llm = VertexAILLM(
            model_name=vertex_model,
            generation_config=generation_config
        )
        
        logger.info("VertexAI LLM initialized successfully")
        
        # Test the LLM directly first
        test_prompt = """You extract entities and relationships for a knowledge graph.
Emit ONE JSON object with keys "nodes" and "relationships" and nothing else.

Graph schema:
Allowed labels: Case, Judge, Court, Attorney, Plaintiff, Defendant, Damages
Allowed relationships: PRESIDED_OVER, REPRESENTED, FILED_IN, AWARDED, OPPOSED

Input text:
This is a personal injury case where Plaintiff John Smith sued Defendant Mary Jones for damages resulting from a motor vehicle accident. Judge William Brown presided over the case in Harris County District Court. Attorney Sarah Wilson represented the plaintiff. The jury awarded $50,000 in actual damages."""
        
        logger.info("Testing LLM directly...")
        direct_result = await llm.ainvoke(test_prompt)
        logger.info(f"Direct LLM result: {direct_result}")
        
        # Initialize embeddings
        embeddings = VoyageAIEmbeddings(
            model="voyage-context-3",
            api_key=voyage_api_key
        )
        
        logger.info("Embeddings initialized successfully")
        
        # Test embeddings
        test_embedding = embeddings.embed_query("test legal document")
        logger.info(f"Test embedding dimension: {len(test_embedding)}")
        
        # Initialize Neo4j driver
        driver = GraphDatabase.driver(
            neo4j_uri,
            auth=(neo4j_user, neo4j_password)
        )
        
        logger.info("Neo4j driver initialized successfully")
        
        # Create legal text splitter
        legal_separators = [
            "\n\n\n",
            "\nHELD:",
            "\nCONCLUSION:",
            "\nDISPOSITION:",
            "\n\n",
            ". ",
            "; ",
            ", ",
            " ",
            ""
        ]
        
        recursive_splitter = RecursiveCharacterTextSplitter(
            chunk_size=2000,
            chunk_overlap=200,
            length_function=len,
            separators=legal_separators,
            keep_separator=True
        )
        
        text_splitter = LangChainTextSplitterAdapter(text_splitter=recursive_splitter)
        
        logger.info("Text splitter initialized successfully")
        
        # Create legal graph schema
        from neo4j_graphrag.experimental.components.schema import (
            GraphSchema,
            NodeType,
            RelationshipType
        )
        
        # Define legal node types with label field
        node_types = [
            NodeType(label="Case", description="Legal cases and proceedings"),
            NodeType(label="Judge", description="Presiding judges"),
            NodeType(label="Court", description="Courts and jurisdictions"),
            NodeType(label="Attorney", description="Legal representatives"),
            NodeType(label="Plaintiff", description="Plaintiffs and petitioners"),
            NodeType(label="Defendant", description="Defendants and respondents"),
            NodeType(label="Damages", description="Monetary awards and damages"),
        ]
        
        # Define legal relationship types with label field  
        relationship_types = [
            RelationshipType(label="PRESIDED_OVER", description="Judge presided over case"),
            RelationshipType(label="REPRESENTED", description="Attorney represented party"),
            RelationshipType(label="FILED_IN", description="Case filed in court"),
            RelationshipType(label="AWARDED", description="Damages awarded"),
            RelationshipType(label="OPPOSED", description="Parties in opposition"),
        ]
        
        legal_schema = GraphSchema(
            node_types=tuple(node_types),
            relationship_types=tuple(relationship_types),
            patterns=()
        )
        
        logger.info("Legal schema created successfully")
        
        # Create GraphRAG pipeline
        kg_pipeline = SimpleKGPipeline(
            llm=llm,
            embedder=embeddings,
            driver=driver,
            text_splitter=text_splitter,
            perform_entity_resolution=True,
            from_pdf=False,
            schema=legal_schema
        )
        
        logger.info("GraphRAG pipeline created successfully")
        
        # Extract text from document
        text = sample_document.get('plain_text', '')
        logger.info(f"Processing text of length: {len(text)}")
        logger.debug(f"Text content: {text[:200]}...")
        
        # Process through pipeline
        logger.info("Starting pipeline processing...")
        result = await kg_pipeline.run_async(file_path=None, text=text)
        
        logger.info(f"Pipeline completed. Result type: {type(result)}")
        logger.info(f"Result attributes: {dir(result)}")
        
        # Examine the result
        if hasattr(result, 'nodes'):
            logger.info(f"Nodes found: {len(result.nodes)}")
            for i, node in enumerate(result.nodes[:3]):  # Show first 3
                logger.info(f"Node {i}: {node}")
        else:
            logger.warning("No 'nodes' attribute in result")
            
        if hasattr(result, 'relationships'):
            logger.info(f"Relationships found: {len(result.relationships)}")
            for i, rel in enumerate(result.relationships[:3]):  # Show first 3
                logger.info(f"Relationship {i}: {rel}")
        else:
            logger.warning("No 'relationships' attribute in result")
            
        # Show all result attributes
        logger.debug(f"Full result: {result}")
        
        # Clean up
        driver.close()
        logger.info("Debug completed successfully")
        
    except Exception as e:
        logger.error(f"Debug failed: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    asyncio.run(debug_legal_graphrag())