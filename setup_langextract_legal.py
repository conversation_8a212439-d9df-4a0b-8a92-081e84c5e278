#!/usr/bin/env python3
"""
LangExtract Integration for Legal Entity Extraction
Enhanced for Texas legal domain with high-precision entity recognition
"""

import os
import asyncio
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime

import langextract
from langextract import Entity, Extraction
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

@dataclass
class Judge(Entity):
    """Legal judge entity with Texas-specific attributes"""
    name: str
    title: Optional[str] = None
    court: Optional[str] = None
    appointment_date: Optional[str] = None
    role_in_case: Optional[str] = None  # presiding, panel member, author

@dataclass 
class Citation(Entity):
    """Legal citation entity with precise format validation"""
    case_name: str
    volume: Optional[str] = None
    reporter: str
    page: str
    year: str
    court: Optional[str] = None
    precedential_value: Optional[str] = None  # binding, persuasive, superseded

@dataclass
class Damages(Entity):
    """Damages/settlement entity with financial details"""
    amount: str
    damage_type: str  # actual, punitive, exemplary, nominal
    awarded_to: Optional[str] = None
    basis: Optional[str] = None  # jury verdict, settlement, judgment

@dataclass
class Attorney(Entity):
    """Attorney entity with representation details"""
    name: str
    firm: Optional[str] = None
    bar_number: Optional[str] = None
    role: Optional[str] = None  # plaintiff counsel, defense counsel
    
@dataclass
class Court(Entity):
    """Court entity with jurisdictional information"""
    name: str
    jurisdiction: str  # state, federal, appellate
    county: Optional[str] = None
    district: Optional[str] = None
    
class LegalEntityExtractor:
    """Enhanced entity extractor for Texas legal documents using LangExtract"""
    
    def __init__(self, model_provider: str = "gemini"):
        """Initialize the legal entity extractor"""
        self.model_provider = model_provider
        
        # Configure model based on provider
        if model_provider == "gemini":
            self.api_key = os.getenv("GEMINI_API_KEY")
            if not self.api_key:
                raise ValueError("GEMINI_API_KEY not found in environment variables")
        
        # Texas court patterns for validation
        self.texas_court_patterns = {
            "supreme_court": r"Texas Supreme Court",
            "criminal_appeals": r"(?:Texas )?Court of Criminal Appeals",
            "appellate_court": r"(\d+)(?:st|nd|rd|th)?\s+Court of Appeals",
            "district_court": r"(\d+)(?:st|nd|rd|th)?\s+(?:Judicial\s+)?District Court.*(?:of\s+)?([A-Z][a-z]+\s+County)",
            "county_court": r"County Court(?:\s+at\s+Law)?.*([A-Z][a-z]+\s+County)",
            "federal_district": r"U\.S\.\s+District Court.*(?:Eastern|Western|Northern|Southern)\s+District.*Texas"
        }
        
        # Citation patterns for Texas courts
        self.citation_patterns = {
            "texas_sw": r"(\d+)\s+S\.W\.(\d)d\s+(\d+)",  # SW2d, SW3d
            "texas_reports": r"(\d+)\s+Tex\.\s+(\d+)",
            "texas_appeals": r"(\d+)\s+Tex\.App\.\s+(\d+)",
            "federal": r"(\d+)\s+F\.(\d)d\s+(\d+)",
            "us_reports": r"(\d+)\s+U\.S\.\s+(\d+)"
        }
        
        # Damage amount patterns
        self.damage_patterns = {
            "monetary": r"\$[\d,]+(?:\.\d{2})?(?:\s+(?:million|thousand|billion))?",
            "verdict": r"(?:jury|court)\s+(?:verdict|award|judgment).*\$[\d,]+",
            "settlement": r"settlement.*\$[\d,]+",
            "damages": r"(?:actual|punitive|exemplary|nominal|consequential)\s+damages.*\$[\d,]+"
        }
        
        logger.info(f"Legal entity extractor initialized with {model_provider} provider")
    
    async def extract_legal_entities(self, text: str, practice_area: str = "personal_injury") -> Dict[str, Any]:
        """Extract legal entities from text using LangExtract with legal domain specificity"""
        
        try:
            # Create extraction instance
            extraction = Extraction(
                text=text,
                entities=[Judge, Citation, Damages, Attorney, Court],
                api_key=self.api_key,
                model_provider=self.model_provider
            )
            
            # Add legal domain context
            extraction.system_prompt = self._create_legal_system_prompt(practice_area)
            
            # Perform extraction
            logger.info("Performing legal entity extraction...")
            result = await extraction.run_async()
            
            # Validate and enhance extracted entities
            validated_result = self._validate_legal_extractions(result, text)
            
            # Add quality metrics
            validated_result["quality_metrics"] = self._calculate_extraction_quality(validated_result, text)
            
            logger.info(f"Extraction completed: {sum(len(entities) for entities in validated_result['entities'].values())} entities extracted")
            
            return validated_result
            
        except Exception as e:
            logger.error(f"Error in legal entity extraction: {e}")
            return {
                "entities": {"judges": [], "citations": [], "damages": [], "attorneys": [], "courts": []},
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def _create_legal_system_prompt(self, practice_area: str) -> str:
        """Create legal domain-specific system prompt"""
        
        base_prompt = f"""
        You are an expert legal entity extractor specializing in Texas {practice_area.replace('_', ' ')} law.
        
        EXTRACTION REQUIREMENTS:
        
        1. JUDGES: Extract all judges mentioned with their roles (presiding, panel member, author)
           - Include formal titles (Judge, Justice, Chief Justice)
           - Identify their court affiliations
           - Note their role in the specific case
        
        2. CITATIONS: Extract legal citations with perfect formatting
           - Texas citations: 123 S.W.3d 456 (Tex. 2020)
           - Federal citations: 123 F.3d 456 (5th Cir. 2020)
           - Include case names when available
           - Identify precedential value (binding, persuasive)
        
        3. DAMAGES: Extract all monetary awards and settlements
           - Exact amounts: $50,000, $1.2 million
           - Type: actual, punitive, exemplary, nominal
           - Basis: jury verdict, court judgment, settlement
        
        4. ATTORNEYS: Extract attorney names and their roles
           - Plaintiff/defense counsel
           - Firm affiliations when mentioned
           - Bar numbers if available
        
        5. COURTS: Extract court names with jurisdictional details
           - Texas state courts: District, County, Appeals, Supreme
           - Federal courts: U.S. District, Circuit, Supreme Court
           - Include county/district information
        
        QUALITY STANDARDS:
        - Extract only entities you are highly confident about
        - Preserve exact text formatting for citations
        - Include source text spans for verification
        - Maintain legal accuracy over quantity
        
        TEXAS LEGAL CONTEXT:
        - Recognize Texas court hierarchy and jurisdiction
        - Understand Texas citation formats and requirements
        - Identify relevant Texas statutes and regulations
        """
        
        return base_prompt
    
    def _validate_legal_extractions(self, raw_result: Any, original_text: str) -> Dict[str, Any]:
        """Validate and clean extracted legal entities"""
        
        validated_entities = {
            "judges": [],
            "citations": [], 
            "damages": [],
            "attorneys": [],
            "courts": []
        }
        
        # Process each entity type
        if hasattr(raw_result, 'entities'):
            for entity in raw_result.entities:
                entity_type = type(entity).__name__.lower()
                
                if entity_type == "judge":
                    validated_judge = self._validate_judge(entity, original_text)
                    if validated_judge:
                        validated_entities["judges"].append(validated_judge)
                
                elif entity_type == "citation":
                    validated_citation = self._validate_citation(entity, original_text)
                    if validated_citation:
                        validated_entities["citations"].append(validated_citation)
                
                elif entity_type == "damages":
                    validated_damages = self._validate_damages(entity, original_text)
                    if validated_damages:
                        validated_entities["damages"].append(validated_damages)
                
                elif entity_type == "attorney":
                    validated_attorney = self._validate_attorney(entity, original_text)
                    if validated_attorney:
                        validated_entities["attorneys"].append(validated_attorney)
                
                elif entity_type == "court":
                    validated_court = self._validate_court(entity, original_text)
                    if validated_court:
                        validated_entities["courts"].append(validated_court)
        
        return {
            "entities": validated_entities,
            "extraction_timestamp": datetime.utcnow().isoformat(),
            "original_text_length": len(original_text),
            "total_entities": sum(len(entities) for entities in validated_entities.values())
        }
    
    def _validate_judge(self, judge: Judge, text: str) -> Optional[Dict[str, Any]]:
        """Validate judge entity against Texas legal standards"""
        
        if not judge.name or len(judge.name.strip()) < 3:
            return None
        
        # Clean judge name
        name = judge.name.strip()
        
        # Validate judge title
        valid_titles = ["Judge", "Justice", "Chief Judge", "Chief Justice", "Magistrate Judge"]
        title = judge.title
        if title and title not in valid_titles:
            title = None
        
        # Validate court name against Texas patterns
        court = judge.court
        if court:
            court_valid = any(re.search(pattern, court, re.IGNORECASE) 
                            for pattern in self.texas_court_patterns.values())
            if not court_valid:
                logger.debug(f"Questionable court name for judge {name}: {court}")
        
        return {
            "name": name,
            "title": title,
            "court": court,
            "role_in_case": judge.role_in_case,
            "confidence": 0.9 if court else 0.7,
            "source_text": self._find_source_text(name, text)
        }
    
    def _validate_citation(self, citation: Citation, text: str) -> Optional[Dict[str, Any]]:
        """Validate citation against standard legal citation formats"""
        
        if not citation.case_name and not citation.reporter:
            return None
        
        # Validate citation format
        citation_text = f"{citation.volume} {citation.reporter} {citation.page}" if citation.volume else f"{citation.reporter} {citation.page}"
        
        # Check against known citation patterns
        valid_format = any(re.search(pattern, citation_text, re.IGNORECASE) 
                          for pattern in self.citation_patterns.values())
        
        if not valid_format:
            logger.debug(f"Invalid citation format: {citation_text}")
            return None
        
        return {
            "case_name": citation.case_name,
            "volume": citation.volume,
            "reporter": citation.reporter,
            "page": citation.page,
            "year": citation.year,
            "court": citation.court,
            "full_citation": citation_text,
            "confidence": 0.95 if valid_format else 0.6,
            "source_text": self._find_source_text(citation_text, text)
        }
    
    def _validate_damages(self, damages: Damages, text: str) -> Optional[Dict[str, Any]]:
        """Validate damages/monetary award information"""
        
        if not damages.amount:
            return None
        
        # Clean and validate amount
        amount = damages.amount.strip()
        
        # Check if amount matches monetary patterns
        valid_amount = any(re.search(pattern, amount, re.IGNORECASE) 
                          for pattern in self.damage_patterns.values())
        
        if not valid_amount:
            logger.debug(f"Invalid damage amount format: {amount}")
            return None
        
        # Validate damage type
        valid_types = ["actual", "punitive", "exemplary", "nominal", "consequential", "compensatory"]
        damage_type = damages.damage_type.lower() if damages.damage_type else "unspecified"
        
        return {
            "amount": amount,
            "damage_type": damage_type,
            "awarded_to": damages.awarded_to,
            "basis": damages.basis,
            "confidence": 0.9 if damage_type in valid_types else 0.7,
            "source_text": self._find_source_text(amount, text)
        }
    
    def _validate_attorney(self, attorney: Attorney, text: str) -> Optional[Dict[str, Any]]:
        """Validate attorney entity"""
        
        if not attorney.name or len(attorney.name.strip()) < 3:
            return None
        
        name = attorney.name.strip()
        
        # Validate role
        valid_roles = ["plaintiff counsel", "defense counsel", "prosecutor", "public defender"]
        role = attorney.role.lower() if attorney.role else None
        
        return {
            "name": name,
            "firm": attorney.firm,
            "role": role,
            "bar_number": attorney.bar_number,
            "confidence": 0.8,
            "source_text": self._find_source_text(name, text)
        }
    
    def _validate_court(self, court: Court, text: str) -> Optional[Dict[str, Any]]:
        """Validate court entity against Texas court system"""
        
        if not court.name:
            return None
        
        name = court.name.strip()
        
        # Validate against Texas court patterns
        court_type = "unknown"
        for court_pattern_name, pattern in self.texas_court_patterns.items():
            if re.search(pattern, name, re.IGNORECASE):
                court_type = court_pattern_name
                break
        
        return {
            "name": name,
            "type": court_type,
            "jurisdiction": court.jurisdiction,
            "county": court.county,
            "district": court.district,
            "confidence": 0.9 if court_type != "unknown" else 0.6,
            "source_text": self._find_source_text(name, text)
        }
    
    def _find_source_text(self, entity_text: str, full_text: str, context_chars: int = 50) -> str:
        """Find source text with context for entity verification"""
        
        # Find the entity in the text
        match = re.search(re.escape(entity_text), full_text, re.IGNORECASE)
        if match:
            start = max(0, match.start() - context_chars)
            end = min(len(full_text), match.end() + context_chars)
            return full_text[start:end]
        
        return entity_text
    
    def _calculate_extraction_quality(self, result: Dict[str, Any], text: str) -> Dict[str, float]:
        """Calculate quality metrics for the extraction"""
        
        total_entities = result["total_entities"]
        text_length = len(text)
        
        # Calculate entity density
        entity_density = total_entities / max(text_length / 1000, 1)  # Entities per 1k chars
        
        # Calculate confidence average
        all_confidences = []
        for entity_type, entities in result["entities"].items():
            for entity in entities:
                if "confidence" in entity:
                    all_confidences.append(entity["confidence"])
        
        avg_confidence = sum(all_confidences) / len(all_confidences) if all_confidences else 0.0
        
        # Calculate entity type diversity
        entity_types_found = sum(1 for entities in result["entities"].values() if entities)
        entity_diversity = entity_types_found / 5  # 5 total entity types
        
        return {
            "entity_density": entity_density,
            "average_confidence": avg_confidence,
            "entity_diversity": entity_diversity,
            "total_entities": total_entities,
            "quality_score": (avg_confidence + entity_diversity) / 2
        }

# Test function
async def test_langextract_legal():
    """Test LangExtract setup with legal document"""
    
    sample_legal_text = """
    In the case of Smith v. Jones, No. 2023-12345, heard in the 55th Judicial District Court 
    of Harris County, Texas, Judge William Brown presided over the proceedings. 
    
    Plaintiff John Smith, represented by Attorney Sarah Wilson of Wilson & Associates, 
    filed suit against Defendant Mary Jones for personal injuries sustained in a motor vehicle accident.
    Defense counsel Robert Davis of Davis Law Firm represented the defendant.
    
    The jury awarded $75,000 in actual damages and $25,000 in punitive damages to the plaintiff.
    The court's decision was based on precedent established in Doe v. Roe, 456 S.W.3d 789 (Tex. 2020),
    and followed the reasoning in Johnson v. State, 123 Tex. 456 (1995).
    
    Chief Justice Martinez of the Texas Supreme Court had previously ruled on similar matters
    in Brown v. Green, 234 S.W.3d 567 (Tex. 2018), establishing binding precedent for this case.
    """
    
    try:
        extractor = LegalEntityExtractor("gemini")
        result = await extractor.extract_legal_entities(sample_legal_text, "personal_injury")
        
        print("=== LangExtract Legal Entity Extraction Test Results ===")
        print(f"Total entities extracted: {result['total_entities']}")
        print(f"Quality score: {result['quality_metrics']['quality_score']:.2f}")
        print(f"Average confidence: {result['quality_metrics']['average_confidence']:.2f}")
        
        for entity_type, entities in result['entities'].items():
            if entities:
                print(f"\n=== {entity_type.upper()} ===")
                for entity in entities:
                    print(f"- {entity.get('name', 'N/A')} (confidence: {entity.get('confidence', 0):.2f})")
                    if 'source_text' in entity:
                        print(f"  Source: ...{entity['source_text'][-50:]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"LangExtract test failed: {e}")
        return False

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_langextract_legal())