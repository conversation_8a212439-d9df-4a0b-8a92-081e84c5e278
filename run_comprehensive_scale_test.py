#!/usr/bin/env python3
"""
Run Comprehensive 4-System Scale Test
Executes the complete end-to-end testing pipeline with evidence verification

This script:
1. Runs the 4-system scale test with 10 documents
2. Captures concrete evidence from all systems
3. Verifies the evidence to prove functionality
4. Generates comprehensive reports with scalability analysis
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# Import our testing infrastructure
from test_complete_4system_scale import Complete4SystemScaleTester
from verify_4system_evidence import FourSystemEvidenceVerifier

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def run_comprehensive_test():
    """Run the complete 4-system scale test with evidence verification"""
    
    logger.info("🚀 STARTING COMPREHENSIVE 4-SYSTEM SCALE TEST")
    logger.info("=" * 80)
    logger.info("This test will:")
    logger.info("  📁 Process 10 documents through GCS retrieval")
    logger.info("  🧠 Extract entities/relationships with Enhanced GraphRAG")  
    logger.info("  🚀 Generate embeddings with Voyage contextual processing")
    logger.info("  💾 Store across Supabase + Neo4j + Pinecone with global UID tracking")
    logger.info("  🔍 Verify evidence across all systems")
    logger.info("  📊 Generate scalability analysis and recommendations")
    logger.info("")
    
    start_time = datetime.now()
    
    try:
        # Phase 1: Execute 4-System Scale Test
        logger.info("📋 PHASE 1: EXECUTING 4-SYSTEM SCALE TEST")
        logger.info("-" * 50)
        
        tester = Complete4SystemScaleTester()
        test_report = await tester.run_complete_scale_test(document_count=10)
        
        if not test_report:
            logger.error("❌ Scale test failed - no report generated")
            return False
        
        # Save test report
        test_report_path = tester.save_test_report()
        
        logger.info(f"✅ Scale test completed:")
        logger.info(f"   Success rate: {test_report.overall_success_rate:.1f}%")
        logger.info(f"   Processing time: {test_report.total_test_time_seconds:.1f}s")
        logger.info(f"   Total cost: ${sum(test_report.total_costs.values()):.2f}")
        logger.info(f"   Report saved: {test_report_path}")
        logger.info("")
        
        # Phase 2: Evidence Verification
        logger.info("🔍 PHASE 2: EVIDENCE VERIFICATION")
        logger.info("-" * 50)
        
        verifier = FourSystemEvidenceVerifier()
        verification_results = await verifier.verify_scale_test_evidence(test_report_path)
        
        # Save verification report
        verification_report_path = verifier.save_verification_report()
        
        verified_count = len([r for r in verification_results if r.verified])
        total_count = len(verification_results)
        verification_rate = (verified_count / total_count) * 100 if total_count > 0 else 0
        
        logger.info(f"✅ Evidence verification completed:")
        logger.info(f"   Verification rate: {verification_rate:.1f}%")
        logger.info(f"   Verified evidence: {verified_count}/{total_count}")
        logger.info(f"   Report saved: {verification_report_path}")
        logger.info("")
        
        # Phase 3: Generate Summary Report
        logger.info("📊 PHASE 3: GENERATING COMPREHENSIVE SUMMARY")
        logger.info("-" * 50)
        
        summary = generate_comprehensive_summary(test_report, verification_results)
        summary_path = save_summary_report(summary)
        
        logger.info(f"✅ Summary report generated: {summary_path}")
        logger.info("")
        
        # Phase 4: Final Assessment
        logger.info("🎯 PHASE 4: FINAL ASSESSMENT")
        logger.info("-" * 50)
        
        overall_success = (
            test_report.overall_success_rate >= 85 and
            verification_rate >= 80
        )
        
        if overall_success:
            logger.info("🎉 SUCCESS: Complete 4-system pipeline is production-ready!")
            logger.info("   ✅ High success rate across all systems")
            logger.info("   ✅ Evidence verification confirms functionality")
            logger.info("   ✅ Ready for larger scale deployment")
        else:
            logger.info("⚠️ NEEDS IMPROVEMENT: Address issues before production")
            logger.info(f"   Scale test success: {test_report.overall_success_rate:.1f}% (target: ≥85%)")
            logger.info(f"   Evidence verification: {verification_rate:.1f}% (target: ≥80%)")
        
        # Display key metrics
        total_time = (datetime.now() - start_time).total_seconds()
        logger.info("")
        logger.info("📈 KEY METRICS:")
        logger.info(f"   Total entities extracted: {test_report.total_entities_extracted}")
        logger.info(f"   Total relationships: {test_report.total_relationships_extracted}")
        logger.info(f"   Total chunks created: {test_report.total_chunks_created}")
        logger.info(f"   Total vectors stored: {test_report.total_vectors_stored}")
        logger.info(f"   Average processing time per doc: {test_report.average_processing_time_per_doc:.1f}s")
        logger.info(f"   Total test execution time: {total_time:.1f}s")
        
        # Scalability projections
        if test_report.scalability_analysis:
            logger.info("")
            logger.info("🚀 SCALABILITY PROJECTIONS:")
            proj_100 = test_report.scalability_analysis.get('projected_100_docs', {})
            logger.info(f"   100 documents: ~{proj_100.get('estimated_time_hours', 0):.1f}h, ${proj_100.get('estimated_cost_usd', 0):.0f}")
            
            proj_1000 = test_report.scalability_analysis.get('projected_1000_docs', {})
            logger.info(f"   1000 documents: ~{proj_1000.get('estimated_time_hours', 0):.1f}h, ${proj_1000.get('estimated_cost_usd', 0):.0f}")
        
        return overall_success
        
    except Exception as e:
        logger.error(f"❌ Comprehensive test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_comprehensive_summary(test_report, verification_results):
    """Generate comprehensive summary of test and verification results"""
    
    # System-by-system evidence summary
    evidence_by_system = {}
    for result in verification_results:
        if result.system not in evidence_by_system:
            evidence_by_system[result.system] = {"verified": 0, "total": 0}
        
        evidence_by_system[result.system]["total"] += 1
        if result.verified:
            evidence_by_system[result.system]["verified"] += 1
    
    # Key findings
    key_findings = []
    
    if test_report.overall_success_rate >= 90:
        key_findings.append("✅ Excellent system reliability - ready for production")
    elif test_report.overall_success_rate >= 70:
        key_findings.append("⚠️ Good system reliability - minor issues to address")
    else:
        key_findings.append("❌ System reliability issues - requires improvement")
    
    if sum(test_report.total_costs.values()) / test_report.total_documents < 1.0:
        key_findings.append("✅ Cost-efficient processing - under $1 per document")
    else:
        key_findings.append("⚠️ High processing costs - optimization needed")
    
    if test_report.average_processing_time_per_doc < 60:
        key_findings.append("✅ Fast processing - under 1 minute per document")
    else:
        key_findings.append("⚠️ Slow processing - performance optimization needed")
    
    # Evidence quality assessment
    verified_systems = len([s for s, data in evidence_by_system.items() 
                           if data["verified"] / data["total"] > 0.8])
    
    if verified_systems >= 3:
        key_findings.append("✅ Strong evidence verification across multiple systems")
    else:
        key_findings.append("❌ Weak evidence verification - system integration issues")
    
    summary = {
        "test_overview": {
            "test_id": test_report.test_id,
            "timestamp": datetime.now().isoformat(),
            "documents_processed": test_report.total_documents,
            "overall_success_rate": test_report.overall_success_rate,
            "total_processing_time": test_report.total_test_time_seconds,
            "total_cost": sum(test_report.total_costs.values())
        },
        "system_performance": {
            "gcs_success_rate": test_report.gcs_success_rate,
            "graphrag_success_rate": test_report.graphrag_success_rate,
            "embedding_success_rate": test_report.embedding_success_rate,
            "storage_success_rate": test_report.storage_success_rate
        },
        "evidence_verification": {
            "total_evidence_checks": len(verification_results),
            "verified_evidence": len([r for r in verification_results if r.verified]),
            "verification_rate": (len([r for r in verification_results if r.verified]) / 
                                len(verification_results)) * 100 if verification_results else 0,
            "evidence_by_system": evidence_by_system
        },
        "scalability_analysis": test_report.scalability_analysis,
        "key_findings": key_findings,
        "production_readiness": {
            "ready_for_production": (test_report.overall_success_rate >= 85 and 
                                   len([r for r in verification_results if r.verified]) / 
                                   len(verification_results) >= 0.8 if verification_results else False),
            "recommended_next_steps": [
                "Scale to 100 documents if success rate ≥ 85%",
                "Implement error handling and checkpointing",
                "Add monitoring and alerting",
                "Optimize costs if > $1 per document",
                "Performance tuning if > 60s per document"
            ]
        }
    }
    
    return summary

def save_summary_report(summary):
    """Save comprehensive summary report"""
    filename = f"comprehensive_4system_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    import json
    with open(filename, 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    return filename

async def main():
    """Main execution function"""
    
    print("🚀 COMPREHENSIVE 4-SYSTEM SCALE TESTING")
    print("=" * 60)
    print("This will execute complete end-to-end testing with evidence verification")
    print("Processing 10 documents through all systems with concrete proof of functionality")
    print("")
    
    # Check if user wants to proceed
    response = input("Continue with comprehensive testing? (y/n): ")
    if response.lower() != 'y':
        print("Testing cancelled.")
        return
    
    success = await run_comprehensive_test()
    
    if success:
        print("\n🎉 COMPREHENSIVE TESTING SUCCESSFUL!")
        print("System is ready for production deployment.")
        sys.exit(0)
    else:
        print("\n❌ COMPREHENSIVE TESTING FAILED!")
        print("Address issues before production deployment.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())