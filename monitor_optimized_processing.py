#!/usr/bin/env python3
"""
Optimized Processing Monitor
===========================

Real-time monitoring for the optimized bulk loader with:
- Live progress tracking
- Performance metrics
- ETA calculations
- Resume recommendations
- System resource monitoring
"""

import json
import time
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Optional, List
import psutil
import argparse

class OptimizedProcessingMonitor:
    """Monitor for optimized bulk loading progress."""
    
    def __init__(self, state_file: str, csv_file: str):
        self.state_file = state_file
        self.csv_file = csv_file
        self.csv_size_bytes = self._get_csv_size()
        self.start_monitoring_time = time.time()
        
    def _get_csv_size(self) -> int:
        """Get the total size of the CSV file."""
        try:
            return os.path.getsize(self.csv_file)
        except Exception:
            return 0
    
    def _load_current_state(self) -> Optional[Dict]:
        """Load current processing state."""
        try:
            if not os.path.exists(self.state_file):
                return None
                
            with open(self.state_file, 'r') as f:
                return json.load(f)
        except Exception:
            return None
    
    def _calculate_csv_progress(self, byte_offset: int) -> float:
        """Calculate CSV processing progress percentage."""
        if self.csv_size_bytes == 0:
            return 0.0
        return min(100.0, (byte_offset / self.csv_size_bytes) * 100)
    
    def _estimate_eta(self, state: Dict) -> str:
        """Estimate time to completion."""
        try:
            current_phase = state.get('phase')
            
            if current_phase == 'BULK_INGEST':
                # ETA based on CSV progress
                csv_progress = self._calculate_csv_progress(state.get('csv_byte_offset', 0))
                if csv_progress > 1:  # At least 1% complete
                    # Get timestamp from state
                    timestamp = state.get('timestamp')
                    if timestamp:
                        state_time = datetime.fromisoformat(timestamp)
                        elapsed = (datetime.now() - state_time).total_seconds()
                        
                        # Calculate rate and ETA
                        rate = csv_progress / elapsed if elapsed > 0 else 0
                        if rate > 0:
                            remaining_percent = 100 - csv_progress
                            eta_seconds = remaining_percent / rate
                            return str(timedelta(seconds=int(eta_seconds)))
                
                return "Calculating..."
            
            elif current_phase == 'STATE_MARKING':
                return "< 5 minutes (single SQL operation)"
            
            elif current_phase == 'CLASSIFICATION':
                # ETA based on classification rate
                classified = state.get('classified_count', 0)
                texas_cases = state.get('texas_cases_marked', 0)
                
                if texas_cases > 0 and classified > 0:
                    remaining = texas_cases - classified
                    # Estimate 100 classifications per minute
                    eta_minutes = remaining / 100
                    return str(timedelta(minutes=int(eta_minutes)))
                
                return "Calculating..."
            
            return "Unknown"
            
        except Exception:
            return "Error calculating"
    
    def _get_system_resources(self) -> Dict:
        """Get current system resource usage."""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'memory_available_gb': psutil.virtual_memory().available / (1024**3),
                'disk_usage_percent': psutil.disk_usage('/').percent
            }
        except Exception:
            return {
                'cpu_percent': 0,
                'memory_percent': 0,
                'memory_available_gb': 0,
                'disk_usage_percent': 0
            }
    
    def _format_performance_metrics(self, state: Dict) -> str:
        """Format performance metrics for display."""
        stats = state.get('stats', {})
        
        # Bulk ingest metrics
        bulk_stats = stats.get('bulk_ingest', {})
        processed = bulk_stats.get('processed', 0)
        gcs_uploads = bulk_stats.get('gcs_uploads', 0)
        errors = bulk_stats.get('errors', 0)
        
        # Classification metrics
        classification_stats = stats.get('classification', {})
        pi_mm_cases = classification_stats.get('pi_mm_cases', 0)
        other_cases = classification_stats.get('other_cases', 0)
        class_errors = classification_stats.get('errors', 0)
        
        return f"""
📊 Performance Metrics:
   Records Processed: {processed:,}
   GCS Uploads: {gcs_uploads:,}
   Processing Errors: {errors:,}
   
🧠 Classification Stats:
   PI/MM Cases: {pi_mm_cases:,}
   Other Cases: {other_cases:,}
   Classification Errors: {class_errors:,}
        """.strip()
    
    def _generate_recommendations(self, state: Dict, resources: Dict) -> List[str]:
        """Generate optimization recommendations."""
        recommendations = []
        
        # Resource-based recommendations
        if resources['memory_percent'] > 90:
            recommendations.append("⚠️ High memory usage - consider reducing batch size")
        
        if resources['cpu_percent'] > 95:
            recommendations.append("⚠️ High CPU usage - system may be overloaded")
        
        if resources['disk_usage_percent'] > 90:
            recommendations.append("⚠️ Low disk space - monitor closely")
        
        # Performance-based recommendations
        stats = state.get('stats', {})
        bulk_stats = stats.get('bulk_ingest', {})
        
        error_rate = bulk_stats.get('errors', 0) / max(bulk_stats.get('processed', 1), 1)
        if error_rate > 0.01:  # More than 1% errors
            recommendations.append("⚠️ High error rate - check logs for issues")
        
        # Phase-specific recommendations
        current_phase = state.get('phase')
        if current_phase == 'BULK_INGEST':
            processed = bulk_stats.get('processed', 0)
            gcs_uploads = bulk_stats.get('gcs_uploads', 0)
            if processed >= 100:  # Avoid startup false positive before any work is done
                gcs_success_rate = gcs_uploads / max(processed, 1)
                if gcs_success_rate < 0.95:  # Less than 95% GCS upload success
                    recommendations.append("⚠️ GCS upload issues - check connectivity")

        if not recommendations:
            recommendations.append("✅ Processing appears healthy")
        
        return recommendations
    
    def display_status(self) -> None:
        """Display current processing status."""
        state = self._load_current_state()
        
        if not state:
            print("❌ No processing state found")
            print(f"Expected state file: {self.state_file}")
            return
        
        # Clear screen for real-time updates
        os.system('clear' if os.name == 'posix' else 'cls')
        
        # Header
        print("🚀 OPTIMIZED BULK LOADER - LIVE MONITOR")
        print("=" * 50)
        
        # Current status
        current_phase = state.get('phase', 'UNKNOWN')
        total_processed = state.get('total_processed', 0)
        
        print(f"📋 Current Phase: {current_phase}")
        print(f"📊 Total Processed: {total_processed:,} records")
        
        # Phase-specific progress
        if current_phase == 'BULK_INGEST':
            csv_progress = self._calculate_csv_progress(state.get('csv_byte_offset', 0))
            print(f"📈 CSV Progress: {csv_progress:.1f}%")
            print(f"🗂️ Batch ID: {state.get('last_batch_id', 0):,}")
        
        elif current_phase == 'STATE_MARKING':
            texas_cases = state.get('texas_cases_marked', 0)
            print(f"🏴󠁵󠁳󠁴󠁸󠁿 Texas Cases Marked: {texas_cases:,}")
        
        elif current_phase == 'CLASSIFICATION':
            classified = state.get('classified_count', 0)
            texas_total = state.get('texas_cases_marked', 0)
            if texas_total > 0:
                class_progress = (classified / texas_total) * 100
                print(f"🧠 Classification Progress: {class_progress:.1f}% ({classified:,}/{texas_total:,})")
        
        # ETA
        eta = self._estimate_eta(state)
        print(f"⏱️ Estimated Time Remaining: {eta}")
        
        # Performance metrics
        print(self._format_performance_metrics(state))
        
        # System resources
        resources = self._get_system_resources()
        print(f"""
💻 System Resources:
   CPU Usage: {resources['cpu_percent']:.1f}%
   Memory Usage: {resources['memory_percent']:.1f}%
   Available Memory: {resources['memory_available_gb']:.1f} GB
   Disk Usage: {resources['disk_usage_percent']:.1f}%
        """.strip())
        
        # Recommendations
        recommendations = self._generate_recommendations(state, resources)
        print("\n📝 Recommendations:")
        for rec in recommendations:
            print(f"   {rec}")
        
        # Last updated
        timestamp = state.get('timestamp', '')
        if timestamp:
            try:
                last_update = datetime.fromisoformat(timestamp)
                time_since = datetime.now() - last_update
                print(f"\n🕐 Last Updated: {time_since.total_seconds():.0f} seconds ago")
            except:
                print(f"\n🕐 Last Updated: {timestamp}")
        
        print(f"\n📁 State File: {self.state_file}")
        print("Press Ctrl+C to exit monitor")
    
    def monitor_continuously(self, interval: int = 10) -> None:
        """Monitor processing continuously."""
        try:
            while True:
                self.display_status()
                time.sleep(interval)
        except KeyboardInterrupt:
            print("\n👋 Monitoring stopped")

def main():
    parser = argparse.ArgumentParser(description="Monitor optimized bulk processing")
    parser.add_argument("--csv-file", required=True, help="Path to the CSV file being processed")
    parser.add_argument("--interval", type=int, default=10, help="Update interval in seconds")
    parser.add_argument("--once", action="store_true", help="Show status once and exit")
    
    args = parser.parse_args()
    
    # Determine state file path
    state_file = f"{args.csv_file}.optimized.state"
    
    monitor = OptimizedProcessingMonitor(state_file, args.csv_file)
    
    if args.once:
        monitor.display_status()
    else:
        monitor.monitor_continuously(args.interval)

if __name__ == "__main__":
    main()