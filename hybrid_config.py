#!/usr/bin/env python3
"""
Hybrid Loader Configuration and Environment Setup
================================================

Configuration management and environment validation for hybrid enhanced loader.
"""

import os
import sys
import json
from typing import Dict, Optional, List
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class HybridConfig:
    """Configuration for hybrid enhanced loader."""
    
    # Processing settings
    batch_size: int = 5000
    num_workers: int = 8
    skip_classification: bool = False
    
    # Storage settings
    gcs_bucket: str = "texas-laws-personalinjury"
    enable_bulk_gcs: bool = True
    enable_bulk_db: bool = True
    
    # Database settings
    supabase_url: str = ""
    supabase_service_key: str = ""
    supabase_db_password: str = ""
    direct_postgres_url: str = ""
    
    # Performance settings
    gcs_upload_timeout: int = 300
    db_insert_timeout: int = 300
    max_memory_gb: float = 8.0
    
    # Resume settings
    enable_resume: bool = True
    checkpoint_interval: int = 1  # batches
    
    # Logging settings
    log_level: str = "INFO"
    log_file: str = "hybrid_enhanced_loader.log"
    
    @classmethod
    def from_env(cls) -> 'HybridConfig':
        """Create configuration from environment variables."""
        
        config = cls()
        
        # Required environment variables
        config.supabase_url = os.getenv("SUPABASE_URL", "")
        config.supabase_service_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "")
        config.supabase_db_password = os.getenv("SUPABASE_DB_PASSWORD", "")
        
        # Optional overrides
        config.batch_size = int(os.getenv("HYBRID_BATCH_SIZE", config.batch_size))
        config.num_workers = int(os.getenv("HYBRID_NUM_WORKERS", config.num_workers))
        config.gcs_bucket = os.getenv("GCS_BUCKET", config.gcs_bucket)
        config.skip_classification = os.getenv("SKIP_CLASSIFICATION", "false").lower() == "true"
        
        # Performance settings
        config.enable_bulk_gcs = os.getenv("ENABLE_BULK_GCS", "true").lower() == "true"
        config.enable_bulk_db = os.getenv("ENABLE_BULK_DB", "true").lower() == "true"
        config.max_memory_gb = float(os.getenv("MAX_MEMORY_GB", config.max_memory_gb))
        
        # Build direct PostgreSQL URL if available
        if config.supabase_url and config.supabase_db_password:
            try:
                project_ref = config.supabase_url.replace("https://", "").replace(".supabase.co", "")
                config.direct_postgres_url = f"postgresql://postgres:{config.supabase_db_password}@db.{project_ref}.supabase.co:5432/postgres"
                logger.info("✅ Built direct PostgreSQL connection URL")
            except Exception as e:
                logger.warning(f"⚠️ Could not build PostgreSQL URL: {e}")
        
        return config
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Required fields
        if not self.supabase_url:
            errors.append("SUPABASE_URL is required")
        
        if not self.supabase_service_key:
            errors.append("SUPABASE_SERVICE_ROLE_KEY is required")
        
        if self.enable_bulk_db and not self.supabase_db_password:
            errors.append("SUPABASE_DB_PASSWORD is required for bulk database operations")
        
        # Validate ranges
        if self.batch_size < 100 or self.batch_size > 50000:
            errors.append("batch_size must be between 100 and 50000")
        
        if self.num_workers < 1 or self.num_workers > 32:
            errors.append("num_workers must be between 1 and 32")
        
        if self.max_memory_gb < 1.0 or self.max_memory_gb > 64.0:
            errors.append("max_memory_gb must be between 1.0 and 64.0")
        
        return errors
    
    def to_dict(self) -> Dict:
        """Convert to dictionary (excluding sensitive data)."""
        data = {
            'batch_size': self.batch_size,
            'num_workers': self.num_workers,
            'skip_classification': self.skip_classification,
            'gcs_bucket': self.gcs_bucket,
            'enable_bulk_gcs': self.enable_bulk_gcs,
            'enable_bulk_db': self.enable_bulk_db,
            'gcs_upload_timeout': self.gcs_upload_timeout,
            'db_insert_timeout': self.db_insert_timeout,
            'max_memory_gb': self.max_memory_gb,
            'enable_resume': self.enable_resume,
            'checkpoint_interval': self.checkpoint_interval,
            'log_level': self.log_level,
            'log_file': self.log_file
        }
        return data

class EnvironmentChecker:
    """Check environment and dependencies for hybrid loader."""
    
    @staticmethod
    def check_python_version() -> bool:
        """Check Python version compatibility."""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            logger.error("❌ Python 3.8+ is required")
            return False
        
        logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro}")
        return True
    
    @staticmethod
    def check_required_packages() -> bool:
        """Check required Python packages."""
        package_imports = {
            'supabase': 'supabase',
            'google-cloud-storage': 'google.cloud.storage',
            'google-generativeai': 'google.generativeai',
            'psycopg2': 'psycopg2',
            'python-dotenv': 'dotenv'
        }

        missing_packages = []

        for package_name, import_name in package_imports.items():
            try:
                __import__(import_name)
                logger.info(f"✅ {package_name}")
            except ImportError:
                missing_packages.append(package_name)
                logger.error(f"❌ {package_name} not found")

        if missing_packages:
            logger.error(f"Install missing packages: pip install {' '.join(missing_packages)}")
            return False

        return True
    
    @staticmethod
    def check_file_dependencies(csv_file: Optional[str] = None) -> bool:
        """Check required files exist."""
        required_files = [
            'enhanced_bulk_loader.py',
            'texas_cluster_ids.pkl'  # This should exist from previous runs
        ]
        
        if csv_file:
            required_files.append(csv_file)
        
        missing_files = []
        
        for file_path in required_files:
            if os.path.exists(file_path):
                logger.info(f"✅ {file_path}")
            else:
                missing_files.append(file_path)
                logger.error(f"❌ {file_path} not found")
        
        if missing_files:
            logger.error("Missing required files. Please ensure all dependencies are in place.")
            return False
        
        return True
    
    @staticmethod
    def check_memory_requirements(config: HybridConfig) -> bool:
        """Check system memory requirements."""
        try:
            import psutil
            
            # Get available memory
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024 ** 3)
            
            if available_gb < config.max_memory_gb:
                logger.warning(f"⚠️ Available memory ({available_gb:.1f}GB) is less than configured limit ({config.max_memory_gb}GB)")
                return False
            
            logger.info(f"✅ Memory: {available_gb:.1f}GB available")
            return True
            
        except ImportError:
            logger.warning("⚠️ psutil not available, cannot check memory requirements")
            return True  # Don't fail if we can't check
        except Exception as e:
            logger.warning(f"⚠️ Memory check failed: {e}")
            return True
    
    @staticmethod
    def run_full_check(config: HybridConfig, csv_file: Optional[str] = None) -> bool:
        """Run all environment checks."""
        logger.info("🔍 Running environment checks...")
        
        checks = [
            ("Python Version", EnvironmentChecker.check_python_version),
            ("Required Packages", EnvironmentChecker.check_required_packages),
            ("File Dependencies", lambda: EnvironmentChecker.check_file_dependencies(csv_file)),
            ("Memory Requirements", lambda: EnvironmentChecker.check_memory_requirements(config))
        ]
        
        all_passed = True
        
        for check_name, check_func in checks:
            logger.info(f"\n--- {check_name} ---")
            try:
                if not check_func():
                    all_passed = False
            except Exception as e:
                logger.error(f"❌ {check_name} check failed: {e}")
                all_passed = False
        
        if all_passed:
            logger.info("\n🎉 All environment checks passed!")
        else:
            logger.error("\n💥 Some environment checks failed!")
        
        return all_passed

def setup_logging(config: HybridConfig):
    """Setup logging configuration."""
    
    level = getattr(logging, config.log_level.upper())
    
    # Configure logging
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(config.log_file)
        ]
    )
    
    logger.info(f"✅ Logging configured: {config.log_level} -> {config.log_file}")

def main():
    """Configuration validation utility."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Hybrid Loader Configuration")
    parser.add_argument("--validate", action="store_true", help="Validate configuration")
    parser.add_argument("--check-env", action="store_true", help="Check environment")
    parser.add_argument("--csv-file", help="CSV file to validate")
    parser.add_argument("--show-config", action="store_true", help="Show current configuration")
    
    args = parser.parse_args()
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Create configuration
    config = HybridConfig.from_env()
    
    if args.show_config:
        print("\n📋 Current Configuration:")
        print(json.dumps(config.to_dict(), indent=2))
    
    if args.validate:
        print("\n🔍 Validating configuration...")
        errors = config.validate()
        
        if errors:
            print("❌ Configuration errors:")
            for error in errors:
                print(f"  - {error}")
            sys.exit(1)
        else:
            print("✅ Configuration is valid")
    
    if args.check_env:
        success = EnvironmentChecker.run_full_check(config, args.csv_file)
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()