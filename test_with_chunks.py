#!/usr/bin/env python3
"""
Test GraphRAG Pipeline with Chunks Data
Use existing chunks data since GCS access is limited
"""

import os
import asyncio
from supabase import create_client
from dotenv import load_dotenv

load_dotenv()

async def test_with_chunks_data():
    """Test using chunks data directly"""
    print("=== Testing with Chunks Data ===\n")
    
    try:
        # Initialize Supabase
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_KEY")
        supabase = create_client(supabase_url, supabase_key)
        
        # Get some chunks with substantial content
        print("1. Fetching chunks with good content...")
        response = supabase.table("chunks").select("*").gte("char_end", 1000).limit(5).execute()
        
        if not response.data:
            print("❌ No chunks found")
            return
        
        print(f"✅ Found {len(response.data)} chunks")
        
        # Import the GraphRAG pipeline
        from setup_legal_graphrag import LegalGraphRAGPipeline
        pipeline = LegalGraphRAGPipeline()
        
        # Process each chunk
        for i, chunk in enumerate(response.data):
            print(f"\n2.{i+1} Processing chunk {chunk['id']}...")
            print(f"     Document: {chunk.get('document_name', 'Unknown')}")
            print(f"     Content length: {len(chunk.get('content', ''))}")
            
            # Create document format for GraphRAG
            doc = {
                "id": f"chunk_{chunk['id']}",
                "plain_text": chunk.get('content', ''),
                "case_name": chunk.get('document_name', 'Unknown'),
                "practice_area": chunk.get('practice_area', 'unknown'),
                "court_name": f"{chunk.get('jurisdiction', 'Unknown')} Court",
                "source": "chunks_table"
            }
            
            try:
                # Process with GraphRAG
                result = await pipeline.process_legal_document(doc)
                
                print(f"     ✅ GraphRAG Results:")
                print(f"        Entities: {len(result.get('entities', []))}")
                print(f"        Relationships: {len(result.get('relationships', []))}")
                print(f"        Processing time: {result.get('processing_time', 0):.2f}s")
                
                # Show some entities
                entities = result.get('entities', [])
                if entities:
                    print(f"        Sample entities:")
                    for entity in entities[:3]:
                        print(f"          - {entity.get('type', 'Unknown')}: {entity.get('text', 'Unknown')}")
                
                # Show some relationships
                relationships = result.get('relationships', [])
                if relationships:
                    print(f"        Sample relationships:")
                    for rel in relationships[:2]:
                        print(f"          - {rel.get('type', 'Unknown')}: {rel.get('start_node_id', '?')} -> {rel.get('end_node_id', '?')}")
                
            except Exception as e:
                print(f"     ❌ GraphRAG failed: {e}")
                continue
        
        print("\n=== Chunks Test Complete ===")
        
    except Exception as e:
        print(f"❌ Chunks test failed: {e}")
        import traceback
        traceback.print_exc()

async def create_test_documents():
    """Create test documents for GraphRAG processing"""
    print("=== Creating Test Documents for Processing ===\n")
    
    # Sample legal documents
    test_documents = [
        {
            "id": "test_pi_case_1",
            "case_name": "Johnson v. Metro Transit Authority",
            "practice_area": "personal_injury",
            "court_name": "Harris County District Court",
            "plain_text": """
            In the case of Johnson v. Metro Transit Authority, the plaintiff Sarah Johnson filed suit against 
            the Metro Transit Authority for injuries sustained in a bus accident on March 15, 2023. 
            Judge William Rodriguez presided over the proceedings in the 164th Judicial District Court of 
            Harris County, Texas. 
            
            Attorney Michael Chen represented the plaintiff, while defense counsel Jennifer Martinez 
            represented the transit authority. The incident occurred when the Metro bus, operated by 
            defendant driver Robert Thompson, failed to yield at a traffic signal, resulting in a collision 
            with plaintiff's vehicle.
            
            Plaintiff sustained significant injuries including a fractured left arm, cervical strain, 
            and psychological trauma requiring ongoing treatment. Medical expenses totaled $45,000, 
            with additional lost wages of $15,000 over a six-month recovery period.
            
            Expert witness Dr. Patricia Williams, an orthopedic surgeon, testified regarding the 
            severity of plaintiff's injuries and the likelihood of permanent disability. The jury 
            awarded $125,000 in compensatory damages and $25,000 in punitive damages against 
            the Metro Transit Authority.
            
            The court cited precedent from Martinez v. City Transit, 567 S.W.3d 234 (Tex. 2022), 
            in determining liability standards for municipal transportation entities.
            """
        },
        {
            "id": "test_commercial_case_1", 
            "case_name": "Tech Solutions Inc. v. DataCorp LLC",
            "practice_area": "commercial",
            "court_name": "Dallas County District Court",
            "plain_text": """
            Tech Solutions Inc. filed a breach of contract action against DataCorp LLC in the 
            District Court of Dallas County, seeking $500,000 in damages. Judge Maria Santos 
            presided over the bench trial held from November 1-5, 2023.
            
            The dispute centered on a software licensing agreement executed on January 10, 2022, 
            whereby DataCorp was to provide cloud hosting services for Tech Solutions' proprietary 
            customer management platform. Attorney Lisa Park represented Tech Solutions, while 
            attorney David Kim appeared for DataCorp.
            
            Plaintiff alleged that DataCorp failed to maintain agreed-upon service levels, resulting 
            in system downtime that cost Tech Solutions significant client relationships and revenue. 
            DataCorp counterclaimed for $150,000 in unpaid hosting fees and early termination penalties.
            
            Key witness testimony came from Chief Technology Officer Amanda Foster, who detailed 
            the technical failures and business impact. Expert witness Dr. Robert Chang provided 
            analysis of industry standard service level agreements.
            
            The court found in favor of Tech Solutions on the primary claim but reduced damages 
            to $300,000 based on plaintiff's failure to mitigate losses. DataCorp's counterclaim 
            was granted in part, awarding $75,000 in unpaid fees. 
            
            Attorney's fees of $45,000 were awarded to Tech Solutions under the contract's 
            prevailing party clause, referencing Smith v. Johnson Enterprises, 234 S.W.3d 567 (Tex. App. 2021).
            """
        }
    ]
    
    # Test each document with GraphRAG
    from setup_legal_graphrag import LegalGraphRAGPipeline
    pipeline = LegalGraphRAGPipeline()
    
    results = []
    
    for doc in test_documents:
        print(f"Processing: {doc['case_name']}")
        
        try:
            result = await pipeline.process_legal_document(doc)
            results.append({
                "document": doc,
                "result": result
            })
            
            print(f"  ✅ Entities: {len(result.get('entities', []))}")
            print(f"  ✅ Relationships: {len(result.get('relationships', []))}")
            
            # Show entities
            for entity in result.get('entities', [])[:5]:
                print(f"    🏷️  {entity.get('type')}: {entity.get('text')}")
            
            # Show relationships  
            for rel in result.get('relationships', [])[:3]:
                print(f"    🔗 {rel.get('type')}: {rel.get('start_node_id')} -> {rel.get('end_node_id')}")
                
        except Exception as e:
            print(f"  ❌ Failed: {e}")
            
        print()
    
    return results

if __name__ == "__main__":
    # Test with chunks data first
    asyncio.run(test_with_chunks_data())
    
    print("\n" + "="*60 + "\n")
    
    # Test with created documents
    asyncio.run(create_test_documents())