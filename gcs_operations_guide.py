#!/usr/bin/env python3
"""
GCS Operations Guide for Texas Laws Personal Injury Project
Comprehensive examples for developers and LLM coders
"""

import os
import json
import gzip
from datetime import datetime
from google.cloud import storage
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class TexasLawsGCS:
    """GCS operations for Texas Laws Personal Injury project."""
    
    def __init__(self):
        """Initialize GCS client and bucket."""
        self.client = storage.Client()
        self.bucket_name = os.getenv('GCS_BUCKET_NAME', 'texas-laws-personalinjury')
        self.bucket = self.client.bucket(self.bucket_name)
        
    def upload_case_document(self, case_id, content, jurisdiction='TX'):
        """Upload a legal case document."""
        try:
            # Create structured path
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            gcs_path = f"{jurisdiction}/opinions/{case_id}_{timestamp}.json.gz"
            
            # Compress content
            compressed_content = gzip.compress(json.dumps(content).encode('utf-8'))
            
            # Upload
            blob = self.bucket.blob(gcs_path)
            blob.upload_from_string(compressed_content, content_type='application/gzip')
            
            print(f"✅ Uploaded case {case_id} to {gcs_path}")
            return gcs_path
        except Exception as e:
            print(f"❌ Upload failed for case {case_id}: {e}")
            return None
    
    def download_case_document(self, gcs_path):
        """Download and decompress a case document."""
        try:
            blob = self.bucket.blob(gcs_path)
            compressed_data = blob.download_as_bytes()
            
            # Decompress
            content = gzip.decompress(compressed_data).decode('utf-8')
            return json.loads(content)
        except Exception as e:
            print(f"❌ Download failed for {gcs_path}: {e}")
            return None
    
    def list_texas_cases(self, limit=100):
        """List Texas case files."""
        try:
            blobs = self.bucket.list_blobs(prefix='TX/opinions/', max_results=limit)
            cases = []
            for blob in blobs:
                cases.append({
                    'path': blob.name,
                    'size': blob.size,
                    'created': blob.time_created,
                    'case_id': blob.name.split('/')[-1].split('_')[0]
                })
            return cases
        except Exception as e:
            print(f"❌ Listing failed: {e}")
            return []
    
    def upload_batch_archive(self, files_dict, batch_id):
        """Upload multiple files as a tar.gz batch."""
        import tarfile
        import io
        
        try:
            # Create tar.gz in memory
            tar_buffer = io.BytesIO()
            with tarfile.open(fileobj=tar_buffer, mode='w:gz') as tar:
                for filename, content in files_dict.items():
                    # Add file to tar
                    file_data = json.dumps(content).encode('utf-8')
                    tarinfo = tarfile.TarInfo(name=filename)
                    tarinfo.size = len(file_data)
                    tar.addfile(tarinfo, io.BytesIO(file_data))
            
            # Upload batch
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            gcs_path = f"batches/batch_{batch_id}_{timestamp}.tar.gz"
            
            blob = self.bucket.blob(gcs_path)
            tar_buffer.seek(0)
            blob.upload_from_file(tar_buffer, content_type='application/gzip')
            
            print(f"✅ Uploaded batch {batch_id} with {len(files_dict)} files")
            return gcs_path
        except Exception as e:
            print(f"❌ Batch upload failed: {e}")
            return None
    
    def get_bucket_stats(self):
        """Get comprehensive bucket statistics."""
        try:
            stats = {
                'folders': {},
                'total_files': 0,
                'total_size': 0
            }
            
            # Analyze all blobs
            for blob in self.bucket.list_blobs():
                stats['total_files'] += 1
                stats['total_size'] += blob.size
                
                # Categorize by folder
                folder = blob.name.split('/')[0] if '/' in blob.name else 'root'
                if folder not in stats['folders']:
                    stats['folders'][folder] = {'count': 0, 'size': 0}
                
                stats['folders'][folder]['count'] += 1
                stats['folders'][folder]['size'] += blob.size
            
            # Convert sizes to MB
            stats['total_size_mb'] = stats['total_size'] / (1024 * 1024)
            for folder in stats['folders']:
                stats['folders'][folder]['size_mb'] = stats['folders'][folder]['size'] / (1024 * 1024)
            
            return stats
        except Exception as e:
            print(f"❌ Stats failed: {e}")
            return None

# Usage examples
if __name__ == "__main__":
    # Initialize
    gcs = TexasLawsGCS()
    
    # Example 1: Upload a case document
    case_data = {
        'id': '12345',
        'title': 'Smith v. Jones',
        'court': 'Texas Supreme Court',
        'date': '2024-01-15',
        'content': 'Legal opinion content...',
        'practice_area': 'Personal Injury'
    }
    
    gcs_path = gcs.upload_case_document('12345', case_data)
    
    # Example 2: List Texas cases
    texas_cases = gcs.list_texas_cases(limit=10)
    print(f"Found {len(texas_cases)} Texas cases")
    
    # Example 3: Get bucket statistics
    stats = gcs.get_bucket_stats()
    if stats:
        print(f"Bucket contains {stats['total_files']} files ({stats['total_size_mb']:.1f} MB)")
        for folder, data in stats['folders'].items():
            print(f"  {folder}/: {data['count']} files ({data['size_mb']:.1f} MB)")

# Authentication troubleshooting
def troubleshoot_auth():
    """Troubleshoot GCS authentication issues."""
    print("🔍 GCS Authentication Troubleshooting")
    print("=" * 50)
    
    # Check environment variables
    creds_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    print(f"GOOGLE_APPLICATION_CREDENTIALS: {'✅ Set' if creds_path else '❌ Not set'}")
    
    if creds_path:
        print(f"Credentials file: {creds_path}")
        print(f"File exists: {'✅ Yes' if os.path.exists(creds_path) else '❌ No'}")
        
        # Try to load and validate
        try:
            with open(creds_path, 'r') as f:
                creds = json.load(f)
            print(f"Service account: {creds.get('client_email', 'Unknown')}")
            print(f"Project ID: {creds.get('project_id', 'Unknown')}")
        except Exception as e:
            print(f"❌ Cannot read credentials: {e}")
    
    # Test connection
    try:
        client = storage.Client()
        bucket = client.bucket('texas-laws-personalinjury')
        # Try to list a few objects
        blobs = list(bucket.list_blobs(max_results=1))
        print("✅ GCS connection successful")
    except Exception as e:
        print(f"❌ GCS connection failed: {e}")
        print("\n🔧 Possible solutions:")
        print("1. Ensure GOOGLE_APPLICATION_CREDENTIALS points to valid JSON file")
        print("2. Verify service account has Storage Object Viewer permissions")
        print("3. Check if bucket name is correct: texas-laws-personalinjury")
        print("4. Try: gcloud auth application-default login")

if __name__ == "__main__":
    troubleshoot_auth()
