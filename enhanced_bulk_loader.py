#!/usr/bin/env python3
"""
Enhanced Texas Bulk Loader with GCS Storage
Processes the full 50GB CourtListener opinions CSV with:
- Pickle-based Texas filtering
- GCS storage for full text content
- 1:1 cross-tracking between Supabase and GCS
- Proper database schema compliance
"""

import bz2
import csv
import pickle
import time
import hashlib
import json
import os
import logging
from typing import Set, Dict, Optional
from datetime import datetime
from supabase import create_client, Client
from google.cloud import storage
import google.generativeai as genai

# Set CSV field size limit for large legal documents
csv.field_size_limit(10**9)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedBulkLoader:
    """Enhanced bulk loader with GCS storage and 1:1 cross-tracking."""

    def __init__(self, batch_size: int = 5000, gcs_bucket: str = "texas-laws-personalinjury", skip_classification: bool = False, csv_file: str = None):
        print("🚀 Starting EnhancedBulkLoader initialization...")

        self.batch_size = batch_size
        self.gcs_bucket = gcs_bucket
        self.skip_classification = skip_classification
        self.texas_cluster_ids: Set[int] = set()
        self.processed_count = 0
        self.duplicate_count = 0
        self.error_count = 0
        self.texas_matches = 0
        self.pi_mm_cases = 0

        # Resume capability
        self.csv_file = csv_file
        self.state_file = f"{csv_file}.state" if csv_file else "enhanced_bulk_loader.state"
        self.resume_offset = 0
        print(f"📁 State file: {self.state_file}")

        # Gemini rate limiting
        self.gemini_requests = 0
        self.gemini_start_time = time.time()
        self.max_requests_per_minute = int(os.getenv("GEMINI_MAX_REQUESTS_PER_MIN", "200"))
        self.gemini_failures = 0
        self.max_gemini_failures = 10  # Switch to keywords after 10 failures

        # Initialize clients
        print("🔌 Initializing Supabase client...")
        self.supabase = self._init_supabase()
        print("✅ Supabase client initialized")

        print("🔌 Initializing GCS client...")
        self.gcs_client = self._init_gcs()
        print("✅ GCS client initialized")

        print("🔌 Initializing Gemini model...")
        self.gemini_model = self._init_gemini()
        print("✅ Gemini model initialized")

        # Load existing content hashes for deduplication
        print("📊 Loading existing content hashes...")
        self.existing_hashes = self._load_existing_hashes()
        print(f"✅ Loaded {len(self.existing_hashes)} existing hashes")

        # Load Texas cluster IDs
        print("🏴󠁵󠁳󠁴󠁸󠁿 Loading Texas cluster IDs...")
        self._load_texas_cluster_pickle()
        print(f"✅ Loaded {len(self.texas_cluster_ids)} Texas cluster IDs")

        # Load resume state
        print("🔄 Loading resume state...")
        self._load_resume_state()
        print(f"✅ Resume state loaded (offset: {self.resume_offset})")

        print("🎉 EnhancedBulkLoader initialization complete!")
    
    def _init_supabase(self) -> Client:
        """Initialize Supabase client."""
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        if not supabase_url or not supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
        
        return create_client(supabase_url, supabase_key)
    
    def _init_gcs(self) -> storage.Client:
        """Initialize Google Cloud Storage client."""
        try:
            # Try to use default credentials (gcloud auth)
            client = storage.Client()

            # Test access to the bucket
            bucket = client.bucket(self.gcs_bucket)
            bucket.reload()  # This will fail if no access

            logger.info(f"GCS client initialized successfully for bucket: {self.gcs_bucket}")
            return client

        except Exception as e:
            logger.error(f"Failed to initialize GCS client: {e}")
            logger.error("Make sure you're authenticated with: gcloud auth application-default login")
            raise

    def _init_gemini(self):
        """Initialize Gemini 2.5 Flash Lite for practice area classification."""
        try:
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                logger.warning("GEMINI_API_KEY not found, falling back to keyword classification")
                return None

            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-2.5-flash-lite')

            logger.info("Gemini 2.5 Flash Lite initialized successfully for practice area classification")
            return model

        except Exception as e:
            logger.warning(f"Failed to initialize Gemini: {e}, falling back to keyword classification")
            return None

    def _rate_limit_gemini(self):
        """Implement rate limiting for Gemini API calls."""
        current_time = time.time()
        elapsed = current_time - self.gemini_start_time

        # Reset counter every minute
        if elapsed >= 60:
            self.gemini_requests = 0
            self.gemini_start_time = current_time
            elapsed = 0

        # If we've hit the rate limit, wait
        if self.gemini_requests >= self.max_requests_per_minute:
            wait_time = 60 - elapsed + 1  # Wait until next minute + 1 second buffer
            logger.info(f"Rate limit reached ({self.gemini_requests}/{self.max_requests_per_minute}), waiting {wait_time:.1f}s")
            time.sleep(wait_time)
            self.gemini_requests = 0
            self.gemini_start_time = time.time()

        self.gemini_requests += 1

    def _load_existing_hashes(self) -> Set[str]:
        """Load existing content hashes for deduplication."""
        try:
            print("📊 Querying Supabase for existing content hashes...")
            logger.info("Loading existing content hashes for deduplication...")
            response = self.supabase.table('cases').select('content_hash').execute()
            print(f"📊 Database query completed, processing {len(response.data)} rows...")

            hashes = {row['content_hash'] for row in response.data if row.get('content_hash')}
            print(f"📊 Processed hashes: {len(hashes)} unique content hashes")
            logger.info(f"Loaded {len(hashes)} existing content hashes")
            return hashes

        except Exception as e:
            print(f"❌ Error loading existing hashes: {e}")
            logger.warning(f"Failed to load existing hashes: {e}")
            return set()
    
    def _load_texas_cluster_pickle(self) -> None:
        """Load Texas cluster IDs from pickle file."""
        pickle_file = "tex_clusters.p"

        try:
            print(f"🏴󠁵󠁳󠁴󠁸󠁿 Opening pickle file: {pickle_file}")
            logger.info(f"Loading Texas cluster IDs from {pickle_file}")

            print("🏴󠁵󠁳󠁴󠁸󠁿 Reading pickle data...")
            with open(pickle_file, 'rb') as f:
                self.texas_cluster_ids = pickle.load(f)

            print(f"🏴󠁵󠁳󠁴󠁸󠁿 Pickle loaded successfully: {len(self.texas_cluster_ids)} cluster IDs")
            logger.info(f"Loaded {len(self.texas_cluster_ids)} Texas cluster IDs from pickle")

        except Exception as e:
            print(f"❌ Error loading Texas cluster pickle: {e}")
            logger.error(f"Failed to load Texas cluster pickle: {e}")
            raise
    
    def _generate_content_hash(self, text: str) -> str:
        """Generate content hash for deduplication."""
        return hashlib.sha256(text.encode('utf-8')).hexdigest()
    
    def _is_duplicate(self, content_hash: str) -> bool:
        """Check if content hash already exists."""
        return content_hash in self.existing_hashes
    
    def _generate_gcs_path(self, case_id: str, cluster_id: str) -> str:
        """Generate GCS path for case document."""
        # Format: courtlistener/opinions/YYYY/cluster_CLUSTER_ID/opinion_CASE_ID.txt
        year = datetime.now().year
        return f"courtlistener/opinions/{year}/cluster_{cluster_id}/opinion_{case_id}.txt"
    
    def _upload_to_gcs(self, content: str, gcs_path: str) -> bool:
        """Upload content to GCS and return success status."""
        try:
            bucket = self.gcs_client.bucket(self.gcs_bucket)
            blob = bucket.blob(gcs_path)

            # Upload with metadata
            blob.metadata = {
                'source': 'courtlistener_csv',
                'uploaded_at': datetime.now().isoformat(),
                'content_type': 'legal_opinion',
                'content_length': str(len(content))
            }

            blob.upload_from_string(content, content_type='text/plain')
            logger.debug(f"Uploaded to GCS: {gcs_path} ({len(content)} bytes)")
            return True

        except Exception as e:
            logger.warning(f"Failed to upload to GCS {gcs_path}: {e}")
            return False

    def _load_resume_state(self) -> None:
        """Load resume state from previous run."""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    state = json.load(f)
                    self.resume_offset = state.get('byte_offset', 0)
                    self.processed_count = state.get('processed_count', 0)
                    self.duplicate_count = state.get('duplicate_count', 0)
                    self.error_count = state.get('error_count', 0)
                    self.texas_matches = state.get('texas_matches', 0)
                    logger.info(f"Resuming from byte offset: {self.resume_offset:,}")
                    logger.info(f"Previous progress: {self.processed_count:,} processed, {self.texas_matches:,} TX matches")
            else:
                self.resume_offset = 0
                logger.info("No resume state found, starting from beginning")
        except Exception as e:
            logger.warning(f"Failed to load resume state: {e}. Starting from beginning.")
            self.resume_offset = 0

    def _save_resume_state(self, byte_offset: int) -> None:
        """Save current progress for resume capability."""
        try:
            state = {
                'byte_offset': byte_offset,
                'timestamp': datetime.now().isoformat(),
                'processed_count': self.processed_count,
                'duplicate_count': self.duplicate_count,
                'error_count': self.error_count,
                'texas_matches': self.texas_matches,
                'pi_mm_cases': self.pi_mm_cases
            }
            with open(self.state_file, 'w') as f:
                json.dump(state, f, indent=2)
            logger.debug(f"Saved resume state at byte offset: {byte_offset:,}")
        except Exception as e:
            logger.warning(f"Failed to save resume state: {e}")

    def _classify_practice_area(self, plain_text: str) -> Dict:
        """Classify practice area using smart pre-filtering + Gemini for ambiguous cases."""

        # First, try keyword pre-filtering for obvious cases
        keyword_result = self._classify_with_keywords(plain_text)

        # If keyword classification is confident (>= 0.7), skip Gemini
        if keyword_result.get("confidence", 0) >= 0.7:
            return keyword_result

        # For ambiguous cases, use Gemini (if available and not too many failures)
        if self.gemini_model and plain_text.strip() and self.gemini_failures < self.max_gemini_failures:
            try:
                return self._classify_with_gemini(plain_text)
            except Exception as e:
                self.gemini_failures += 1
                logger.warning(f"Gemini classification failed ({self.gemini_failures}/{self.max_gemini_failures}): {e}, falling back to keywords")

                # If too many failures, disable Gemini for this session
                if self.gemini_failures >= self.max_gemini_failures:
                    logger.error(f"Gemini disabled after {self.max_gemini_failures} failures, using keywords only")

        # Fallback to keyword result
        return keyword_result

    def _classify_with_gemini(self, plain_text: str) -> Dict:
        """Classify practice area using Gemini 2.5 Flash Lite with rate limiting and retry."""
        import time
        import random

        # Truncate text to avoid token limits (keep first 1500 chars)
        text_sample = plain_text[:1500] if len(plain_text) > 1500 else plain_text

        prompt = f"""Analyze this legal case text and classify its primary practice area.

Text: "{text_sample}"

Respond with ONLY a JSON object in this exact format:
{{
    "primary_practice_area": "Medical Malpractice" | "Personal Injury" | "Other",
    "practice_areas": ["array of relevant practice areas"],
    "confidence": 0.0-1.0
}}

Focus on:
- Medical Malpractice: medical negligence, doctor/hospital liability, standard of care
- Personal Injury: accidents, negligence, tort liability, damages, wrongful death
- Other: all other legal areas

Be precise and confident."""

        # Retry logic with exponential backoff
        max_retries = 3
        base_delay = 2.0

        for attempt in range(max_retries):
            try:
                # Apply rate limiting
                self._rate_limit_gemini()

                response = self.gemini_model.generate_content(prompt)

                # Parse JSON response
                import re
                json_match = re.search(r'\{.*\}', response.text, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())

                    # Validate and return
                    return {
                        "primary_practice_area": result.get("primary_practice_area", "Other"),
                        "practice_areas": result.get("practice_areas", ["Other"]),
                        "confidence": float(result.get("confidence", 0.5)),
                        "reasoning": result.get("reasoning", "Gemini classification")
                    }
                else:
                    raise ValueError("Invalid JSON response from Gemini")

            except Exception as e:
                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.debug(f"Gemini attempt {attempt + 1} failed: {e}, retrying in {delay:.1f}s")
                    time.sleep(delay)
                else:
                    raise e

    def _classify_with_keywords(self, plain_text: str) -> Dict:
        """Enhanced keyword-based classification with smart pre-filtering."""
        if not plain_text:
            return {"primary_practice_area": "Other", "confidence": 0.5}

        content_lower = plain_text.lower()

        # Strong non-PI indicators (high confidence to skip Gemini)
        non_pi_keywords = [
            "criminal", "felony", "misdemeanor", "prosecution", "defendant pleaded",
            "divorce", "custody", "child support", "alimony", "marriage", "family court",
            "bankruptcy", "chapter 7", "chapter 11", "discharge", "trustee", "debtor",
            "contract", "breach of contract", "commercial", "business dispute", "corporate",
            "real estate", "property", "landlord", "tenant", "lease", "zoning",
            "employment", "wrongful termination", "discrimination", "harassment",
            "tax", "taxation", "irs", "revenue", "assessment"
        ]

        # Strong PI/MM indicators (high confidence)
        strong_pi_keywords = [
            "medical malpractice", "doctor", "physician", "hospital", "surgery",
            "surgical", "medical negligence", "standard of care", "misdiagnosis",
            "personal injury", "car accident", "motor vehicle", "slip and fall",
            "premises liability", "product liability", "wrongful death", "negligence"
        ]

        # Moderate PI indicators
        moderate_pi_keywords = [
            "accident", "injury", "damages", "tort", "liability", "compensation",
            "pain and suffering", "defective product", "automobile accident"
        ]

        non_pi_score = sum(1 for keyword in non_pi_keywords if keyword in content_lower)
        strong_pi_score = sum(1 for keyword in strong_pi_keywords if keyword in content_lower)
        moderate_pi_score = sum(1 for keyword in moderate_pi_keywords if keyword in content_lower)

        # High confidence non-PI (skip Gemini)
        if non_pi_score >= 2:
            return {"primary_practice_area": "Other", "confidence": 0.9}
        elif non_pi_score >= 1:
            return {"primary_practice_area": "Other", "confidence": 0.8}

        # High confidence PI/MM (skip Gemini)
        elif strong_pi_score >= 2:
            return {"primary_practice_area": "Personal Injury", "confidence": 0.9}
        elif strong_pi_score >= 1:
            return {"primary_practice_area": "Personal Injury", "confidence": 0.8}

        # Moderate confidence PI
        elif moderate_pi_score >= 2:
            return {"primary_practice_area": "Personal Injury", "confidence": 0.7}

        # Ambiguous - needs Gemini
        else:
            return {"primary_practice_area": "Other", "confidence": 0.3}
    
    def _extract_case_data(self, opinion_row: Dict) -> Optional[Dict]:
        """Extract case data from opinion row with GCS storage."""
        try:
            case_id = str(opinion_row.get("id"))
            cluster_id = str(opinion_row.get("cluster_id"))
            plain_text = opinion_row.get("plain_text", "")
            
            if not case_id or not cluster_id:
                return None
            
            # Generate content hash
            content_hash = self._generate_content_hash(plain_text)
            
            # Check for duplicates
            if self._is_duplicate(content_hash):
                return None
            
            # Generate GCS path
            gcs_path = self._generate_gcs_path(case_id, cluster_id)
            
            # Upload to GCS
            if not self._upload_to_gcs(plain_text, gcs_path):
                logger.warning(f"Failed to upload case {case_id} to GCS, skipping...")
                return None
            
            # Classify practice area (skip if requested)
            if self.skip_classification:
                classification = {
                    "primary_practice_area": None,
                    "practice_areas": [],
                    "confidence": 0.0,
                    "reasoning": "Classification skipped for faster ingestion"
                }
            else:
                classification = self._classify_practice_area(plain_text)
            
            # Extract date
            date_created = opinion_row.get("date_created")
            year_filed = None
            if date_created:
                try:
                    date_obj = datetime.fromisoformat(date_created.replace('Z', '+00:00'))
                    year_filed = date_obj.year
                except:
                    pass
            
            # Normalize document type to schema constraints
            raw_doc_type = opinion_row.get("type", "opinion")
            doc_type = str(raw_doc_type).lower()
            # Schema allows: opinion, precedential, nonprecedential, order, docket, unknown
            allowed_doc_types = {"opinion", "precedential", "nonprecedential", "order", "docket", "unknown"}
            if doc_type not in allowed_doc_types:
                doc_type = "unknown"  # Map invalid types to unknown
            
            # Build case record with 1:1 GCS cross-tracking
            case_record = {
                "id": case_id,
                "case_name": f"Texas Case {case_id}",
                "case_name_full": f"Texas Case {case_id}",
                "court_id": "texas-verified",
                "jurisdiction": "TX",
                "date_filed": date_created,
                "status": "Published",
                "docket_number": None,
                "nature": None,
                "citation": [],
                "precedential": True,
                "source": "courtlistener_csv",
                "source_id": case_id,
                "cluster_id": cluster_id,
                "docket_id": None,
                "gcs_path": gcs_path,  # ← 1:1 cross-tracking with GCS
                "opinion_count": 1,
                "citation_count": 0,
                "document_type": doc_type,
                "year_filed": year_filed,
                "source_window": self._determine_source_window(year_filed),
                "court_slug": "texas-verified",
                "judge_name": opinion_row.get("author_str", "").strip() if opinion_row.get("author_str") else None,
                "content_hash": content_hash,
                "practice_areas": classification.get("practice_areas", []),
                "primary_practice_area": classification.get("primary_practice_area"),
                "practice_area_confidence": classification.get("confidence", 0.0),
                "practice_area_checked_at": datetime.now().isoformat(),
                "word_count": len(plain_text.split()) if plain_text else 0
            }
            
            return case_record
            
        except Exception as e:
            logger.error(f"Error extracting case data: {e}")
            return None

    def _determine_source_window(self, year_filed: Optional[int]) -> str:
        """Determine source window based on year filed per schema constraints."""
        if not year_filed:
            return "unknown"

        # CourtListener data: modern (1994+) vs historical (pre-1994)
        if year_filed >= 1994:
            return "modern"
        else:
            return "historical"

    def _insert_batch(self, batch_records: list) -> bool:
        """Insert batch of records into Supabase."""
        try:
            if not batch_records:
                return True
            
            logger.info(f"Upserting batch of {len(batch_records)} records (on_conflict=id)...")

            response = self.supabase.table('cases').upsert(batch_records, on_conflict="id").execute()

            # Update existing hashes
            for record in batch_records:
                self.existing_hashes.add(record['content_hash'])

            logger.info(f"Successfully upserted {len(batch_records)} records")
            return True
            
        except Exception as e:
            logger.error(f"Error inserting batch: {e}")
            return False

    def process_csv_file(self, opinions_file: str, limit: Optional[int] = None) -> Dict:
        """Process the full CSV file with GCS storage."""
        logger.info(f"Starting enhanced CSV processing with GCS storage: {opinions_file}")

        stats = {
            'rows_read': 0,
            'texas_matches': 0,
            'pi_mm_cases': 0,
            'other_cases': 0,
            'duplicates': 0,
            'gcs_uploads': 0,
            'gcs_failures': 0,
            'errors': 0,
            'batches_processed': 0
        }

        start_time = time.time()
        batch_records = []

        try:
            print(f"📂 Opening CSV file: {opinions_file}")
            with bz2.open(opinions_file, 'rt', encoding='utf-8', newline='') as f:
                print("📂 CSV file opened successfully")

                # Seek to resume position if resuming
                if self.resume_offset > 0:
                    print(f"🔄 Seeking to resume position: {self.resume_offset:,} bytes")
                    logger.info(f"Seeking to resume position: {self.resume_offset:,} bytes")
                    f.seek(self.resume_offset)
                    # Skip to next complete line after seek position
                    f.readline()  # Skip potentially incomplete line
                    print("🔄 Resume position set")

                print("📊 Creating CSV reader...")
                # Use PostgreSQL-compatible CSV parameters (removed strict=True for error tolerance)
                reader = csv.DictReader(f, delimiter=',', quotechar='"',
                                      escapechar='\\', doublequote=False)
                print("📊 CSV reader created, starting row processing...")

                for row in reader:
                    try:
                        stats['rows_read'] += 1

                        if limit and stats['rows_read'] > limit:
                            break
                        # Check if this is a Texas case using pickle-based filtering
                        cluster_id = row.get('cluster_id', '')
                        if not (cluster_id and cluster_id.isdigit() and int(cluster_id) in self.texas_cluster_ids):
                            continue

                        stats['texas_matches'] += 1

                        # Extract case data (includes GCS upload)
                        case_record = self._extract_case_data(row)
                        if not case_record:
                            stats['duplicates'] += 1
                            continue

                        # Track GCS upload success
                        if case_record.get('gcs_path'):
                            stats['gcs_uploads'] += 1
                        else:
                            stats['gcs_failures'] += 1

                        # Track practice areas
                        primary_area = case_record.get('primary_practice_area', 'Other')
                        if primary_area in ['Personal Injury', 'Medical Malpractice']:
                            stats['pi_mm_cases'] += 1
                        else:
                            stats['other_cases'] += 1

                        batch_records.append(case_record)

                        # Process batch when full
                        if len(batch_records) >= self.batch_size:
                            if self._insert_batch(batch_records):
                                stats['batches_processed'] += 1
                                self.processed_count += len(batch_records)
                            else:
                                stats['errors'] += len(batch_records)

                            batch_records = []

                            # Save progress every batch (skip file position due to CSV buffering)
                            # current_offset = f.tell()
                            # self._save_resume_state(current_offset)

                    except Exception as e:
                        stats['errors'] += 1
                        if stats['errors'] <= 10:
                            logger.error(f"Row {stats['rows_read']} error: {e}")

                    # Progress update every 10K rows
                    if stats['rows_read'] % 10000 == 0:
                        elapsed = time.time() - start_time
                        gcs_rate = (stats['gcs_uploads'] / max(stats['texas_matches'], 1)) * 100
                        logger.info(f"[{elapsed:.1f}s] scanned={stats['rows_read']:,} tx_hits={stats['texas_matches']:,} pi_mm={stats['pi_mm_cases']:,} gcs={stats['gcs_uploads']:,} ({gcs_rate:.1f}%) dupes={stats['duplicates']:,} errors={stats['errors']:,}")

                # Process final batch
                if batch_records:
                    if self._insert_batch(batch_records):
                        stats['batches_processed'] += 1
                        self.processed_count += len(batch_records)
                    else:
                        stats['errors'] += len(batch_records)

                # Final progress save (skip file position due to CSV buffering)
                # current_offset = f.tell()
                # self._save_resume_state(current_offset)

        except Exception as e:
            logger.error(f"Fatal error processing CSV: {e}")
            raise

        elapsed = time.time() - start_time
        stats['elapsed'] = elapsed

        # Keep state file for future multi-state processing
        # State file preserved for resume capability and audit trail
        logger.info(f"Enhanced CSV processing complete in {elapsed:.1f}s")
        logger.info(f"State file preserved: {self.state_file}")
        return stats

def main():
    """Main function."""
    import argparse

    parser = argparse.ArgumentParser(description="Enhanced Texas Bulk Loader with GCS")
    parser.add_argument("--csv-file", required=True, help="Path to opinions CSV file")
    parser.add_argument("--batch-size", type=int, default=5000, help="Batch size")
    parser.add_argument("--gcs-bucket", default="texas-laws-personalinjury", help="GCS bucket name")
    parser.add_argument("--limit", type=int, help="Limit rows for testing")
    parser.add_argument("--skip-classification", action="store_true", help="Skip practice area classification for faster ingestion")

    args = parser.parse_args()

    # Load environment
    from dotenv import load_dotenv
    load_dotenv()

    # Initialize enhanced loader
    loader = EnhancedBulkLoader(batch_size=args.batch_size, gcs_bucket=args.gcs_bucket, skip_classification=args.skip_classification, csv_file=args.csv_file)

    # Process CSV with GCS storage
    stats = loader.process_csv_file(args.csv_file, limit=args.limit)

    # Final report
    print(f"\n🎉 ENHANCED BULK IMPORT COMPLETE!")
    print(f"   Total rows scanned: {stats['rows_read']:,}")
    print(f"   Texas matches: {stats['texas_matches']:,}")
    print(f"   PI/MM cases: {stats['pi_mm_cases']:,}")
    print(f"   Other cases: {stats['other_cases']:,}")
    print(f"   GCS uploads: {stats['gcs_uploads']:,}")
    print(f"   GCS failures: {stats['gcs_failures']:,}")
    print(f"   Duplicates: {stats['duplicates']:,}")
    print(f"   Errors: {stats['errors']:,}")
    print(f"   Batches processed: {stats['batches_processed']:,}")
    print(f"   Processing time: {stats['elapsed']:.1f}s")
    print(f"   1:1 Cross-tracking: Supabase ↔ GCS")

if __name__ == "__main__":
    main()
