
# Fixed VertexAI LLM Configuration for GraphRAG Pipeline
# Generated: 2025-08-19T10:41:59.403127

WORKING_CONFIGURATION = {
    "model_name": "gemini-2.0-flash-exp",
    "model_params": {
        "temperature": 0.0,
        "max_output_tokens": 4096
    },
    "generation_config": {
        "temperature": 0.0,
        "response_mime_type": "application/json",
        "response_schema": {
        "type": "object",
        "properties": {
                "nodes": {
                        "type": "array",
                        "items": {
                                "type": "object",
                                "properties": {
                                        "id": {
                                                "type": "string"
                                        },
                                        "label": {
                                                "type": "string"
                                        },
                                        "properties": {
                                                "type": "object"
                                        }
                                },
                                "required": [
                                        "id",
                                        "label",
                                        "properties"
                                ]
                        }
                },
                "relationships": {
                        "type": "array",
                        "items": {
                                "type": "object",
                                "properties": {
                                        "type": {
                                                "type": "string"
                                        },
                                        "start_node_id": {
                                                "type": "string"
                                        },
                                        "end_node_id": {
                                                "type": "string"
                                        },
                                        "properties": {
                                                "type": "object"
                                        }
                                },
                                "required": [
                                        "type",
                                        "start_node_id",
                                        "end_node_id"
                                ]
                        }
                }
        },
        "required": [
                "nodes",
                "relationships"
        ]
}
    }
}

WORKING_PROMPT_TEMPLATE = '''
You are a legal knowledge graph extraction expert. Extract entities and relationships from the legal text.

Extract these entity types with unique IDs:
- Case: Legal cases and proceedings  
- Judge: Presiding judges
- Court: Courts and jurisdictions
- Attorney: Legal representatives
- Plaintiff: Plaintiffs and petitioners
- Defendant: Defendants and respondents
- Damages: Monetary awards and damages

Extract these relationship types:
- PRESIDED_OVER: Judge presided over case
- REPRESENTED: Attorney represented party
- FILED_IN: Case filed in court
- AWARDED: Damages awarded
- OPPOSED: Parties in opposition

Return only valid JSON in this exact format:
{"nodes": [{"id": "unique_id", "label": "EntityType", "properties": {"name": "Entity Name"}}], "relationships": [{"type": "RELATIONSHIP_TYPE", "start_node_id": "id1", "end_node_id": "id2", "properties": {}}]}

Text to analyze:
{text}
'''
