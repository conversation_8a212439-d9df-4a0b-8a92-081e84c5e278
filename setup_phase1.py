#!/usr/bin/env python3
"""
Phase 1 Setup Script: End-to-End Validation Pipeline
Installs dependencies and configures all components for legal data processing validation
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase1Setup:
    """Setup manager for Phase 1 implementation"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.requirements_installed = False
        self.env_configured = False
        self.neo4j_ready = False
        
    def run_complete_setup(self) -> bool:
        """Run complete Phase 1 setup process"""
        
        logger.info("=== Starting Phase 1 Setup: End-to-End Validation Pipeline ===")
        
        try:
            # Step 1: Install Python dependencies
            self._install_dependencies()
            
            # Step 2: Verify environment variables
            self._verify_environment()
            
            # Step 3: Test Neo4j connection
            self._test_neo4j_connection()
            
            # Step 4: Test all API connections
            self._test_api_connections()
            
            # Step 5: Run initial validation test
            self._run_initial_validation()
            
            logger.info("=== Phase 1 Setup Completed Successfully ===")
            self._print_next_steps()
            
            return True
            
        except Exception as e:
            logger.error(f"Setup failed: {e}")
            self._print_troubleshooting()
            return False
    
    def _install_dependencies(self):
        """Install required Python packages"""
        
        logger.info("Installing Python dependencies...")
        
        # Core dependencies for Phase 1
        requirements = [
            "neo4j>=5.0.0",
            "neo4j-graphrag-python[dev]",
            "langextract>=0.1.0",
            "voyageai>=0.2.0",
            "google-generativeai>=0.8.0",
            "pinecone-client>=3.0.0",
            "supabase>=2.0.0",
            "python-dotenv>=1.0.0",
            "langchain-text-splitters>=0.2.0",
            "tenacity>=8.0.0",
            "asyncio-throttle>=1.0.0"
        ]
        
        for requirement in requirements:
            try:
                logger.info(f"Installing {requirement}...")
                subprocess.run([
                    sys.executable, "-m", "pip", "install", requirement
                ], check=True, capture_output=True, text=True)
                
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to install {requirement}: {e}")
                logger.error(f"Error output: {e.stderr}")
                raise
        
        logger.info("All dependencies installed successfully")
        self.requirements_installed = True
    
    def _verify_environment(self):
        """Verify all required environment variables are set"""
        
        logger.info("Verifying environment variables...")
        
        required_vars = {
            "NEO4J_URI": "Neo4j AuraDB connection URI",
            "NEO4J_USERNAME": "Neo4j username (usually 'neo4j')",
            "NEO4J_PASSWORD": "Neo4j password",
            "GEMINI_API_KEY": "Google Gemini API key for LLM processing",
            "VOYAGE_API_KEY": "Voyage AI API key for embeddings",
            "PINECONE_API_KEY": "Pinecone API key for vector storage",
            "PINECONE_INDEX_NAME": "Pinecone index name",
            "SUPABASE_URL": "Supabase project URL",
            "SUPABASE_SERVICE_ROLE_KEY": "Supabase service role key"
        }
        
        missing_vars = []
        
        for var, description in required_vars.items():
            value = os.getenv(var)
            if not value:
                missing_vars.append(f"{var}: {description}")
                logger.error(f"Missing environment variable: {var}")
            else:
                logger.info(f"✓ {var} is set")
        
        if missing_vars:
            logger.error("Missing required environment variables:")
            for var in missing_vars:
                logger.error(f"  - {var}")
            
            logger.info("Please set these variables in your .env file or environment")
            raise ValueError("Missing required environment variables")
        
        logger.info("All environment variables verified")
        self.env_configured = True
    
    def _test_neo4j_connection(self):
        """Test Neo4j database connection"""
        
        logger.info("Testing Neo4j connection...")
        
        try:
            from neo4j import GraphDatabase
            
            uri = os.getenv("NEO4J_URI")
            username = os.getenv("NEO4J_USERNAME", "neo4j")
            password = os.getenv("NEO4J_PASSWORD")
            
            driver = GraphDatabase.driver(uri, auth=(username, password))
            
            # Test connection
            with driver.session() as session:
                result = session.run("RETURN 'Hello Neo4j' as message")
                message = result.single()["message"]
                logger.info(f"Neo4j connection successful: {message}")
            
            driver.close()
            self.neo4j_ready = True
            
        except Exception as e:
            logger.error(f"Neo4j connection failed: {e}")
            logger.error("Please check your Neo4j credentials and network connectivity")
            raise
    
    def _test_api_connections(self):
        """Test all external API connections"""
        
        logger.info("Testing API connections...")
        
        # Test Gemini API
        try:
            import google.generativeai as genai
            genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
            
            # Try a simple generation
            model = genai.GenerativeModel('gemini-2.0-flash-exp')
            response = model.generate_content("Hello")
            logger.info("✓ Gemini API connection successful")
            
        except Exception as e:
            logger.error(f"✗ Gemini API connection failed: {e}")
            raise
        
        # Test Voyage API
        try:
            import voyageai
            client = voyageai.Client(api_key=os.getenv("VOYAGE_API_KEY"))
            
            # Test embedding
            result = client.embed(["test"], model="voyage-3-large")
            if result.embeddings:
                logger.info("✓ Voyage AI API connection successful")
            
        except Exception as e:
            logger.error(f"✗ Voyage AI API connection failed: {e}")
            raise
        
        # Test Pinecone
        try:
            from pinecone import Pinecone
            pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
            
            # List indexes to test connection
            indexes = pc.list_indexes()
            logger.info("✓ Pinecone API connection successful")
            
        except Exception as e:
            logger.error(f"✗ Pinecone API connection failed: {e}")
            raise
        
        # Test Supabase
        try:
            from supabase import create_client
            supabase = create_client(
                os.getenv("SUPABASE_URL"),
                os.getenv("SUPABASE_SERVICE_ROLE_KEY")
            )
            
            # Test simple query
            result = supabase.table("cases").select("id").limit(1).execute()
            logger.info("✓ Supabase API connection successful")
            
        except Exception as e:
            logger.error(f"✗ Supabase API connection failed: {e}")
            raise
        
        logger.info("All API connections verified successfully")
    
    def _run_initial_validation(self):
        """Run initial validation to ensure everything works"""
        
        logger.info("Running initial validation test...")
        
        try:
            # Import and run our validation pipeline
            import asyncio
            from validation_pipeline import run_validation_test
            
            # Run the validation test
            result = asyncio.run(run_validation_test())
            
            if result:
                logger.info("✓ Initial validation test completed successfully")
                return True
            else:
                logger.error("✗ Initial validation test failed")
                return False
                
        except Exception as e:
            logger.error(f"Initial validation failed: {e}")
            raise
    
    def _print_next_steps(self):
        """Print next steps after successful setup"""
        
        print("\n" + "="*60)
        print("🎉 PHASE 1 SETUP COMPLETED SUCCESSFULLY!")
        print("="*60)
        
        print("\n📋 NEXT STEPS:")
        print("1. Run end-to-end validation:")
        print("   python validation_pipeline.py")
        
        print("\n2. Test individual components:")
        print("   python setup_legal_graphrag.py")
        print("   python setup_langextract_legal.py")
        
        print("\n3. Process sample documents:")
        print("   python -c \"from validation_pipeline import run_validation_test; import asyncio; asyncio.run(run_validation_test())\"")
        
        print("\n4. Check quality metrics:")
        print("   - Citation accuracy: Target 99.5%+")
        print("   - Entity extraction confidence: Target 95%+")
        print("   - Cross-system consistency: Target 100%")
        
        print("\n📊 MONITORING:")
        print("- Check logs for processing quality")
        print("- Monitor embedding costs")
        print("- Validate cross-system data integrity")
        
        print("\n🚀 READY FOR PRODUCTION TESTING:")
        print("Your pipeline is now ready for processing real Texas legal documents!")
        print("="*60)
    
    def _print_troubleshooting(self):
        """Print troubleshooting information"""
        
        print("\n" + "="*60)
        print("❌ SETUP FAILED - TROUBLESHOOTING GUIDE")
        print("="*60)
        
        print("\n🔧 COMMON ISSUES:")
        
        if not self.requirements_installed:
            print("\n📦 DEPENDENCY ISSUES:")
            print("- Ensure Python 3.9+ is installed")
            print("- Try: pip install --upgrade pip")
            print("- For M1/M2 Macs: pip install --no-cache-dir <package>")
        
        if not self.env_configured:
            print("\n🔑 ENVIRONMENT VARIABLES:")
            print("- Create a .env file in the project root")
            print("- Copy required variables from .env.example")
            print("- Ensure no extra spaces around = signs")
        
        if not self.neo4j_ready:
            print("\n🗄️  NEO4J ISSUES:")
            print("- Check Neo4j AuraDB is running")
            print("- Verify URI format: neo4j+s://xxx.databases.neo4j.io")
            print("- Test connection in Neo4j Browser first")
        
        print("\n🆘 GET HELP:")
        print("- Check logs above for specific error messages")
        print("- Verify all API keys are valid and have appropriate permissions")
        print("- Ensure network connectivity to all services")
        
        print("="*60)

def main():
    """Main setup function"""
    
    setup = Phase1Setup()
    
    try:
        success = setup.run_complete_setup()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("Setup interrupted by user")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"Unexpected error during setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()