-- PostgreSQL function to efficiently mark Texas cases
-- Run this in your Supabase SQL editor before using the optimized loader

CREATE OR REPLACE FUNCTION mark_texas_cases(cluster_ids bigint[])
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    updated_count integer;
BEGIN
    -- Update all cases with matching cluster_ids to mark them as Texas
    UPDATE cases 
    SET 
        jurisdiction = 'TX',
        court_id = 'texas-verified',
        court_slug = 'texas-verified',
        updated_at = NOW()
    WHERE cluster_id = ANY(cluster_ids)
    AND (jurisdiction IS NULL OR jurisdiction != 'TX');
    
    -- Get the count of updated rows
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    -- Log the operation
    INSERT INTO processing_log (
        operation_type,
        operation_details,
        records_affected,
        created_at
    ) VALUES (
        'mark_texas_cases',
        format('Marked %s cases as Texas jurisdiction', updated_count),
        updated_count,
        NOW()
    );
    
    RETURN updated_count;
END;
$$;

-- Create processing log table if it doesn't exist
CREATE TABLE IF NOT EXISTS processing_log (
    id SERIAL PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL,
    operation_details TEXT,
    records_affected INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on cluster_id for fast lookups if it doesn't exist
CREATE INDEX IF NOT EXISTS idx_cases_cluster_id ON cases(cluster_id);

-- Create index on jurisdiction for filtering
CREATE INDEX IF NOT EXISTS idx_cases_jurisdiction ON cases(jurisdiction);

-- Create index on primary_practice_area for classification queries
CREATE INDEX IF NOT EXISTS idx_cases_practice_area ON cases(primary_practice_area);

COMMENT ON FUNCTION mark_texas_cases(bigint[]) IS 'Efficiently mark cases as Texas jurisdiction based on cluster_ids';