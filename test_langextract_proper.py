#!/usr/bin/env python3
"""
Test LangExtract Proper API
"""

import asyncio
import os
from dotenv import load_dotenv
load_dotenv()

def test_langextract_extract():
    """Test LangExtract main extract function"""
    try:
        import langextract
        
        # Sample legal text
        legal_text = """
        This is a personal injury case where <PERSON><PERSON><PERSON> sued Defendant <PERSON> 
        for damages resulting from a motor vehicle accident. Judge <PERSON> presided over 
        the case. The jury awarded $50,000 in actual damages and $25,000 in punitive damages. 
        The case was decided on March 15, 2023, in the 55th Judicial District Court of Harris County, Texas.
        Attorney <PERSON> represented the plaintiff, while Attorney <PERSON> represented the defendant.
        The court cited precedent from <PERSON><PERSON> v. Roe, 123 S.W.3d 456 (Tex. 2020).
        """
        
        print("=== Testing LangExtract Main Extract Function ===")
        
        # Check the extract function signature
        import inspect
        sig = inspect.signature(langextract.extract)
        print(f"Extract function signature: {sig}")
        
        # Try basic extraction
        result = langextract.extract(legal_text)
        print(f"✅ Basic extraction successful")
        print(f"   Result type: {type(result)}")
        print(f"   Result: {result}")
        
        return result
        
    except Exception as e:
        print(f"❌ LangExtract extract test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_langextract_schema():
    """Test LangExtract schema configuration"""
    try:
        import langextract
        
        print("\n=== Testing LangExtract Schema ===")
        
        # Explore schema module
        if hasattr(langextract, 'schema'):
            schema_module = langextract.schema
            print("Schema module contents:")
            for name in dir(schema_module):
                if not name.startswith('_'):
                    print(f"  - {name}")
        
        return True
        
    except Exception as e:
        print(f"❌ LangExtract schema test failed: {e}")
        return False

def test_langextract_legal_schema():
    """Test creating a legal schema for LangExtract"""
    try:
        import langextract
        from langextract import schema
        
        print("\n=== Testing Legal Schema Creation ===")
        
        # Try to create a legal entity schema
        legal_entities = [
            "PERSON",
            "ORGANIZATION", 
            "LOCATION",
            "DATE",
            "MONEY",
            "JUDGE",
            "ATTORNEY",
            "PLAINTIFF",
            "DEFENDANT",
            "COURT",
            "CASE",
            "CITATION"
        ]
        
        # Try different schema creation methods
        schema_methods = ['EntitySchema', 'Schema', 'create_schema', 'entity_schema']
        
        for method_name in schema_methods:
            if hasattr(schema, method_name):
                try:
                    method = getattr(schema, method_name)
                    print(f"✅ Found schema method: {method_name}")
                    
                    # Try to create schema
                    if callable(method):
                        legal_schema = method(entities=legal_entities)
                        print(f"   Created legal schema: {type(legal_schema)}")
                    
                except Exception as e:
                    print(f"⚠️  {method_name} failed: {e}")
            else:
                print(f"❌ {method_name}: Not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Legal schema test failed: {e}")
        return False

def test_langextract_with_config():
    """Test LangExtract with configuration"""
    try:
        import langextract
        
        print("\n=== Testing LangExtract with Configuration ===")
        
        legal_text = """
        Judge William Brown of Harris County District Court ruled in favor of plaintiff 
        John Smith against defendant Mary Jones on March 15, 2023. The court awarded 
        $50,000 in damages. Attorney Sarah Wilson represented Smith.
        """
        
        # Try extraction with different configurations
        configs = [
            {},  # Default config
            {"entities": ["PERSON", "ORG", "MONEY", "DATE"]},  # Custom entities
            {"max_length": 1000},  # Length limit
            {"language": "en"},  # Language setting
        ]
        
        for i, config in enumerate(configs):
            try:
                print(f"\nConfig {i+1}: {config}")
                result = langextract.extract(legal_text, **config)
                print(f"✅ Success with config {i+1}")
                print(f"   Result: {result}")
            except Exception as e:
                print(f"⚠️  Config {i+1} failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_langextract_providers():
    """Test LangExtract providers"""
    try:
        import langextract
        
        print("\n=== Testing LangExtract Providers ===")
        
        if hasattr(langextract, 'providers'):
            providers_module = langextract.providers
            print("Available providers:")
            for name in dir(providers_module):
                if not name.startswith('_'):
                    print(f"  - {name}")
                    
            # Check for specific providers
            provider_types = ['openai', 'gemini', 'anthropic', 'huggingface']
            for provider in provider_types:
                if hasattr(providers_module, provider):
                    print(f"✅ {provider} provider available")
                else:
                    print(f"❌ {provider} provider not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Providers test failed: {e}")
        return False

def main():
    """Run all LangExtract proper API tests"""
    print("=== LangExtract Proper API Test ===\n")
    
    # Test main extract function
    test_langextract_extract()
    
    # Test schema configuration
    test_langextract_schema()
    
    # Test legal schema creation
    test_langextract_legal_schema()
    
    # Test with configuration
    test_langextract_with_config()
    
    # Test providers
    test_langextract_providers()
    
    print("\n=== LangExtract Proper API Test Complete ===")

if __name__ == "__main__":
    main()