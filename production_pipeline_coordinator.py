#!/usr/bin/env python3
"""
Production Pipeline Coordinator
Orchestrates REAL Texas legal document processing using all existing components
"""

import os
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import json

# Import all our completed components
from enhanced_gcs_client import EnhancedGCSClient, LegalDocument, GCSProcessingReport
from voyage_contextual_embedder import VoyageContextualEmbedder, ContextAwareLegalChunker, ContextualChunk, EmbeddedChunk
from setup_legal_graphrag import LegalGraphRAGPipeline
from global_uid_system import GlobalUIDManager, GlobalUIDRecord, StorageSystem

from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

load_dotenv()

@dataclass
class ProcessingResult:
    """Result of processing a single document through the pipeline"""
    global_uid: str
    document_id: str
    gcs_path: str
    case_name: str
    status: str  # 'success', 'error', 'partial'
    
    # Processing metrics
    chunks_created: int
    chunks_embedded: int
    entities_extracted: int
    relationships_extracted: int
    
    # Storage tracking
    supabase_stored: bool = False
    pinecone_stored: bool = False
    neo4j_stored: bool = False
    
    # Timing and costs
    processing_time_seconds: float = 0.0
    api_costs: Dict[str, float] = None
    
    # Error tracking
    error_message: Optional[str] = None
    errors: List[str] = None
    
    def __post_init__(self):
        if self.api_costs is None:
            self.api_costs = {}
        if self.errors is None:
            self.errors = []

@dataclass 
class BatchProcessingReport:
    """Report for batch processing of multiple documents"""
    total_documents: int
    successful_documents: int
    failed_documents: int
    partial_documents: int
    
    total_chunks: int
    total_entities: int
    total_relationships: int
    
    total_processing_time: float
    total_api_costs: Dict[str, float]
    
    # Quality metrics
    average_entities_per_doc: float
    average_chunks_per_doc: float
    
    # Individual results
    results: List[ProcessingResult]
    
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
    
    @property
    def success_rate(self) -> float:
        if self.total_documents == 0:
            return 100.0
        return (self.successful_documents / self.total_documents) * 100.0

class ProductionPipelineCoordinator:
    """
    Production-ready coordinator that processes REAL Texas legal documents
    Orchestrates all existing components for end-to-end processing
    """
    
    def __init__(self):
        logger.info("🚀 Initializing Production Pipeline Coordinator...")
        
        # Initialize all existing components
        self.gcs_client = EnhancedGCSClient()
        self.legal_chunker = ContextAwareLegalChunker(chunk_size=2000, overlap=200)
        self.embedder = VoyageContextualEmbedder(model="voyage-context-3", output_dimension=1024)
        self.graphrag_pipeline = LegalGraphRAGPipeline()
        self.uid_manager = GlobalUIDManager()
        
        logger.info("✅ All pipeline components initialized")
    
    async def process_real_texas_documents(self, 
                                         document_limit: int = 10,
                                         practice_area: str = "personal_injury",
                                         max_concurrent: int = 3) -> BatchProcessingReport:
        """
        Process REAL Texas legal documents from GCS through the complete pipeline
        """
        logger.info(f"🔄 Starting REAL data processing: {document_limit} documents, {practice_area}")
        
        start_time = datetime.utcnow()
        
        try:
            # Step 1: Discover real Texas documents in GCS
            logger.info("📋 Discovering real Texas legal documents in GCS...")
            texas_document_paths = await self.gcs_client.find_texas_cases(
                max_results=document_limit * 2,  # Get extra to filter
                year_filter=None
            )
            
            if not texas_document_paths:
                logger.error("❌ No Texas documents found in GCS")
                return self._create_empty_report()
            
            # Limit to requested number
            selected_paths = texas_document_paths[:document_limit]
            logger.info(f"📄 Selected {len(selected_paths)} real Texas documents for processing")
            
            # Step 2: Process documents concurrently  
            logger.info(f"⚙️  Processing {len(selected_paths)} documents with {max_concurrent} workers...")
            
            # Use semaphore to control concurrency
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_with_semaphore(gcs_path: str) -> ProcessingResult:
                async with semaphore:
                    return await self._process_single_document(gcs_path, practice_area)
            
            # Process all documents concurrently
            tasks = [process_with_semaphore(path) for path in selected_paths]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions in results
            processing_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"❌ Document {selected_paths[i]} failed: {result}")
                    # Create error result
                    error_result = ProcessingResult(
                        global_uid="unknown",
                        document_id="unknown", 
                        gcs_path=selected_paths[i],
                        case_name="Unknown",
                        status="error",
                        chunks_created=0,
                        chunks_embedded=0,
                        entities_extracted=0,
                        relationships_extracted=0,
                        error_message=str(result)
                    )
                    processing_results.append(error_result)
                else:
                    processing_results.append(result)
            
            # Step 3: Generate batch report
            total_time = (datetime.utcnow() - start_time).total_seconds()
            report = self._generate_batch_report(processing_results, total_time)
            
            # Step 4: Log summary
            self._log_processing_summary(report)
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Batch processing failed: {e}")
            import traceback
            traceback.print_exc()
            return self._create_empty_report()
    
    async def _process_single_document(self, 
                                     gcs_path: str,
                                     practice_area: str) -> ProcessingResult:
        """
        Process a single document through the complete pipeline
        """
        doc_start_time = datetime.utcnow()
        result = None
        
        try:
            logger.info(f"📖 Processing document: {gcs_path}")
            
            # Step 1: Load document from GCS
            legal_document = await self.gcs_client.read_document(gcs_path)
            if not legal_document:
                return ProcessingResult(
                    global_uid="failed_load",
                    document_id="unknown",
                    gcs_path=gcs_path,
                    case_name="Failed to Load",
                    status="error",
                    chunks_created=0,
                    chunks_embedded=0,
                    entities_extracted=0,
                    relationships_extracted=0,
                    error_message="Failed to load document from GCS"
                )
            
            if not legal_document.is_processable:
                return ProcessingResult(
                    global_uid="not_processable",
                    document_id=legal_document.id,
                    gcs_path=gcs_path,
                    case_name=legal_document.case_name,
                    status="error",
                    chunks_created=0,
                    chunks_embedded=0,
                    entities_extracted=0,
                    relationships_extracted=0,
                    error_message=f"Document not processable: {legal_document.word_count} words"
                )
            
            # Step 2: Generate global UID for tracking
            global_uid = self.uid_manager.generate_global_uid(
                document_id=legal_document.id,
                source_system="gcs"
            )
            
            # Step 3: Create contextual chunks
            logger.info(f"🔪 Creating contextual chunks for {legal_document.id}")
            
            # Convert LegalDocument to format expected by chunker
            document_dict = {
                "id": legal_document.id,
                "case_name": legal_document.case_name,
                "court_name": legal_document.metadata.get("court_type", "Unknown Court"),
                "jurisdiction": legal_document.metadata.get("jurisdiction", "TX"),
                "practice_area": practice_area,
                "content": legal_document.content,
                "date_filed": legal_document.metadata.get("created_time"),
                "word_count": legal_document.word_count
            }
            
            contextual_chunks = await self.legal_chunker.create_contextual_chunks(document_dict)
            
            if not contextual_chunks:
                return ProcessingResult(
                    global_uid=global_uid,
                    document_id=legal_document.id,
                    gcs_path=gcs_path,
                    case_name=legal_document.case_name,
                    status="error",
                    chunks_created=0,
                    chunks_embedded=0,
                    entities_extracted=0,
                    relationships_extracted=0,
                    error_message="No valid chunks created"
                )
            
            # Step 4: Generate contextual embeddings
            logger.info(f"🔄 Generating contextual embeddings for {len(contextual_chunks)} chunks")
            embedded_chunks = await self.embedder.embed_document_with_full_context(contextual_chunks)
            
            if not embedded_chunks:
                return ProcessingResult(
                    global_uid=global_uid,
                    document_id=legal_document.id,
                    gcs_path=gcs_path,
                    case_name=legal_document.case_name,
                    status="error",
                    chunks_created=len(contextual_chunks),
                    chunks_embedded=0,
                    entities_extracted=0,
                    relationships_extracted=0,
                    error_message="Failed to generate embeddings"
                )
            
            # Step 5: Extract entities using GraphRAG
            logger.info(f"🧠 Extracting entities with GraphRAG for {legal_document.id}")
            
            # Use the contextual chunking method from GraphRAG
            graphrag_result = await self.graphrag_pipeline.process_document_with_contextual_chunking(
                document_dict, practice_area
            )
            
            entities_extracted = graphrag_result.get('entities_extracted', 0)
            relationships_extracted = graphrag_result.get('relationships_extracted', 0)
            
            # Step 6: Register global UID and cross-system tracking
            logger.info(f"🔗 Registering global UID: {global_uid}")
            
            uid_record = GlobalUIDRecord(
                global_uid=global_uid,
                document_id=legal_document.id,
                chunk_id=None,  # Document-level record
                entity_id=None,
                source_system="gcs",
                source_path=gcs_path,
                gcs_path=gcs_path,
                document_hash=legal_document.metadata.get("document_hash")
            )
            
            uid_registered = await self.uid_manager.register_global_uid(uid_record)
            
            # Step 7: Calculate processing time and create result
            processing_time = (datetime.utcnow() - doc_start_time).total_seconds()
            
            result = ProcessingResult(
                global_uid=global_uid,
                document_id=legal_document.id,
                gcs_path=gcs_path,
                case_name=legal_document.case_name,
                status="success",
                chunks_created=len(contextual_chunks),
                chunks_embedded=len(embedded_chunks),
                entities_extracted=entities_extracted,
                relationships_extracted=relationships_extracted,
                supabase_stored=uid_registered,
                neo4j_stored=entities_extracted > 0,
                processing_time_seconds=processing_time,
                api_costs={
                    "voyage_embeddings": len(embedded_chunks) * 0.0001,  # Estimate
                    "vertex_ai": entities_extracted * 0.001  # Estimate
                }
            )
            
            logger.info(f"✅ Successfully processed {legal_document.case_name}: "
                       f"{len(embedded_chunks)} chunks, {entities_extracted} entities")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Error processing {gcs_path}: {e}")
            
            processing_time = (datetime.utcnow() - doc_start_time).total_seconds()
            
            return ProcessingResult(
                global_uid=result.global_uid if result else "error",
                document_id=result.document_id if result else "unknown",
                gcs_path=gcs_path,
                case_name=result.case_name if result else "Error",
                status="error",
                chunks_created=result.chunks_created if result else 0,
                chunks_embedded=result.chunks_embedded if result else 0,
                entities_extracted=result.entities_extracted if result else 0,
                relationships_extracted=result.relationships_extracted if result else 0,
                processing_time_seconds=processing_time,
                error_message=str(e),
                errors=[str(e)]
            )
    
    def _generate_batch_report(self, 
                              results: List[ProcessingResult], 
                              total_time: float) -> BatchProcessingReport:
        """Generate comprehensive batch processing report"""
        
        successful_results = [r for r in results if r.status == "success"]
        failed_results = [r for r in results if r.status == "error"]
        partial_results = [r for r in results if r.status == "partial"]
        
        total_chunks = sum(r.chunks_embedded for r in results)
        total_entities = sum(r.entities_extracted for r in results)
        total_relationships = sum(r.relationships_extracted for r in results)
        
        # Calculate API costs
        total_costs = {}
        for result in results:
            for api, cost in result.api_costs.items():
                total_costs[api] = total_costs.get(api, 0) + cost
        
        # Calculate averages
        avg_entities = total_entities / len(results) if results else 0
        avg_chunks = total_chunks / len(results) if results else 0
        
        return BatchProcessingReport(
            total_documents=len(results),
            successful_documents=len(successful_results),
            failed_documents=len(failed_results),
            partial_documents=len(partial_results),
            total_chunks=total_chunks,
            total_entities=total_entities,
            total_relationships=total_relationships,
            total_processing_time=total_time,
            total_api_costs=total_costs,
            average_entities_per_doc=avg_entities,
            average_chunks_per_doc=avg_chunks,
            results=results
        )
    
    def _create_empty_report(self) -> BatchProcessingReport:
        """Create empty report for error cases"""
        return BatchProcessingReport(
            total_documents=0,
            successful_documents=0,
            failed_documents=0,
            partial_documents=0,
            total_chunks=0,
            total_entities=0,
            total_relationships=0,
            total_processing_time=0.0,
            total_api_costs={},
            average_entities_per_doc=0.0,
            average_chunks_per_doc=0.0,
            results=[]
        )
    
    def _log_processing_summary(self, report: BatchProcessingReport):
        """Log comprehensive processing summary"""
        
        logger.info("=" * 60)
        logger.info("📊 PRODUCTION PIPELINE PROCESSING SUMMARY")
        logger.info("=" * 60)
        logger.info(f"📄 Total Documents: {report.total_documents}")
        logger.info(f"✅ Successful: {report.successful_documents}")
        logger.info(f"❌ Failed: {report.failed_documents}")
        logger.info(f"⚠️  Partial: {report.partial_documents}")
        logger.info(f"📈 Success Rate: {report.success_rate:.1f}%")
        logger.info("")
        logger.info(f"🔪 Total Chunks: {report.total_chunks}")
        logger.info(f"🧠 Total Entities: {report.total_entities}")
        logger.info(f"🔗 Total Relationships: {report.total_relationships}")
        logger.info("")
        logger.info(f"⏱️  Processing Time: {report.total_processing_time:.1f}s")
        logger.info(f"📊 Avg Entities/Doc: {report.average_entities_per_doc:.1f}")
        logger.info(f"📊 Avg Chunks/Doc: {report.average_chunks_per_doc:.1f}")
        logger.info("")
        
        if report.total_api_costs:
            logger.info("💰 API Costs:")
            for api, cost in report.total_api_costs.items():
                logger.info(f"   {api}: ${cost:.4f}")
        
        logger.info("=" * 60)
        
        # Log sample successful documents
        successful_docs = [r for r in report.results if r.status == "success"]
        if successful_docs:
            logger.info("🎯 Sample Successful Documents:")
            for doc in successful_docs[:3]:
                logger.info(f"   - {doc.case_name}: {doc.entities_extracted} entities, "
                           f"{doc.chunks_embedded} chunks ({doc.processing_time_seconds:.1f}s)")
        
        # Log failed documents
        failed_docs = [r for r in report.results if r.status == "error"]
        if failed_docs:
            logger.info("❌ Failed Documents:")
            for doc in failed_docs[:3]:
                logger.info(f"   - {doc.gcs_path}: {doc.error_message}")
    
    async def save_processing_report(self, 
                                   report: BatchProcessingReport, 
                                   filename: Optional[str] = None) -> str:
        """Save processing report to JSON file"""
        
        if filename is None:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"production_pipeline_report_{timestamp}.json"
        
        # Convert report to JSON-serializable format
        report_dict = asdict(report)
        report_dict['timestamp'] = report.timestamp.isoformat()
        
        # Convert results to dict
        report_dict['results'] = [asdict(result) for result in report.results]
        
        with open(filename, 'w') as f:
            json.dump(report_dict, f, indent=2, default=str)
        
        logger.info(f"📄 Processing report saved: {filename}")
        return filename
    
    def close(self):
        """Clean up pipeline resources"""
        if hasattr(self.graphrag_pipeline, 'close'):
            self.graphrag_pipeline.close()
        logger.info("🔒 Production pipeline coordinator closed")

# Test function for production pipeline
async def test_production_pipeline():
    """Test the production pipeline with REAL Texas documents"""
    
    print("=== Testing Production Pipeline Coordinator ===\n")
    
    try:
        # Initialize coordinator
        coordinator = ProductionPipelineCoordinator()
        
        # Process 5 REAL Texas documents
        print("🚀 Processing 5 REAL Texas legal documents...")
        report = await coordinator.process_real_texas_documents(
            document_limit=5,
            practice_area="personal_injury",
            max_concurrent=2
        )
        
        # Save report
        report_file = await coordinator.save_processing_report(report)
        
        print(f"\n📄 Processing report saved: {report_file}")
        print(f"✅ Production pipeline test completed!")
        print(f"📊 Success rate: {report.success_rate:.1f}%")
        
        # Cleanup
        coordinator.close()
        
        return report
        
    except Exception as e:
        print(f"❌ Production pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    asyncio.run(test_production_pipeline())