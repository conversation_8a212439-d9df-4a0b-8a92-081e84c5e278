# 📚 Legal Opinion Systems - Master Developer Guide
## Texas Laws Personal Injury Project

> **🎯 START HERE**: This is your single entry point to understand and work with our dual legal opinion extraction systems. Follow the links to dive deeper into specific areas.

---

## 🏗️ **System Architecture Overview**

### **Why Two Systems?**
We operate **two complementary systems** to provide complete coverage of legal opinions:

```
📊 HISTORICAL FOUNDATION + 🌐 LIVE UPDATES = 🎯 COMPLETE LEGAL DATABASE
        ↓                        ↓                    ↓
   Bulk CSV System          API System         Unified Platform
   (680K+ TX cases)        (500+ recent)      (Complete coverage)
   Historical depth        Real-time updates   Best of both worlds
```

### **Current Status**
- ✅ **273,173 Texas cases** processed (bulk system)
- ✅ **500 API cases** with rich metadata (API system)  
- ✅ **Full text access** from both systems
- ✅ **AI classification** for practice areas
- 🔄 **5.6M rows remaining** in bulk processing

---

## 📋 **Quick Navigation**

### **🚀 New Developer Onboarding**
1. **[Start Here](#getting-started)** - Environment setup & authentication
2. **[Choose Your Path](#choose-your-system)** - Which system for your use case
3. **[Run Examples](#quick-examples)** - See it working immediately
4. **[Deep Dive](#detailed-guides)** - Comprehensive documentation

### **🔧 System-Specific Guides**
- **[Bulk CSV System](#bulk-csv-system-historical-cases)** - 680K+ historical cases
- **[API System](#api-system-recent-cases)** - Recent cases with rich metadata
- **[Infrastructure](#infrastructure--authentication)** - GCS, authentication, deployment

---

## 🚀 **Getting Started**

### **Prerequisites**
```bash
# 1. Clone repository
git clone https://github.com/Jpkay/texas-laws-personalinjury.git
cd texas-laws-personalinjury

# 2. Install dependencies
pip install -r requirements.txt
pip install google-cloud-storage beautifulsoup4

# 3. Set up environment
cp .env.example .env  # Configure your credentials
```

### **Authentication Setup**
📖 **Detailed Guide**: [`GCS_AUTHENTICATION_GUIDE.md`](GCS_AUTHENTICATION_GUIDE.md)

```bash
# Quick setup
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
export SUPABASE_URL="https://anwefmklplkjxkmzpnva.supabase.co"
export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

### **Verify Setup**
```bash
# Test GCS connection
python gcs_operations_guide.py

# Test database connection
python -c "from supabase import create_client; print('✅ Supabase connected')"
```

---

## 🎯 **Choose Your System**

### **Use Bulk CSV System When:**
- 📊 **Large-scale analysis** (273K+ cases available)
- 🏴󠁵󠁳󠁴󠁸󠁿 **Texas-focused research** (comprehensive coverage)
- 📈 **Historical trends** (pre-2025 data)
- 🤖 **Machine learning** (volume training data)
- ⚡ **Fast processing** (6 cases/sec proven)

### **Use API System When:**
- 🌐 **Recent cases** (August 2025+)
- 📋 **Rich metadata** (courts, judges, citations)
- 🔄 **Real-time updates** (live CourtListener data)
- 🏛️ **Multi-jurisdictional** (Federal + state courts)
- 📑 **Multi-document** (opinions + clusters + dockets)

---

## ⚡ **Quick Examples**

### **Example 1: Extract Historical Case (Bulk System)**
```python
from opinion_extractor import TexasOpinionExtractor

extractor = TexasOpinionExtractor()
opinion = extractor.get_full_opinion('11113215')

print(f"Case: {opinion['case_id']}")
print(f"Text: {len(opinion['text']):,} characters")
print(f"Source: {opinion['source']}")
```

### **Example 2: Extract Recent Case (API System)**
```python
from api_case_extractor import CourtListenerAPIExtractor

extractor = CourtListenerAPIExtractor()
case = extractor.get_complete_case('********')

print(f"Case: {case['case_name']}")
print(f"Court: {case['court']}")
print(f"Text: {len(case['full_text']):,} characters")
```

### **Example 3: Unified Access (Both Systems)**
```python
def get_any_case_text(case_id: str) -> str:
    # Try API system first (recent, rich metadata)
    api_extractor = CourtListenerAPIExtractor()
    text = api_extractor.get_full_text(case_id)
    if text:
        return text
    
    # Fallback to bulk system (historical, volume)
    bulk_extractor = TexasOpinionExtractor()
    opinion = bulk_extractor.get_full_opinion(case_id)
    return opinion['text'] if opinion else ""

# Works with any case ID from either system
text = get_any_case_text('********')  # API case
text = get_any_case_text('11113215')  # Bulk case
```

---

## 📊 **Bulk CSV System (Historical Cases)**

### **Overview**
- **Data Source**: 49.7GB CourtListener opinions CSV
- **Volume**: 6.7M rows → ~680K Texas cases
- **Current Status**: 273K cases processed, 5.6M rows remaining
- **Processing Rate**: 26 rows/sec, 6 TX cases/sec
- **Storage**: GCS compressed JSON (`TX/opinions/*.json.gz`)

### **Key Files & Guides**
| File | Purpose | When to Use |
|------|---------|-------------|
| **[`FULL_OPINION_EXTRACTION_GUIDE.md`](FULL_OPINION_EXTRACTION_GUIDE.md)** | Complete extraction guide | Understanding bulk system |
| **[`opinion_extractor.py`](opinion_extractor.py)** | Production toolkit | Extracting historical cases |
| **[`enhanced_bulk_loader.py`](enhanced_bulk_loader.py)** | Main processor | Continuing bulk processing |

### **Quick Start**
```bash
# Extract existing processed cases
python opinion_extractor.py

# Continue bulk processing (runs for hours)
python enhanced_bulk_loader.py --csv-file bulk_csv/opinions-2025-07-02.csv.bz2

# Check progress
python -c "from supabase import create_client; print('Cases:', len(create_client().table('cases').select('id').execute().data))"
```

### **Data Structure**
```
Database Record:
{
  "id": "11113215",
  "source": "courtlistener_csv", 
  "gcs_path": "TX/opinions/11113215.json.gz",
  "practice_area": "Personal Injury",
  "jurisdiction": "TX"
}

GCS File (TX/opinions/11113215.json.gz):
{
  "plain_text": "FULL OPINION TEXT...",
  "html_with_citations": "HTML VERSION...",
  "metadata": {...}
}
```

---

## 🌐 **API System (Recent Cases)**

### **Overview**
- **Data Source**: Live CourtListener API
- **Volume**: 500+ recent cases (expandable)
- **Update Frequency**: Real-time/scheduled
- **Storage**: Multi-file GCS (`FED/opinions/`, `FED/clusters/`, `FED/dockets/`)
- **Metadata**: Rich (case names, courts, judges, citations)

### **Key Files & Guides**
| File | Purpose | When to Use |
|------|---------|-------------|
| **[`COURTLISTENER_API_CASES_GUIDE.md`](COURTLISTENER_API_CASES_GUIDE.md)** | Complete API guide | Understanding API system |
| **[`api_case_extractor.py`](api_case_extractor.py)** | Production toolkit | Extracting API cases |
| **[`fetch_courtlistener_cases.py`](courtlistener/core/fetch_courtlistener_cases.py)** | API client | Fetching new cases |

### **Quick Start**
```bash
# Extract existing API cases
python api_case_extractor.py

# Fetch more recent cases (when needed)
python courtlistener/core/fetch_courtlistener_cases.py --practice-area "Personal Injury" --max-cases 100

# List available API cases
python -c "from api_case_extractor import CourtListenerAPIExtractor; print(CourtListenerAPIExtractor().list_api_cases())"
```

### **Data Structure**
```
Database Record:
{
  "id": "********",
  "source": "courtlistener",
  "gcs_path": "FED/opinions/********.json.gz",
  "gcs_cluster_path": "FED/clusters/10646617.json.gz",
  "gcs_docket_path": "FED/dockets/70633610.json.gz"
}

GCS Files:
- FED/opinions/********.json.gz (full opinion text)
- FED/clusters/10646617.json.gz (case metadata)  
- FED/dockets/70633610.json.gz (court proceedings)
```

---

## 🔐 **Infrastructure & Authentication**

### **Key Files & Guides**
| File | Purpose | When to Use |
|------|---------|-------------|
| **[`GCS_AUTHENTICATION_GUIDE.md`](GCS_AUTHENTICATION_GUIDE.md)** | Complete GCS setup | First-time setup |
| **[`gcs_operations_guide.py`](gcs_operations_guide.py)** | GCS toolkit | Working with GCS |

### **GCS Bucket Structure**
```
gs://texas-laws-personalinjury/
├── TX/                    # Bulk CSV system
│   └── opinions/          # 273K+ historical cases
├── FED/                   # API system  
│   ├── opinions/          # 500+ recent opinions
│   ├── clusters/          # Case metadata
│   └── dockets/           # Court proceedings
└── batches/               # Processing archives
```

### **Database Schema**
```sql
-- Main cases table
CREATE TABLE cases (
  id TEXT PRIMARY KEY,
  source TEXT,                    -- 'courtlistener_csv' or 'courtlistener'
  gcs_path TEXT,                  -- Link to opinion text
  gcs_cluster_path TEXT,          -- Link to metadata (API cases)
  practice_area TEXT,             -- AI-classified
  jurisdiction TEXT,              -- 'TX', 'FED', etc.
  created_at TIMESTAMP
);
```

---

## 🎯 **Development Workflows**

### **Workflow 1: Historical Research**
```bash
# 1. Query database for relevant cases
python -c "
from supabase import create_client
cases = create_client().table('cases').select('id').eq('practice_area', 'Personal Injury').limit(10).execute()
print([c['id'] for c in cases.data])
"

# 2. Extract full text
python -c "
from opinion_extractor import TexasOpinionExtractor
extractor = TexasOpinionExtractor()
for case_id in ['11113215', '11113216']:
    opinion = extractor.get_full_opinion(case_id)
    print(f'{case_id}: {len(opinion[\"text\"]):,} chars')
"
```

### **Workflow 2: Recent Case Analysis**
```bash
# 1. List recent API cases
python -c "
from api_case_extractor import CourtListenerAPIExtractor
extractor = CourtListenerAPIExtractor()
cases = extractor.list_api_cases(5)
for case in cases:
    print(f'{case[\"id\"]}: {case[\"created_at\"][:10]}')
"

# 2. Analyze case quality
python -c "
from api_case_extractor import CourtListenerAPIExtractor
extractor = CourtListenerAPIExtractor()
quality = extractor.analyze_api_case_quality('********')
print(f'Valid: {quality[\"valid\"]}, Text: {quality[\"text_length\"]:,} chars')
"
```

### **Workflow 3: Bulk Processing**
```bash
# 1. Check current progress
python -c "
from supabase import create_client
result = create_client().table('cases').select('source', 'count').execute()
print('Current database size by source:')
for row in result.data:
    print(f'  {row[\"source\"]}: {row[\"count\"]:,} cases')
"

# 2. Continue processing (long-running)
python enhanced_bulk_loader.py --csv-file bulk_csv/opinions-2025-07-02.csv.bz2 --limit 50000

# 3. Monitor progress
tail -f enhanced_bulk_loader.log
```

---

## 🚨 **Troubleshooting**

### **Common Issues**
| Issue | Solution | Reference |
|-------|----------|-----------|
| GCS authentication failed | Check service account setup | [`GCS_AUTHENTICATION_GUIDE.md`](GCS_AUTHENTICATION_GUIDE.md) |
| Case not found | Check which system (bulk vs API) | This guide - [Choose Your System](#choose-your-system) |
| Empty text returned | Verify GCS file exists | [`gcs_operations_guide.py`](gcs_operations_guide.py) |
| Database connection failed | Check Supabase credentials | [`.env` file configuration](#getting-started) |

### **Debug Commands**
```bash
# Test GCS connection
python -c "from google.cloud import storage; print('✅ GCS OK' if storage.Client().bucket('texas-laws-personalinjury').exists() else '❌ GCS Failed')"

# Test database connection  
python -c "from supabase import create_client; print('✅ DB OK' if create_client().table('cases').select('count').execute() else '❌ DB Failed')"

# Check case exists
python -c "
case_id = '********'
from api_case_extractor import CourtListenerAPIExtractor
print('✅ Found' if CourtListenerAPIExtractor().get_api_case_opinion(case_id) else '❌ Not found')
"
```

---

## 🎯 **Next Steps & Roadmap**

### **Immediate (Current Sprint)**
- 🔄 **Complete bulk processing** (5.6M rows remaining)
- 📊 **Reach 680K Texas cases** target
- 🤖 **Improve AI classification** accuracy

### **Short Term (Next Month)**
- 🌐 **Expand API fetching** (more recent cases)
- 🏛️ **Add Federal courts** coverage
- 🔍 **Implement full-text search**

### **Long Term (Next Quarter)**
- 🗺️ **Multi-state expansion** (NY, FL, OH)
- 📈 **Advanced analytics** dashboard
- 🔗 **Citation network** analysis
- 👥 **Multi-tenant access** controls

---

## 📞 **Support & Resources**

### **Getting Help**
1. **Start with this guide** - covers 90% of use cases
2. **Check specific guides** - linked throughout this document
3. **Run debug commands** - provided in troubleshooting section
4. **Review code examples** - all tools include working examples

### **Key Contacts**
- **System Architecture**: This master guide
- **Bulk Processing**: [`FULL_OPINION_EXTRACTION_GUIDE.md`](FULL_OPINION_EXTRACTION_GUIDE.md)
- **API Integration**: [`COURTLISTENER_API_CASES_GUIDE.md`](COURTLISTENER_API_CASES_GUIDE.md)
- **Infrastructure**: [`GCS_AUTHENTICATION_GUIDE.md`](GCS_AUTHENTICATION_GUIDE.md)

---

## 🏆 **Success Metrics**

### **Current Achievement**
- ✅ **273,673 cases** processed and accessible
- ✅ **100% uptime** for both systems
- ✅ **Zero data loss** with 1:1 GCS tracking
- ✅ **Production ready** with comprehensive tooling

### **Target Goals**
- 🎯 **680K Texas cases** (bulk system complete)
- 🎯 **6.7M total cases** (multi-state expansion)
- 🎯 **Real-time updates** (API system scaled)
- 🎯 **Sub-second search** (full-text indexing)

**Welcome to the Texas Laws Personal Injury legal opinion systems! Start with the [Quick Examples](#quick-examples) to see it working, then dive deeper into the specific guides as needed.** 🚀
