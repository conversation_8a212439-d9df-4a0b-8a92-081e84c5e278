# Production Pipeline Coordinator for Enhanced GraphRAG Workflow

## Overview

The Production Pipeline Coordinator orchestrates the complete legal document processing pipeline from CourtListener API through Enhanced GraphRAG processing to multi-backend storage. This production-ready system handles large-scale processing of Texas legal cases with cost efficiency, data integrity, and operational reliability.

## Features

### 🚀 Complete Workflow Orchestration
- **Fetch** → **Chunk** → **Embed** → **Store** pipeline coordination
- Intelligent batch processing with concurrency control
- Automatic practice area detection (Personal Injury, Criminal Defense, Family Law)
- Schema discovery and evolution with caching

### 💰 Cost Optimization
- Real-time cost monitoring with circuit breaker patterns
- Budget enforcement ($150 per 250k opinions target)
- Token usage tracking and optimization
- Cost-per-case analysis and reporting

### 🔄 Interruption & Resume
- Granular checkpoint management for fault tolerance
- Resume from any checkpoint without data duplication
- Intelligent recovery with data integrity validation
- Processing state persistence across restarts

### 🗄️ Multi-Backend Storage
- **GCS**: Raw document and JSON storage
- **Supabase**: Metadata and checkpoint management
- **Neo4j**: Graph relationships and entities
- **Pinecone**: Vector embeddings with namespacing

### 📊 Production Features
- Comprehensive health checks and monitoring
- Real-time processing status and metrics
- Retry logic with exponential backoff
- Circuit breaker for API protection
- Detailed audit logs and reporting

## Architecture

```
┌─────────────────────┐
│  CourtListener API  │
└──────────┬──────────┘
           │
           ▼
┌─────────────────────┐     ┌──────────────────┐
│ Pipeline Coordinator│────▶│ Cost Monitor     │
└──────────┬──────────┘     └──────────────────┘
           │
           ▼
┌─────────────────────┐     ┌──────────────────┐
│ Enhanced GraphRAG   │────▶│ Voyage-context-3 │
└──────────┬──────────┘     └──────────────────┘
           │
           ▼
┌─────────────────────┐
│ Storage Orchestrator│
└──────────┬──────────┘
           │
    ┌──────┴──────┬──────────┬──────────┐
    ▼             ▼          ▼          ▼
┌───────┐    ┌────────┐  ┌───────┐  ┌──────────┐
│  GCS  │    │Supabase│  │ Neo4j │  │ Pinecone │
└───────┘    └────────┘  └───────┘  └──────────┘
```

## Installation

### Prerequisites

1. Python 3.8+
2. Required API Keys:
   - CourtListener API Key
   - Voyage AI API Key
   - Neo4j credentials
   - Supabase project credentials
   - Pinecone API Key
   - Google Cloud credentials

### Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your credentials

# Initialize storage backends
python scripts/init_storage.py
```

## Usage

### Basic Processing

```python
from processing.production_pipeline_coordinator import (
    ProductionPipelineCoordinator,
    PipelineConfig
)

# Configure pipeline
config = PipelineConfig(
    courtlistener_api_key="your-key",
    jurisdiction="tex",
    max_cases_per_batch=100,
    daily_budget=150.0
)

# Create coordinator
coordinator = ProductionPipelineCoordinator(config)

# Process Texas cases
results = await coordinator.process_texas_cases(
    query="personal injury negligence",
    start_date="2024-01-01",
    max_cases=250000
)
```

### Resume from Checkpoint

```python
# Resume interrupted processing
results = await coordinator.process_texas_cases(
    resume_checkpoint="texas_batch_20250801_checkpoint"
)
```

### Monitor Processing

```python
# Get real-time status
status = await coordinator.get_processing_status()
print(f"Active cases: {status['active_cases']}")
print(f"Cost so far: ${status['metrics']['total_cost']:.4f}")

# Perform health check
health = await coordinator.health_check()
print(f"System health: {health['overall_status']}")
```

## Configuration Options

### PipelineConfig Parameters

| Parameter | Default | Description |
|-----------|---------|-------------|
| `courtlistener_api_key` | Required | CourtListener API authentication |
| `jurisdiction` | "tex" | Target jurisdiction |
| `max_cases_per_batch` | 100 | Batch size for processing |
| `max_concurrent_processing` | 5 | Concurrent case processing |
| `chunk_size` | 2000 | Document chunk size in tokens |
| `daily_budget` | 150.0 | Daily cost budget in USD |
| `hourly_budget` | 20.0 | Hourly cost budget in USD |
| `cost_per_case_limit` | 0.60 | Maximum cost per case |
| `checkpoint_interval` | 10 | Checkpoint every N cases |

## API Reference

### Main Methods

#### `process_texas_cases()`
```python
async def process_texas_cases(
    query: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    max_cases: Optional[int] = None,
    resume_checkpoint: Optional[str] = None
) -> Dict[str, Any]
```

Process Texas legal cases with optional filtering and resume capability.

#### `get_processing_status()`
```python
async def get_processing_status() -> Dict[str, Any]
```

Get current processing status including active cases, metrics, and cost status.

#### `health_check()`
```python
async def health_check() -> Dict[str, Any]
```

Perform comprehensive health check on all system components.

## Monitoring and Metrics

### Processing Metrics
- Total cases processed
- Success/failure rates
- Average processing time per case
- Total entities and relationships extracted
- Embedding generation statistics

### Cost Metrics
- Total cost and cost per case
- Token usage statistics
- Budget utilization (daily/hourly)
- Circuit breaker status

### Storage Metrics
- Backend availability status
- Data consistency scores
- Storage operation statistics

## Error Handling

The pipeline implements multiple layers of error handling:

1. **Retry Logic**: Automatic retry with exponential backoff
2. **Circuit Breaker**: Prevents cascade failures
3. **Checkpointing**: Enables resume on failure
4. **Graceful Degradation**: Continues processing on partial failures
5. **Comprehensive Logging**: Detailed error tracking and reporting

## Testing

Run the comprehensive test suite:

```bash
# Run all tests
python test_production_pipeline_coordinator.py

# Run specific demo
python test_production_pipeline_coordinator.py
# Select: 1 (Basic Processing)
```

## Performance Optimization

### Recommended Settings for Production

```python
config = PipelineConfig(
    max_cases_per_batch=1000,
    max_concurrent_processing=10,
    max_workers=20,
    chunk_size=2000,
    checkpoint_interval=100
)
```

### Cost Optimization Tips

1. **Batch Processing**: Larger batches reduce API overhead
2. **Caching**: Enable schema and embedding caching
3. **Selective Processing**: Use query filters to target specific cases
4. **Off-Peak Processing**: Schedule large jobs during off-peak hours

## Troubleshooting

### Common Issues

1. **Circuit Breaker Open**
   - Check cost limits and budget settings
   - Review recent error logs
   - Wait for automatic reset or manually reset

2. **Checkpoint Resume Failures**
   - Verify checkpoint exists
   - Check for data corruption
   - Use repair utilities if needed

3. **Storage Backend Errors**
   - Verify credentials and connectivity
   - Check backend health status
   - Review storage quotas

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Submit a pull request

## License

This project is proprietary software. All rights reserved.

## Support

For issues and questions:
- Check the troubleshooting guide
- Review logs in `pipeline_metrics/`
- Contact the development team