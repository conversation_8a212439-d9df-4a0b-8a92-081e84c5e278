# Parallel GraphRAG Strategy with Community Detection

## Overview

This document outlines the strategy for implementing **parallel GraphRAG processing** alongside the optimized bulk loader, using **Neo4j schema-based extraction**, **Voyage-Context-3 embeddings**, and **community detection** for legal case analysis.

## Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  BULK INGEST    │    │   GRAPHRAG      │    │ CLASSIFICATION  │
│    (Phase 1)    │───►│   (Parallel)    │    │   (Phase 3)     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • CSV → Supabase│    │ • Schema Extract│    │ • Texas Only    │
│ • CSV → GCS     │    │ • Entity Extract│    │ • PI/MM Focus   │
│ • Fast Ingest   │    │ • Relationship  │    │ • Keywords+LLM  │
│ • Queue Tasks   │    │ • Voyage-3 Embed│    │                 │
└─────────────────┘    │ • Neo4j Store   │    └─────────────────┘
        │              │ • Community Det │
        ▼              └─────────────────┘
┌─────────────────┐             ▲
│  MESSAGE QUEUE  │─────────────┘
│   (Redis/RMQ)   │
├─────────────────┤
│ • Priority Queue│
│ • Texas Priority│  
│ • Retry Logic   │
│ • Worker Coord  │
└─────────────────┘
```

## Core Components

### 1. Queue-Based Coordination

**Message Queue Infrastructure:**
- **Technology**: Redis with priority queues
- **Task Distribution**: Round-robin to GraphRAG workers
- **Priority System**: 
  - Priority 3: Texas PI/MM cases (urgent)
  - Priority 2: Texas other cases (high)
  - Priority 1: Non-Texas cases (normal)
- **Retry Logic**: Failed tasks retry up to 3 times with exponential backoff
- **Dead Letter Queue**: Failed tasks after 3 retries for manual review

**Queue Structure:**
```json
{
  "task_id": "uuid-v4",
  "case_id": "12345",
  "content": "legal_document_text...",
  "metadata": {
    "jurisdiction": "TX",
    "year_filed": 2023,
    "practice_area": "Personal Injury",
    "court_id": "texas-verified"
  },
  "priority": 2,
  "retry_count": 0,
  "created_at": "2025-08-13T10:30:00Z"
}
```

### 2. Schema-Based Entity Extraction (Neo4j Approach)

**Legal Document Schema:**
```python
LEGAL_SCHEMA = {
    "entities": {
        "Case": {
            "properties": ["case_name", "docket_number", "year_filed", "jurisdiction", "practice_area"],
            "required": ["case_name"],
            "embedding_fields": ["case_name", "summary"]
        },
        "Court": {
            "properties": ["court_name", "jurisdiction", "level", "circuit"],
            "required": ["court_name"],
            "embedding_fields": ["court_name"]
        },
        "Judge": {
            "properties": ["name", "title", "appointment_year"],
            "required": ["name"],
            "embedding_fields": ["name"]
        },
        "Party": {
            "properties": ["name", "type", "role", "entity_type"],
            "required": ["name", "type"],  # type: plaintiff, defendant, appellant, appellee
            "embedding_fields": ["name"]
        },
        "LegalConcept": {
            "properties": ["concept", "area_of_law", "definition", "precedent_strength"],
            "required": ["concept"],
            "embedding_fields": ["concept", "definition"]
        },
        "Citation": {
            "properties": ["citation_text", "case_name", "year", "court", "volume", "page"],
            "required": ["citation_text"],
            "embedding_fields": ["citation_text", "case_name"]
        },
        "LegalIssue": {
            "properties": ["issue_description", "practice_area", "outcome", "legal_standard"],
            "required": ["issue_description"],
            "embedding_fields": ["issue_description"]
        },
        "Attorney": {
            "properties": ["name", "firm", "role", "bar_number"],
            "required": ["name"],
            "embedding_fields": ["name", "firm"]
        },
        "Statute": {
            "properties": ["statute_citation", "title", "section", "jurisdiction"],
            "required": ["statute_citation"],
            "embedding_fields": ["statute_citation", "title"]
        }
    },
    "relationships": {
        "DECIDED_BY": {"from": "Case", "to": "Court", "properties": ["decision_date"]},
        "PRESIDED_BY": {"from": "Case", "to": "Judge", "properties": ["role"]},
        "INVOLVES": {"from": "Case", "to": "Party", "properties": ["party_role", "outcome"]},
        "CITES": {"from": "Case", "to": "Citation", "properties": ["citation_purpose", "precedent_weight"]},
        "ADDRESSES": {"from": "Case", "to": "LegalIssue", "properties": ["resolution", "holding"]},
        "RELATES_TO": {"from": "LegalIssue", "to": "LegalConcept", "properties": ["relevance_score"]},
        "SIMILAR_TO": {"from": "Case", "to": "Case", "properties": ["similarity_score", "similarity_type"]},
        "APPEALS": {"from": "Case", "to": "Case", "properties": ["appeal_outcome", "reversed"]},
        "REPRESENTED_BY": {"from": "Party", "to": "Attorney", "properties": ["representation_type"]},
        "INTERPRETS": {"from": "Case", "to": "Statute", "properties": ["interpretation_type"]},
        "OVERRULES": {"from": "Case", "to": "Case", "properties": ["overrule_scope"]},
        "DISTINGUISHES": {"from": "Case", "to": "Case", "properties": ["distinction_reason"]}
    }
}
```

### 3. Voyage-Context-3 Embeddings

**Embedding Strategy:**
```python
EMBEDDING_CONFIG = {
    "model": "voyage-context-3",
    "dimension": 1024,
    "input_type": "document",
    "truncation": True,
    "max_tokens": 32000,  # voyage-context-3 extended context
    "batch_size": 16,
    "embedding_types": {
        "case_content": "Full case text embedding",
        "case_summary": "Summary/holding embedding", 
        "legal_issue": "Issue-specific embedding",
        "citation_context": "Citation context embedding"
    }
}
```

**Multi-Level Embeddings:**
1. **Document Level**: Full case text (primary search)
2. **Issue Level**: Individual legal issues (precise matching)
3. **Citation Level**: Citation context (precedent analysis)
4. **Entity Level**: Key entities (entity linking)

### 4. Community Detection Integration

**Community Detection Algorithms:**

#### **A. Legal Practice Area Communities**
```cypher
// Detect communities based on practice areas and legal concepts
CALL gds.louvain.write.estimate('legal-graph', {
  nodeLabels: ['Case', 'LegalConcept', 'LegalIssue'],
  relationshipTypes: ['ADDRESSES', 'RELATES_TO', 'SIMILAR_TO'],
  writeProperty: 'practiceAreaCommunity'
})
```

#### **B. Jurisdictional Communities**
```cypher
// Detect communities based on court jurisdiction and citations
CALL gds.louvain.write('legal-graph', {
  nodeLabels: ['Case', 'Court', 'Citation'],
  relationshipTypes: ['DECIDED_BY', 'CITES'],
  writeProperty: 'jurisdictionalCommunity',
  relationshipWeightProperty: 'citation_weight'
})
```

#### **C. Temporal Case Evolution Communities**
```cypher
// Detect communities showing legal evolution over time
CALL gds.leiden.write('legal-graph', {
  nodeLabels: ['Case'],
  relationshipTypes: ['CITES', 'OVERRULES', 'DISTINGUISHES'],
  writeProperty: 'evolutionCommunity',
  gamma: 1.5,
  theta: 0.01
})
```

#### **D. Attorney/Firm Practice Networks**
```cypher
// Detect attorney practice communities
CALL gds.louvain.write('legal-graph', {
  nodeLabels: ['Attorney', 'Party', 'Case'],
  relationshipTypes: ['REPRESENTED_BY', 'INVOLVES'],
  writeProperty: 'attorneyCommunity'
})
```

**Community Analysis Metrics:**
```python
COMMUNITY_METRICS = {
    "modularity": "Quality of community division",
    "conductance": "Community boundary strength", 
    "coverage": "Fraction of edges within communities",
    "performance": "Community detection accuracy",
    "size_distribution": "Community size analysis",
    "temporal_stability": "How communities evolve over time"
}
```

### 5. Parallel Processing Strategy

**Timeline Integration:**
```
Time:     0    6h   12h   18h   24h   30h   36h   42h
Ingest:   ████████████████████████████████
GraphRAG:      ████████████████████████████████████████
Community:                      ████████████████████████
Marking:                           █
Classify:                            ████████████████
```

**Worker Distribution:**
- **1 Bulk Ingest Worker**: Feeds queue while processing CSV
- **4 GraphRAG Workers**: Process queue → Neo4j graph
- **1 Community Detection Worker**: Runs algorithms on growing graph
- **1 Classification Worker**: Processes Texas cases
- **1 Monitoring Worker**: Tracks all pipelines

**Resource Allocation:**
```python
WORKER_CONFIG = {
    "bulk_ingest": {"cpu": "2 cores", "memory": "4GB", "priority": "high"},
    "graphrag_workers": {"cpu": "1.5 cores each", "memory": "3GB each", "count": 4},
    "community_detection": {"cpu": "2 cores", "memory": "8GB", "priority": "low"},
    "classification": {"cpu": "1 core", "memory": "2GB", "priority": "medium"},
    "monitoring": {"cpu": "0.5 cores", "memory": "1GB", "priority": "low"}
}
```

## Performance Benefits

| Aspect | Sequential | Parallel GraphRAG | Improvement |
|--------|------------|-------------------|-------------|
| **Total Time** | 3 days | 2.5 days | **17% faster** |
| **Graph Available** | After completion | During ingest | **Real-time** |
| **Resource Usage** | 30% CPU | 85% CPU | **Better utilization** |
| **Early Analysis** | Not possible | Available in 6h | **Immediate insights** |
| **Community Detection** | Post-processing | Incremental | **Continuous analysis** |
| **Similarity Search** | Not available | Real-time | **Live recommendations** |

## Implementation Phases

### Phase A: Queue Integration (Week 1)
**Deliverables:**
1. **Modified Optimized Loader**: Add Redis queue publishing
2. **Redis Infrastructure**: Setup with priority queues
3. **Basic GraphRAG Worker**: Simple entity extraction
4. **Queue Monitoring**: Track task processing
5. **Integration Testing**: Verify coordination

**Technical Tasks:**
```python
# 1. Modify optimized_bulk_loader.py
def _insert_batch_optimized(self, batch_data):
    # Existing Supabase + GCS logic
    # NEW: Publish to GraphRAG queue
    for record, content, gcs_path in batch_data:
        self.queue_manager.add_task(
            case_id=record['id'],
            content=content,
            metadata=record,
            priority=2 if record.get('jurisdiction') == 'TX' else 1
        )

# 2. Basic GraphRAG worker
class BasicGraphRAGWorker:
    async def process_task(self, task):
        entities = self.extract_basic_entities(task.content)
        embedding = await self.voyage_embed(task.content)
        self.store_in_neo4j(entities, embedding)
```

### Phase B: Schema Extraction (Week 2)
**Deliverables:**
1. **Complete Legal Schema**: Full entity/relationship definitions
2. **LLM Integration**: GPT-4 for schema-guided extraction
3. **Neo4j Graph Storage**: Structured data storage
4. **Extraction Validation**: Quality metrics and testing
5. **Performance Optimization**: Batch processing

**Technical Tasks:**
```python
# 1. Schema-guided extraction
class SchemaBasedExtractor:
    def extract_entities_and_relationships(self, content, metadata):
        prompt = self.build_schema_prompt(content, self.LEGAL_SCHEMA)
        response = await openai.ChatCompletion.acreate(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1
        )
        return self.parse_extraction(response)

# 2. Neo4j storage with schema validation
class Neo4jGraphStore:
    def store_case_graph(self, case_id, entities, relationships, embeddings):
        with self.driver.session() as session:
            # Create nodes with proper schema
            # Create relationships with properties
            # Store multiple embedding types
```

### Phase C: Voyage-Context-3 Integration (Week 3)
**Deliverables:**
1. **Voyage Embedding Service**: Context-aware embeddings
2. **Multi-Level Embeddings**: Document, issue, citation levels
3. **Semantic Search**: Similarity queries
4. **Embedding Storage**: Efficient vector storage in Neo4j
5. **Search Interface**: Query endpoints

**Technical Tasks:**
```python
# 1. Multi-level embedding generation
class VoyageEmbeddingService:
    async def generate_multi_level_embeddings(self, case_data):
        embeddings = {}
        
        # Document level
        embeddings['document'] = await self.embed_text(
            case_data['content'], input_type="document"
        )
        
        # Issue level
        for issue in case_data['legal_issues']:
            embeddings[f'issue_{issue.id}'] = await self.embed_text(
                issue['description'], input_type="query"
            )
        
        # Citation level
        for citation in case_data['citations']:
            embeddings[f'citation_{citation.id}'] = await self.embed_text(
                citation['context'], input_type="document"
            )
        
        return embeddings

# 2. Similarity search
def find_similar_cases(case_embedding, jurisdiction=None, practice_area=None, limit=10):
    query = """
    CALL db.index.vector.queryNodes('case_embeddings', $limit, $embedding)
    YIELD node, score
    WHERE ($jurisdiction IS NULL OR node.jurisdiction = $jurisdiction)
    AND ($practice_area IS NULL OR node.primary_practice_area = $practice_area)
    RETURN node.case_name, node.jurisdiction, score
    ORDER BY score DESC
    """
```

### Phase D: Community Detection (Week 4)
**Deliverables:**
1. **Community Detection Pipeline**: Automated algorithm execution
2. **Multi-Algorithm Analysis**: Louvain, Leiden, Label Propagation
3. **Incremental Updates**: Update communities as graph grows
4. **Community Analytics**: Metrics and visualization
5. **Legal Insights**: Practice area and jurisdiction communities

**Technical Tasks:**
```python
# 1. Incremental community detection
class CommunityDetectionPipeline:
    def __init__(self):
        self.algorithms = ['louvain', 'leiden', 'label_propagation']
        self.last_run_node_count = 0
        self.update_threshold = 1000  # New nodes trigger re-detection
    
    async def run_incremental_detection(self):
        current_node_count = self.get_node_count()
        
        if current_node_count - self.last_run_node_count > self.update_threshold:
            for algorithm in self.algorithms:
                await self.run_community_algorithm(algorithm)
            
            self.last_run_node_count = current_node_count
            await self.analyze_community_changes()

# 2. Legal-specific community analysis
class LegalCommunityAnalyzer:
    def analyze_practice_area_communities(self):
        # Detect cases that cluster by legal issues
        # Identify emerging practice areas
        # Track legal concept evolution
        
    def analyze_citation_networks(self):
        # Detect influential case clusters
        # Identify precedent hierarchies
        # Track legal doctrine evolution
        
    def analyze_jurisdictional_patterns(self):
        # Detect cross-jurisdictional influence
        # Identify circuit splits
        # Track legal harmonization
```

### Phase E: Advanced Features (Week 5)
**Deliverables:**
1. **Auto-scaling Workers**: Dynamic scaling based on queue size
2. **Advanced Monitoring**: Real-time GraphRAG metrics
3. **Query Optimization**: Efficient graph traversal
4. **Legal Analytics**: Practice area insights
5. **API Endpoints**: GraphRAG query interface

## Community Detection Use Cases

### 1. Practice Area Evolution
**Goal**: Track how legal practice areas evolve and merge over time

**Implementation**:
```cypher
// Detect cases that bridge multiple practice areas
MATCH (c:Case)-[:ADDRESSES]->(i:LegalIssue)
WHERE c.practiceAreaCommunity <> i.practiceAreaCommunity
RETURN c.case_name, c.practiceAreaCommunity, 
       collect(i.practiceAreaCommunity) as bridged_areas
ORDER BY size(bridged_areas) DESC
```

**Insights**:
- Emerging hybrid practice areas (e.g., "Cyber Personal Injury")
- Legal concept convergence
- Cross-disciplinary case patterns

### 2. Influential Case Networks
**Goal**: Identify cases that form the backbone of legal precedent

**Implementation**:
```cypher
// Find cases with high betweenness centrality in citation networks
CALL gds.betweenness.stream('legal-graph', {
  nodeLabels: ['Case'],
  relationshipTypes: ['CITES', 'OVERRULES']
})
YIELD nodeId, score
RETURN gds.util.asNode(nodeId).case_name, score
ORDER BY score DESC LIMIT 20
```

**Insights**:
- Landmark cases that bridge legal concepts
- Cases that create new precedent pathways
- Identify potential Supreme Court candidates

### 3. Jurisdictional Influence Patterns
**Goal**: Understand how legal decisions spread across jurisdictions

**Implementation**:
```cypher
// Detect cross-jurisdictional citation communities
MATCH (c1:Case)-[:CITES]->(c2:Case)
WHERE c1.jurisdiction <> c2.jurisdiction
WITH c1.jurisdiction as from_jurisdiction, c2.jurisdiction as to_jurisdiction, count(*) as citation_count
RETURN from_jurisdiction, to_jurisdiction, citation_count
ORDER BY citation_count DESC
```

**Insights**:
- Which circuits influence others most
- Interstate legal concept adoption patterns
- Federal vs. state law interaction networks

### 4. Attorney Practice Networks
**Goal**: Identify attorney specialization and collaboration patterns

**Implementation**:
```cypher
// Detect attorney communities based on case co-appearance
MATCH (a1:Attorney)-[:REPRESENTED_BY]-(p1:Party)-[:INVOLVES]-(c:Case)-[:INVOLVES]-(p2:Party)-[:REPRESENTED_BY]-(a2:Attorney)
WHERE a1 <> a2 AND p1.role = 'plaintiff' AND p2.role = 'defendant'
WITH a1, a2, count(c) as co_appearances
WHERE co_appearances > 3
RETURN a1.name, a2.name, co_appearances
ORDER BY co_appearances DESC
```

**Insights**:
- Specialist attorney networks
- Opposing counsel relationships
- Firm collaboration patterns

## Monitoring Integration

### Extended Monitoring Dashboard
```bash
🚀 OPTIMIZED BULK LOADER - LIVE MONITOR
==================================================
📋 Current Phase: BULK_INGEST
📊 Total Processed: 156,247 records
📈 CSV Progress: 23.4%

🕸️ GraphRAG Pipeline:
   Queue Pending: 1,247 tasks (TX: 423, Other: 824)
   Workers Active: 4/4 GraphRAG, 1/1 Community
   Graph Stats:
     • Nodes: 45,678 (Cases: 15,234, Entities: 30,444)
     • Relationships: 123,456
     • Embeddings: 45,234/45,678 (99.0%)
   
🔍 Community Detection:
   Last Run: 2 hours ago (43,000 nodes)
   Communities Found:
     • Practice Areas: 23 communities
     • Jurisdictional: 51 communities
     • Citation Networks: 156 communities
   Next Run: In 15 minutes (threshold: 1,000 new nodes)

📊 Performance Metrics:
   GraphRAG Rate: 2.3 cases/second
   Embedding Rate: 2.1 embeddings/second
   Community Update: Every 1,000 nodes
   
💻 System Resources:
   CPU Usage: 87.3% (↑ from parallel processing)
   Memory Usage: 65.2% (Neo4j: 8GB, Redis: 2GB)
   Neo4j Connections: 4/10
   Redis Queue: 1,247 pending, 45,234 completed

🎯 Early Insights Available:
   • 12 major practice area clusters identified
   • 3 circuit court influence patterns detected
   • 156 landmark cases with high centrality
   • Real-time similarity search active
```

## Expected Outcomes

### Immediate Benefits (Within 24 hours)
1. **Real-time Graph Growth**: Watch knowledge graph build incrementally
2. **Early Similarity Search**: Find similar cases as they're processed
3. **Entity Recognition**: Identify key legal entities and relationships
4. **Practice Area Clustering**: See cases group by legal concepts

### Medium-term Benefits (Within 1 week)
1. **Community Insights**: Understand practice area boundaries
2. **Citation Networks**: Map precedent influence patterns
3. **Jurisdictional Analysis**: Cross-jurisdiction legal influence
4. **Semantic Search**: High-quality case similarity matching

### Long-term Benefits (Ongoing)
1. **Legal Trend Analysis**: Track how law evolves over time
2. **Predictive Insights**: Identify emerging legal concepts
3. **Research Acceleration**: Instant access to related cases
4. **Strategic Intelligence**: Understand legal landscape patterns

## Technical Requirements

### Infrastructure
```yaml
Redis:
  version: "7.0+"
  memory: "4GB"
  persistence: "RDB + AOF"
  
Neo4j:
  version: "5.0+"
  memory: "16GB"
  storage: "SSD recommended"
  plugins: ["GDS", "APOC"]
  
Voyage API:
  model: "voyage-context-3"
  rate_limit: "1000 requests/minute"
  
OpenAI API:
  model: "gpt-4"
  rate_limit: "200 requests/minute"
```

### Dependencies
```bash
pip install:
  - redis
  - neo4j
  - voyageai
  - openai
  - networkx
  - asyncio
  - python-dotenv
```

## Next Steps

1. **Tomorrow**: Review and refine this strategy
2. **Week 1**: Implement basic queue integration
3. **Week 2**: Add schema-based extraction
4. **Week 3**: Integrate Voyage-Context-3 embeddings
5. **Week 4**: Implement community detection
6. **Week 5**: Polish and optimize

This parallel GraphRAG approach will transform your legal data processing from simple ingestion into real-time knowledge graph construction with advanced community detection and semantic understanding.