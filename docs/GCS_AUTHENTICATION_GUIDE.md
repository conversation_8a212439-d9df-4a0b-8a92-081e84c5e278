# 🔐 GCS Authentication & Access Guide
## Texas Laws Personal Injury Project

### 📊 **Current GCS Data Overview**

**Bucket**: `gs://texas-laws-personalinjury/`
**Total Data**: ~400 Texas opinions + batch files + federal cases
**Structure**:
```
texas-laws-personalinjury/
├── TX/                    # Texas case data
│   ├── opinions/         # 400 processed opinions
│   ├── clusters/         # Case clustering data
│   └── dockets/          # Docket information
├── batches/              # Bulk upload archives (4 files)
├── FED/                  # Federal case data (1000+ files)
├── courtlistener/        # CourtListener API data
├── legal/                # Processed legal documents
├── ml-models/            # ML artifacts & embeddings
└── public/               # Shared resources
```

---

## 🚀 **Quick Start for Developers**

### **1. Environment Setup**
```bash
# Clone repository
git clone https://github.com/Jpkay/texas-laws-personalinjury.git
cd texas-laws-personalinjury

# Install dependencies
pip install google-cloud-storage python-dotenv

# Copy .env template and configure
cp .env.example .env
```

### **2. Authentication Methods**

#### **Method A: Service Account (Recommended for Production)**
```bash
# 1. Get service account key from project admin
# 2. Place in secure location
mkdir -p ~/.config/gcp
mv texas-laws-sa.json ~/.config/gcp/

# 3. Set environment variable
export GOOGLE_APPLICATION_CREDENTIALS="~/.config/gcp/texas-laws-sa.json"

# 4. Add to .env file
echo "GOOGLE_APPLICATION_CREDENTIALS=/Users/<USER>/.config/gcp/texas-laws-sa.json" >> .env
```

#### **Method B: User Account (Development)**
```bash
# Install gcloud CLI
curl https://sdk.cloud.google.com | bash

# Authenticate
gcloud auth login
gcloud config set project texas-laws-personalinjury

# Set application default credentials
gcloud auth application-default login
```

### **3. Test Connection**
```python
from google.cloud import storage
import os

# Test basic connection
client = storage.Client()
bucket = client.bucket('texas-laws-personalinjury')

# List some files
for blob in bucket.list_blobs(prefix='TX/', max_results=5):
    print(f"Found: {blob.name}")
```

---

## 🤖 **For LLM/AI Coders**

### **Essential Environment Variables**
```python
# Required in .env file
GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
GCS_BUCKET_NAME="texas-laws-personalinjury"
PROJECT_ID="texas-laws-personalinjury"
```

### **Standard Code Pattern**
```python
import os
from google.cloud import storage
from dotenv import load_dotenv

class GCSManager:
    def __init__(self):
        load_dotenv()
        self.client = storage.Client()
        self.bucket = self.client.bucket(os.getenv('GCS_BUCKET_NAME'))
    
    def upload_legal_document(self, case_id, content, jurisdiction='TX'):
        """Upload legal document with proper structure."""
        gcs_path = f"{jurisdiction}/opinions/{case_id}.json.gz"
        
        # Compress and upload
        import gzip, json
        compressed = gzip.compress(json.dumps(content).encode())
        
        blob = self.bucket.blob(gcs_path)
        blob.upload_from_string(compressed, content_type='application/gzip')
        
        return f"gs://{self.bucket.name}/{gcs_path}"
    
    def download_case(self, gcs_path):
        """Download and decompress case document."""
        blob = self.bucket.blob(gcs_path.replace(f'gs://{self.bucket.name}/', ''))
        compressed_data = blob.download_as_bytes()
        
        import gzip, json
        return json.loads(gzip.decompress(compressed_data).decode())
```

### **Common Operations**
```python
# Initialize
gcs = GCSManager()

# Upload case
case_data = {'id': '12345', 'title': 'Smith v. Jones', 'content': '...'}
gcs_url = gcs.upload_legal_document('12345', case_data)

# Download case
case = gcs.download_case('TX/opinions/12345.json.gz')

# List Texas cases
texas_cases = list(gcs.bucket.list_blobs(prefix='TX/opinions/'))
```

---

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

#### **403 Permission Denied**
```bash
# Check current authentication
gcloud auth list

# Re-authenticate if needed
gcloud auth application-default login

# Verify service account permissions
gcloud projects get-iam-policy texas-laws-personalinjury
```

#### **Credentials Not Found**
```python
import os
print("GOOGLE_APPLICATION_CREDENTIALS:", os.getenv('GOOGLE_APPLICATION_CREDENTIALS'))
print("File exists:", os.path.exists(os.getenv('GOOGLE_APPLICATION_CREDENTIALS', '')))
```

#### **Bucket Access Issues**
```bash
# Test bucket access
gsutil ls gs://texas-laws-personalinjury/

# Check bucket permissions
gsutil iam get gs://texas-laws-personalinjury/
```

### **Debug Script**
```python
def debug_gcs_setup():
    """Comprehensive GCS setup debugging."""
    import os, json
    from google.cloud import storage
    
    print("🔍 GCS Setup Debug")
    print("=" * 30)
    
    # Check environment
    creds = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    print(f"Credentials: {'✅' if creds else '❌'} {creds}")
    
    if creds and os.path.exists(creds):
        with open(creds) as f:
            data = json.load(f)
        print(f"Service Account: {data.get('client_email')}")
        print(f"Project: {data.get('project_id')}")
    
    # Test connection
    try:
        client = storage.Client()
        bucket = client.bucket('texas-laws-personalinjury')
        files = list(bucket.list_blobs(max_results=1))
        print("✅ GCS Connection: Success")
    except Exception as e:
        print(f"❌ GCS Connection: {e}")

# Run debug
debug_gcs_setup()
```

---

## 📋 **Current Data Summary**

| **Location** | **Content** | **Count** | **Purpose** |
|--------------|-------------|-----------|-------------|
| `TX/opinions/` | Texas legal opinions | 400 files | Core case database |
| `batches/` | Bulk upload archives | 4 files | Hybrid loader testing |
| `FED/clusters/` | Federal case clusters | 1000+ files | Federal jurisdiction |
| `courtlistener/` | API-sourced data | Various | External data integration |

**Key Metrics**:
- ✅ **400 Texas cases** ready for analysis
- ✅ **Batch processing** capability proven
- ✅ **Multi-jurisdictional** data structure
- ✅ **Compressed storage** (JSON.gz format)

---

## 🎯 **Best Practices**

### **For Production Code**
1. **Always use service accounts** (not user credentials)
2. **Compress large documents** (use gzip for JSON)
3. **Structure paths consistently** (`{jurisdiction}/{type}/{id}`)
4. **Handle errors gracefully** (network issues, permissions)
5. **Use batch operations** for multiple files

### **For Development**
1. **Test with small datasets** first
2. **Use dry-run modes** when available
3. **Monitor costs** (storage + operations)
4. **Version control** service account keys securely
5. **Document data schemas** and folder structures

### **Security**
- ✅ Never commit service account keys to git
- ✅ Use environment variables for credentials
- ✅ Rotate keys regularly
- ✅ Apply principle of least privilege
- ✅ Monitor access logs

---

## 📞 **Support**

**For access issues**: Contact project admin for service account keys
**For technical issues**: Use the provided debug scripts
**For new features**: Follow the code patterns in `gcs_operations_guide.py`

The GCS setup is production-ready with 400+ Texas cases already processed and stored! 🚀
