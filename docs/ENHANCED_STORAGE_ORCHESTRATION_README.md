# Enhanced Storage Orchestration System

## Overview

The Enhanced Storage Orchestration System is a sophisticated data management solution designed to coordinate GraphRAG operations across four storage backends: **GCS** (Google Cloud Storage), **Supabase** (PostgreSQL), **Neo4j** (Graph Database), and **Pinecone** (Vector Database). This system ensures data consistency, implements fault-tolerant storage patterns, and maintains data integrity across distributed storage architectures.

## 🚀 Key Features

### ✅ **Multi-Backend Coordination**
- Orchestrates storage operations across GCS, Supabase, Neo4j, and Pinecone
- Ensures atomic-like operations using compensation patterns
- Implements proper upsert patterns for each backend type

### ✅ **Global UID Management**
- Consistent global unique identifiers across all storage backends
- Cross-backend referential integrity validation
- Collision detection and resolution strategies

### ✅ **GraphRAG Integration**
- Native integration with Neo4j GraphRAG Python package v1.8.0
- Practice area-specific schema management and caching
- Legal domain entity and relationship extraction

### ✅ **ETL Checkpoint Management**
- Detailed ETL progress checkpoints in Supabase
- Resume-from-checkpoint functionality for interrupted processes
- Batch operation tracking and recovery

### ✅ **Data Integrity Validation**
- Cross-backend consistency checks using global UIDs
- Data completeness validation across all storage systems
- Integrity reports and discrepancy detection

### ✅ **Fault Tolerance & Recovery**
- Exponential backoff retry strategies with circuit breaker patterns
- Comprehensive error handling and rollback capabilities
- Audit logs for all failure events and recovery actions

## 📁 Architecture

```
Enhanced Storage Orchestration System
├── Enhanced Storage Orchestrator (Core)
│   ├── Global UID Management
│   ├── Multi-Backend Coordination
│   ├── ETL Checkpoint Management
│   └── Data Integrity Validation
├── GraphRAG Storage Manager
│   ├── Legal Schema Management
│   ├── Practice Area Specialization
│   └── Neo4j GraphRAG Integration
├── Enhanced Neo4j Connector
│   ├── GraphRAG Entity Operations
│   ├── Idempotent Operations
│   └── Batch Processing
└── Storage Backends
    ├── GCS (Document Storage)
    ├── Supabase (Metadata & Checkpoints)
    ├── Neo4j (Graph Relationships)
    └── Pinecone (Vector Embeddings)
```

## 🔧 Installation & Setup

### Prerequisites

1. **Python 3.11+** with required packages:
   ```bash
   pip install neo4j-graphrag[google-vertex-ai]
   pip install supabase
   pip install pinecone-client
   pip install google-cloud-storage
   pip install python-dotenv
   ```

2. **Storage Backend Configuration**:
   - **Supabase**: Database URL and service role key
   - **Neo4j**: AuraDB or self-hosted instance with APOC
   - **Pinecone**: API key and index configuration
   - **GCS**: Service account credentials and bucket access

### Environment Variables

Create a `.env` file with the following configuration:

```bash
# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Neo4j Configuration
NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=us-west1-gcp
PINECONE_INDEX_NAME=legal-documents

# Google Cloud Storage
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
GCS_BUCKET_NAME=your-gcs-bucket

# Optional: LLM Configuration for GraphRAG
GOOGLE_API_KEY=your_gemini_api_key
VOYAGE_API_KEY=your_voyage_api_key
```

### Database Schema Setup

1. **Initialize Supabase Schema**:
   ```sql
   -- Run the enhanced_storage_schema.sql file
   \i courtlistener/processing/src/processing/storage/enhanced_storage_schema.sql
   ```

2. **Verify Neo4j Installation**:
   ```cypher
   // Check APOC availability
   CALL apoc.help("apoc")
   ```

## 🏃‍♂️ Quick Start

### Basic Usage

```python
import asyncio
from courtlistener.processing.src.processing.storage.enhanced_storage_orchestrator import EnhancedStorageOrchestrator
from courtlistener.processing.src.processing.storage.graphrag_storage_manager import GraphRAGStorageManager, PracticeArea

async def main():
    # Initialize the storage orchestrator
    orchestrator = EnhancedStorageOrchestrator()
    
    # Initialize GraphRAG storage manager
    graphrag_manager = GraphRAGStorageManager(orchestrator)
    
    # Generate global UID
    global_uid = orchestrator.generate_global_uid(
        entity_type="legal_opinion",
        source_system="courtlistener",
        source_id="12345"
    )
    
    # Process legal document
    sample_text = "Judge Smith presided over the case..."
    result = await graphrag_manager.process_legal_document(
        document_text=sample_text,
        practice_area=PracticeArea.PERSONAL_INJURY,
        case_metadata={"court": "Texas Supreme Court"}
    )
    
    # Store results across all backends
    if result:
        storage_results = await graphrag_manager.store_graphrag_results(
            result=result,
            jurisdiction="tx"
        )
        print(f"Storage results: {storage_results}")
    
    # Validate data integrity
    validation = await orchestrator.validate_global_uid_integrity(global_uid.uid)
    print(f"Integrity score: {validation['consistency_score']}")
    
    # Cleanup
    orchestrator.close()

# Run the example
asyncio.run(main())
```

### ETL Checkpoint Management

```python
# Create ETL checkpoint
checkpoint = orchestrator.create_etl_checkpoint(
    pipeline_name="courtlistener_graphrag",
    batch_id="batch_001",
    metadata={"practice_area": "personal_injury"}
)

# Update checkpoint with progress
orchestrator.update_etl_checkpoint(
    checkpoint.checkpoint_id,
    processed_uids=["uid1", "uid2", "uid3"],
    failed_uids=["uid4"]
)

# Resume from checkpoint
processed, failed = orchestrator.resume_from_checkpoint(checkpoint.checkpoint_id)
print(f"Resuming: {len(processed)} processed, {len(failed)} failed")
```

### Schema Management

```python
# Cache GraphRAG schema for practice area
schema_data = {
    "node_types": [
        {"label": "Judge", "properties": [{"name": "name", "type": "STRING"}]}
    ],
    "relationship_types": [
        {"label": "PRESIDES_OVER", "properties": []}
    ]
}

schema_id = orchestrator.store_graphrag_schema(
    practice_area="personal_injury",
    schema_data=schema_data,
    schema_version="1.0"
)

# Retrieve cached schema
cached_schema = orchestrator.get_cached_schema("personal_injury")
```

## 📊 Storage Backends

### GCS (Google Cloud Storage)
- **Purpose**: Raw document storage and archival
- **Data Types**: JSON documents, PDFs, text files
- **Organization**: Hierarchical by jurisdiction and document type
- **Features**: Versioning, lifecycle management, cost optimization

### Supabase (PostgreSQL)
- **Purpose**: Metadata, checkpoints, schemas, operational data
- **Data Types**: Structured metadata, JSON configurations, audit logs
- **Tables**: 
  - `global_uid_registry` - Cross-backend UID tracking
  - `graphrag_schemas` - Cached extraction schemas
  - `etl_checkpoints` - Processing checkpoints
  - `storage_operations` - Operation audit trail

### Neo4j (Graph Database)
- **Purpose**: Entity relationships, citation networks, legal knowledge graphs
- **Data Types**: Nodes (entities), relationships, graph structures
- **Features**: GraphRAG integration, APOC procedures, full-text search
- **Labels**: `GraphRAGEntity`, `Judge`, `Court`, `Case`, etc.

### Pinecone (Vector Database)
- **Purpose**: Semantic embeddings, similarity search, RAG retrieval
- **Data Types**: Vector embeddings (1024-dimensional)
- **Organization**: Namespace-based by jurisdiction and document type
- **Features**: Fast similarity search, metadata filtering, hybrid search

## 🔧 Practice Area Specialization

The system supports specialized schemas for different legal practice areas:

### Supported Practice Areas
- **Personal Injury**: Medical entities, damages, liability relationships
- **Criminal Defense**: Charges, evidence, procedural steps, sentencing
- **Family Law**: Custody, support, property division, family relationships
- **Estate Planning**: Wills, trusts, beneficiaries, asset distribution
- **Immigration Law**: Status, applications, visas, proceedings
- **Real Estate**: Property transactions, contracts, financing, titles
- **Bankruptcy**: Debts, assets, discharge orders, trustee actions

### Schema Customization
Each practice area has:
- **Specialized Entity Types**: Domain-specific entities (e.g., "Injury", "Custody")
- **Custom Relationships**: Practice-specific relationships (e.g., "SUFFERED_INJURY")
- **Extraction Prompts**: Tailored prompts for accurate entity extraction
- **Validation Rules**: Domain-specific validation and quality checks

## 🔍 Data Integrity & Validation

### Integrity Validation Features
- **Cross-Backend Consistency**: Verify same global UID exists across all backends
- **Referential Integrity**: Ensure relationships point to valid entities
- **Data Completeness**: Check for missing or incomplete data
- **Schema Compliance**: Validate against practice area schemas

### Validation Reports
```python
# Validate single entity
report = await orchestrator.validate_global_uid_integrity("uid-123")
print(f"Consistency score: {report['consistency_score']}")
print(f"Backend status: {report['backend_status']}")
print(f"Issues: {report['issues']}")

# Batch validation
batch_report = await orchestrator.validate_batch_integrity(["uid1", "uid2", "uid3"])
print(f"Overall score: {batch_report['overall_score']}")
```

## 📈 Monitoring & Analytics

### System Statistics
```python
# Get comprehensive statistics
stats = orchestrator.get_storage_statistics()
print(f"Global UIDs: {stats['global_uids']}")
print(f"Schemas: {stats['schemas']}")
print(f"Checkpoints: {stats['checkpoints']}")
print(f"Integrity: {stats['integrity']}")
```

### Performance Metrics
- **Processing Rate**: Items processed per minute
- **Success Rate**: Percentage of successful operations
- **Backend Response Times**: Latency for each storage backend
- **Error Rates**: Failure rates by backend and operation type
- **Cost Tracking**: API usage and estimated costs

## 🚨 Error Handling & Recovery

### Error Categories
1. **Network Errors**: Connection timeouts, service unavailable
2. **Authentication Errors**: Invalid credentials, expired tokens
3. **Data Validation Errors**: Schema violations, type mismatches
4. **Capacity Errors**: Storage limits, rate limiting
5. **System Errors**: Unexpected failures, dependency issues

### Recovery Strategies
- **Exponential Backoff**: Retry with increasing delays
- **Circuit Breaker Pattern**: Fail fast when backend is down
- **Compensation Patterns**: Rollback operations on failure
- **Dead Letter Queues**: Store failed operations for manual review
- **Health Checks**: Monitor backend availability

### Rollback Capabilities
```python
# Automatic rollback on failure
try:
    result = await orchestrator.store_graphrag_entity(global_uid, entity_data, "tx", "personal_injury")
except Exception as e:
    # System automatically attempts rollback of partial operations
    logger.error(f"Storage failed with rollback: {e}")
```

## 🧪 Testing

### Run Integration Tests
```bash
# Basic integration test
python test_enhanced_storage_integration.py

# Full system test (requires backend configuration)
python test_enhanced_storage_system.py

# Usage example (demonstration)
python enhanced_storage_usage_example.py
```

### Test Coverage
- ✅ File structure and syntax validation
- ✅ Storage connector availability
- ✅ SQL schema validity
- ✅ Global UID generation and tracking
- ✅ Schema storage and caching
- ✅ ETL checkpoint management
- ✅ Data integrity validation
- ✅ Error handling and recovery
- ✅ Batch operations
- ✅ Monitoring and statistics

## 📚 API Reference

### EnhancedStorageOrchestrator

#### Core Methods
- `generate_global_uid()` - Generate and register global UID
- `store_graphrag_entity()` - Store entity across all backends
- `store_graphrag_relationship()` - Store relationship data
- `validate_global_uid_integrity()` - Check data consistency
- `create_etl_checkpoint()` - Create processing checkpoint
- `get_storage_statistics()` - Get system statistics

### GraphRAGStorageManager

#### Key Methods
- `extract_legal_schema()` - Extract schema from legal text
- `process_legal_document()` - Process document through GraphRAG
- `store_graphrag_results()` - Store processing results
- `validate_extraction_quality()` - Validate extraction quality
- `get_practice_area_stats()` - Get practice area statistics

### EnhancedNeo4jConnector

#### GraphRAG Methods
- `create_graphrag_entity()` - Create GraphRAG entity node
- `create_graphrag_relationship()` - Create relationship
- `create_graphrag_entities_batch()` - Batch entity creation
- `get_graphrag_entity()` - Retrieve entity by global UID
- `validate_graphrag_entity_integrity()` - Validate entity integrity

## 🔄 Integration with CourtListener

### Integration Points
1. **Data Ingestion**: Process CourtListener API responses
2. **Opinion Processing**: Extract entities from legal opinions
3. **Citation Networks**: Build citation relationship graphs
4. **Judge Analytics**: Track judge patterns and statistics
5. **Practice Area Classification**: Automatic categorization
6. **Batch Processing**: Handle large-scale data processing

### Example Integration
```python
# Process CourtListener opinion through enhanced storage
courtlistener_data = {
    "cl_opinion_id": "12345",
    "case_name": "Smith v. Johnson",
    "html_with_citations": "...",
    "practice_area": "personal_injury"
}

# Generate global UID
global_uid = orchestrator.generate_global_uid(
    entity_type="legal_opinion",
    source_system="courtlistener",
    source_id=courtlistener_data["cl_opinion_id"]
)

# Process through GraphRAG
result = await graphrag_manager.process_legal_document(
    document_text=courtlistener_data["html_with_citations"],
    practice_area=PracticeArea.PERSONAL_INJURY,
    case_metadata=courtlistener_data
)

# Store across all backends
storage_results = await graphrag_manager.store_graphrag_results(result, "tx")
```

## 💰 Cost Optimization

### Cost Monitoring
- **GraphRAG API Usage**: Track LLM API calls and tokens
- **Storage Costs**: Monitor GCS, Supabase, Neo4j, Pinecone usage
- **Processing Efficiency**: Optimize batch sizes and caching
- **Schema Reuse**: Cache schemas to reduce extraction costs

### Budget Controls
- **Cost Limits**: Set maximum spending thresholds
- **Usage Alerts**: Notifications when approaching limits
- **Optimization Suggestions**: Automated cost reduction recommendations
- **Batch Optimization**: Process documents in cost-effective batches

## 🤝 Contributing

### Development Setup
1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Configure environment variables
4. Run integration tests
5. Create feature branch for changes

### Code Standards
- **Type Hints**: Use comprehensive type annotations
- **Documentation**: Document all public methods
- **Error Handling**: Implement proper exception handling
- **Testing**: Write tests for new functionality
- **Logging**: Use appropriate logging levels

## 📄 License

This project is part of the Texas Laws Personal Injury processing system. See the main project license for terms and conditions.

## 🆘 Support

### Troubleshooting
1. **Check Environment Variables**: Ensure all required credentials are set
2. **Verify Backend Connectivity**: Test each storage backend individually
3. **Review Logs**: Check application logs for detailed error messages
4. **Run Integration Tests**: Use test suite to identify issues
5. **Check Documentation**: Review API documentation for usage examples

### Common Issues
- **Connection Timeouts**: Increase timeout values or check network connectivity
- **Schema Errors**: Verify database schema is properly initialized
- **Authentication Failures**: Check credentials and permissions
- **Memory Issues**: Reduce batch sizes for large datasets
- **Cost Overruns**: Monitor usage and adjust processing parameters

### Getting Help
- Review the implementation plan: `ENHANCED_GRAPHRAG_IMPLEMENTATION_PLAN_2025-08-01.md`
- Run the usage example: `python enhanced_storage_usage_example.py`
- Check integration tests: `python test_enhanced_storage_integration.py`
- Review code documentation and comments in source files

---

**Enhanced Storage Orchestration System v1.0**  
*Coordinating GraphRAG operations across distributed storage architectures*