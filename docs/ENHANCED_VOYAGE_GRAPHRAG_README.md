# Enhanced Voyage-context-3 GraphRAG Integration

This document provides comprehensive documentation for the enhanced Voyage-context-3 embeddings integration with the GraphRAG Pipeline for legal document processing.

## 🚀 Overview

The Enhanced Voyage-context-3 GraphRAG Integration combines state-of-the-art vector embeddings with knowledge graph construction to create a powerful legal intelligence system. This integration provides:

- **2k Token Chunking**: Optimal document segmentation with legal boundary preservation
- **Multi-Type Embeddings**: Document chunks, entities, and relationships embeddings
- **Practice-Area Specialization**: Domain-specific processing and storage
- **Hybrid Search**: Vector similarity combined with graph traversal
- **Cost Optimization**: Intelligent batching and caching strategies
- **Legal Domain Optimization**: Terminology normalization and citation processing

## 📁 File Structure

```
courtlistener/processing/src/processing/
├── enhanced_voyage_graphrag_processor.py    # Main processor with GraphRAG integration
├── legal_domain_optimizations.py           # Legal terminology and citation processing
└── storage/
    ├── pinecone_connector.py               # Enhanced Pinecone connector with GraphRAG support
    ├── graphrag_storage_manager.py         # GraphRAG-specific storage coordination
    └── enhanced_storage_orchestrator.py    # Multi-backend storage coordination

test_enhanced_voyage_graphrag_integration.py    # Comprehensive test suite
enhanced_graphrag_production_demo.py            # Production demonstration script
```

## 🔧 Core Components

### 1. Enhanced Voyage-context-3 Processor

**File**: `enhanced_voyage_graphrag_processor.py`

The main processor that orchestrates the complete pipeline:

```python
from processing.enhanced_voyage_graphrag_processor import EnhancedVoyageGraphRAGProcessor

# Initialize with optimized configuration
processor = EnhancedVoyageGraphRAGProcessor({
    'model': 'voyage-context-3',
    'chunking': {
        'chunk_size': 2000,
        'overlap_size': 200,
        'preserve_sentences': True
    },
    'cost_limits': {
        'max_cost_per_document': 0.10,
        'daily_budget': 100.0
    }
})

# Process legal document
result = await processor.process_graphrag_document(
    document_text=legal_text,
    case_metadata=metadata,
    practice_area="personal_injury"
)
```

**Key Features**:
- **2k Token Chunking**: Preserves legal structure and context
- **Multi-Type Embeddings**: Generates embeddings for chunks, entities, and relationships
- **Cost Optimization**: Intelligent batching with budget controls
- **Caching**: Embedding cache for cost reduction
- **Error Handling**: Robust retry logic with exponential backoff

### 2. Enhanced Pinecone Connector

**File**: `storage/pinecone_connector.py`

Extended Pinecone connector with GraphRAG-specific capabilities:

```python
from processing.storage.pinecone_connector import PineconeConnector

connector = PineconeConnector()

# Store document chunks with metadata
success = connector.store_document_chunk_embeddings(
    chunks=chunks,
    jurisdiction="tx",
    practice_area="personal_injury",
    global_uid=document_uid
)

# Hybrid search across multiple embedding types
results = connector.hybrid_search(
    query_vector=query_embedding,
    jurisdiction="tx",
    practice_area="personal_injury",
    search_types=['document_chunks', 'entities', 'relationships'],
    top_k=10
)
```

**Enhanced Features**:
- **Multi-Namespace Storage**: Separate namespaces for chunks, entities, relationships
- **Practice-Area Organization**: `{jurisdiction}_{practice_area}_{type}` namespace pattern
- **Hybrid Search**: Search across multiple embedding types simultaneously
- **Rich Metadata**: Legal entities, confidence scores, extraction metadata

### 3. Legal Domain Optimizations

**File**: `legal_domain_optimizations.py`

Specialized optimizations for legal terminology and concepts:

```python
from processing.legal_domain_optimizations import LegalDomainOptimizer

optimizer = LegalDomainOptimizer()

# Normalize legal text
normalized = optimizer.normalize_legal_text(raw_text)

# Extract legal entities
entities = optimizer.extract_judicial_entities(text)
citations = optimizer.extract_citations(text)

# Classify practice area
classifications = optimizer.classify_practice_area(text)

# Create enhanced embedding text
enhanced = optimizer.create_enhanced_embedding_text(
    original_text=text,
    practice_area="personal_injury",
    include_citations=True,
    include_entities=True
)
```

**Legal Optimizations**:
- **Terminology Normalization**: Standardizes legal synonyms and abbreviations
- **Citation Processing**: Extracts and classifies legal citations
- **Entity Extraction**: Judges, courts, attorneys, parties
- **Practice Area Classification**: Automated practice area detection
- **Quality Metrics**: Legal content density and relevance scoring

## 🗄️ Storage Architecture

### Namespace Organization

The system uses a hierarchical namespace structure in Pinecone:

```
{jurisdiction}_{practice_area}_docs          # Document chunks
{jurisdiction}_{practice_area}_entities      # Legal entities  
{jurisdiction}_{practice_area}_relationships # Entity relationships
{jurisdiction}_{practice_area}_citations     # Legal citations
```

**Examples**:
- `tx_personal_injury_docs` - Texas personal injury document chunks
- `tx_criminal_defense_entities` - Texas criminal defense entities
- `ca_family_law_relationships` - California family law relationships

### Storage Coordination

The Enhanced Storage Orchestrator coordinates data across:

- **GCS**: Document storage and archival
- **Supabase**: Metadata and structured data
- **Neo4j**: Knowledge graph storage
- **Pinecone**: Vector embeddings storage

## 🔍 Search Capabilities

### Hybrid Search

Combines vector similarity with graph traversal:

```python
# Multi-type hybrid search
results = await processor.hybrid_search(
    query="medical malpractice standard of care",
    jurisdiction="tx",
    practice_area="personal_injury",
    search_types=['document_chunks', 'entities', 'relationships'],
    top_k=5
)

# Results structure:
{
    'document_chunks': [
        {'id': 'doc_chunk_1', 'score': 0.95, 'metadata': {...}},
        # ...
    ],
    'entities': [
        {'id': 'entity_judge_smith', 'score': 0.87, 'metadata': {...}},
        # ...
    ],
    'relationships': [
        {'id': 'rel_presides_over', 'score': 0.82, 'metadata': {...}},
        # ...
    ]
}
```

### Search Features

- **Multi-Type Results**: Search across chunks, entities, and relationships
- **Practice-Area Filtering**: Scope search to specific legal domains
- **Jurisdiction-Specific**: Filter by geographic jurisdiction
- **Relevance Scoring**: Enhanced scoring with legal domain factors
- **Result Enhancement**: Additional context from GraphRAG data

## 📊 Performance & Cost Optimization

### Cost Controls

```python
config = {
    'cost_limits': {
        'max_cost_per_document': 0.10,   # Per document limit
        'daily_budget': 100.0,           # Daily spending limit
        'hourly_budget': 20.0            # Hourly rate limit
    },
    'optimization': {
        'enable_caching': True,          # Embedding cache
        'cache_ttl_hours': 24,           # Cache lifetime
        'focus_on_entities': True        # Prioritize entity embeddings
    }
}
```

### Performance Optimizations

- **Batch Processing**: Optimized batch sizes for Voyage API
- **Concurrent Processing**: Parallel embedding generation
- **Caching Strategy**: MD5-based embedding cache
- **Rate Limiting**: Respects API rate limits with backoff
- **Memory Management**: Efficient chunk processing

### Cost Monitoring

```python
# Get comprehensive statistics
stats = processor.get_processing_statistics()

print(f"Total cost: ${stats['embedding_stats']['total_cost']:.4f}")
print(f"Cost per document: ${stats['cost_analysis']['cost_per_case']:.4f}")
print(f"Embeddings per second: {stats['performance_metrics']['embeddings_per_second']:.2f}")
```

## 🧪 Testing & Validation

### Test Suite

Run the comprehensive test suite:

```bash
python test_enhanced_voyage_graphrag_integration.py
```

**Test Coverage**:
- Document chunking with legal optimization
- Voyage-context-3 embedding generation
- GraphRAG entity and relationship extraction
- Multi-namespace Pinecone storage
- Hybrid search functionality
- Performance and cost analysis

### Production Demo

Run the production demonstration:

```bash
python enhanced_graphrag_production_demo.py
```

**Demo Features**:
- End-to-end processing of sample legal documents
- Multi practice-area processing
- Cost and performance monitoring
- Quality assessment and recommendations
- Production readiness evaluation

## 📈 Quality Metrics

### Legal Content Quality

The system provides comprehensive quality metrics:

```python
quality = optimizer.calculate_legal_content_quality(text)

{
    'citation_density': 0.12,           # Citations per 100 words
    'legal_terminology_density': 0.35,  # Legal terms density
    'entity_coverage': 0.08,            # Entities per 50 words
    'practice_area_clarity': 0.87,      # Classification confidence
    'overall_quality': 0.74             # Weighted quality score
}
```

### Processing Statistics

```python
stats = processor.get_processing_statistics()

{
    'embedding_stats': {
        'total_chunks': 150,
        'total_entities': 45,
        'total_relationships': 32,
        'total_embeddings_generated': 227,
        'total_cost': 0.0234
    },
    'performance_metrics': {
        'embeddings_per_second': 12.5,
        'avg_time_per_document': 8.3
    },
    'cost_analysis': {
        'cost_per_embedding': 0.0001,
        'tokens_per_dollar': 42567
    }
}
```

## 🚀 Getting Started

### Prerequisites

1. **Environment Variables**:
   ```bash
   export VOYAGE_API_KEY="your_voyage_api_key"
   export PINECONE_API_KEY="your_pinecone_api_key"
   export SUPABASE_URL="your_supabase_url"
   export SUPABASE_SERVICE_ROLE_KEY="your_supabase_key"
   export NEO4J_URI="your_neo4j_uri"
   export NEO4J_PASSWORD="your_neo4j_password"
   ```

2. **Dependencies**:
   ```bash
   pip install aiohttp pinecone-client supabase neo4j psutil
   ```

### Basic Usage

```python
import asyncio
from processing.enhanced_voyage_graphrag_processor import EnhancedVoyageGraphRAGProcessor

async def process_legal_document():
    # Initialize processor
    processor = EnhancedVoyageGraphRAGProcessor()
    
    # Process document
    result = await processor.process_graphrag_document(
        document_text="Your legal document text...",
        case_metadata={
            'cl_opinion_id': 'case_123',
            'case_name': 'Smith v. Jones',
            'jurisdiction': 'tx',
            'practice_area': 'personal_injury'
        }
    )
    
    print(f"Processed: {result['processing_stats']['chunks_generated']} chunks")
    print(f"Entities: {result['processing_stats']['entities_extracted']}")
    print(f"Cost: ${result['cost_analysis']['estimated_cost']:.4f}")
    
    # Perform hybrid search
    search_results = await processor.hybrid_search(
        query="medical malpractice negligence",
        jurisdiction="tx",
        practice_area="personal_injury"
    )
    
    print(f"Search results: {search_results['total_results']}")
    
    processor.close()

# Run the example
asyncio.run(process_legal_document())
```

## 🔧 Configuration Options

### Processor Configuration

```python
config = {
    'model': 'voyage-context-3',              # Embedding model
    'dimension': 1024,                        # Embedding dimension
    'cost_per_1k_tokens': 0.0001,            # Voyage pricing
    'batch_size': 25,                         # Embedding batch size
    'max_concurrent': 5,                      # Concurrent requests
    'chunking': {
        'chunk_size': 2000,                   # Target chunk size (tokens)
        'overlap_size': 200,                  # Chunk overlap (tokens)
        'preserve_sentences': True,           # Sentence boundary preservation
        'legal_boundary_markers': [           # Legal section markers
            "HELD:", "REASONING:", "I.", "II."
        ]
    },
    'cost_limits': {
        'max_cost_per_document': 0.10,        # Document cost limit
        'daily_budget': 100.0,                # Daily budget
        'hourly_budget': 20.0                 # Hourly budget
    },
    'optimization': {
        'enable_caching': True,               # Enable embedding cache
        'cache_ttl_hours': 24,                # Cache time-to-live
        'enable_legal_preprocessing': True,    # Legal text preprocessing
        'focus_on_entities': True             # Prioritize entity embeddings
    }
}
```

### Legal Domain Configuration

```python
# Practice area keywords for classification
practice_area_keywords = {
    'personal_injury': [
        'negligence', 'malpractice', 'accident', 'injury', 'damages',
        'liability', 'tort', 'harm', 'causation', 'standard of care'
    ],
    'criminal_defense': [
        'criminal', 'felony', 'misdemeanor', 'prosecution', 'defense',
        'conviction', 'sentence', 'plea', 'trial', 'evidence'
    ],
    # ... additional practice areas
}

# Legal terminology mappings
terminology_mappings = {
    'synonyms': {
        'plaintiff': ['plaintiff', 'petitioner', 'claimant'],
        'defendant': ['defendant', 'respondent', 'appellee']
    },
    'abbreviations': {
        'S.Ct.': 'Supreme Court',
        'F.3d': 'Federal Reporter Third Series'
    }
}
```

## 📚 API Reference

### EnhancedVoyageGraphRAGProcessor

#### Methods

- `process_graphrag_document(document_text, case_metadata, practice_area)` - Process complete legal document
- `generate_embeddings(texts, embedding_type)` - Generate Voyage-context-3 embeddings
- `chunk_legal_document(text, metadata)` - Create 2k token chunks with legal optimization
- `hybrid_search(query, jurisdiction, practice_area, search_types, top_k)` - Perform hybrid search
- `get_processing_statistics()` - Get comprehensive processing statistics

### PineconeConnector

#### Enhanced Methods

- `store_document_chunk_embeddings(chunks, jurisdiction, practice_area, global_uid)` - Store chunk embeddings
- `store_entity_embeddings(entities, jurisdiction, practice_area, global_uid)` - Store entity embeddings
- `store_relationship_embeddings(relationships, jurisdiction, practice_area, global_uid)` - Store relationship embeddings
- `get_graphrag_namespace(jurisdiction, practice_area, embedding_type)` - Generate GraphRAG namespace
- `hybrid_search(query_vector, jurisdiction, practice_area, search_types, top_k)` - Multi-type search
- `get_embedding_statistics(jurisdiction, practice_area)` - Get namespace statistics

### LegalDomainOptimizer

#### Methods

- `normalize_legal_text(text)` - Normalize legal terminology
- `enhance_legal_context(text, practice_area)` - Add legal context
- `extract_citations(text)` - Extract legal citations
- `extract_judicial_entities(text)` - Extract judges and courts
- `classify_practice_area(text)` - Classify practice area
- `calculate_legal_content_quality(text)` - Calculate quality metrics

## 🔍 Troubleshooting

### Common Issues

1. **Embedding API Errors**:
   - Check Voyage API key configuration
   - Verify API rate limits and quotas
   - Monitor cost limits and budgets

2. **Pinecone Connection Issues**:
   - Verify Pinecone API key and environment
   - Check index existence and dimensions
   - Validate namespace naming conventions

3. **GraphRAG Processing Failures**:
   - Ensure Neo4j connection is available
   - Check GraphRAG package installation
   - Verify document text quality and length

4. **Cost Overruns**:
   - Review cost limits configuration
   - Enable embedding caching
   - Optimize chunk sizes and batch processing

### Debug Mode

Enable debug logging for detailed troubleshooting:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Processor will now output detailed debug information
processor = EnhancedVoyageGraphRAGProcessor(config)
```

## 📊 Performance Benchmarks

### Typical Performance (per document)

- **Processing Time**: 5-15 seconds
- **Embedding Cost**: $0.001-$0.010
- **Chunks Generated**: 10-50 (depending on document length)
- **Entities Extracted**: 5-25
- **Relationships Found**: 3-15

### Scalability Metrics

- **Documents per Hour**: 200-500 (depending on complexity)
- **Concurrent Processing**: Up to 5 documents simultaneously
- **Cost per 1000 Documents**: $1-10 (depending on length and complexity)
- **Storage Growth**: ~1KB metadata + embeddings per chunk

## 🚀 Production Deployment

### Recommended Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Document      │    │   Enhanced       │    │   Storage       │
│   Ingestion     │───▶│   GraphRAG       │───▶│   Backends      │
│   Pipeline      │    │   Processor      │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                          │
                              ▼                          ▼
                       ┌─────────────┐         ┌─────────────────┐
                       │  Monitoring │         │  Search API     │
                       │  & Alerts   │         │  Interface      │
                       └─────────────┘         └─────────────────┘
```

### Monitoring & Alerting

Set up monitoring for:
- **Cost Tracking**: Daily/hourly budget utilization
- **Performance Metrics**: Processing time and throughput
- **Error Rates**: API failures and retry counts
- **Storage Growth**: Vector count and namespace utilization
- **Search Performance**: Query latency and result quality

### Scaling Considerations

- **Horizontal Scaling**: Deploy multiple processor instances
- **Load Balancing**: Distribute documents across processors
- **Cache Optimization**: Shared embedding cache across instances
- **Database Sharding**: Partition by jurisdiction or practice area
- **CDN Integration**: Cache frequently accessed embeddings

## 📝 Changelog

### Version 1.0.0 (2025-08-01)

**Initial Release**:
- Enhanced Voyage-context-3 integration with GraphRAG
- 2k token chunking with legal optimization
- Multi-type embedding storage (chunks, entities, relationships)
- Practice-area specific Pinecone namespaces
- Hybrid search capabilities
- Legal domain optimizations
- Comprehensive cost monitoring and optimization
- Production-ready testing and demonstration scripts

## 🤝 Contributing

### Development Setup

1. Clone the repository
2. Install development dependencies: `pip install -r requirements-dev.txt`
3. Set up environment variables
4. Run tests: `python test_enhanced_voyage_graphrag_integration.py`

### Code Standards

- Follow PEP 8 style guidelines
- Add comprehensive docstrings
- Include unit tests for new features
- Update documentation for API changes

## 📄 License

This enhanced integration builds upon the existing Texas Laws Personal Injury project and follows the same licensing terms.

## 🆘 Support

For issues and questions:

1. Check the troubleshooting section above
2. Review the test suite and demo scripts
3. Check the logs for detailed error information
4. Ensure all environment variables are properly configured

---

**Enhanced Voyage-context-3 GraphRAG Integration** - Powering intelligent legal document processing with state-of-the-art embeddings and knowledge graphs.