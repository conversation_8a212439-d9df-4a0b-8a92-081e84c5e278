# 🌐 CourtListener API Cases - Storage & Access Guide
## Texas Laws Personal Injury Project

## 📊 **Current API-Fetched Cases Overview**

### **Database Status**
- **500 API-fetched cases** stored (source: "courtlistener")
- **100% GCS coverage** - all cases have full GCS paths
- **Multi-file storage** - opinions, clusters, dockets, parties
- **Recent data** - fetched August 2-4, 2025

### **Storage Architecture**
```
CourtListener API → Client → GCS Storage → Database Metadata
       ↓              ↓           ↓              ↓
Recent cases    Full content   JSON.gz files   Cross-tracking
Live data       + metadata     Compressed      gcs_path links
```

---

## 🗂️ **GCS Storage Structure for API Cases**

### **File Organization**
```
gs://texas-laws-personalinjury/
├── FED/                          # Federal cases from API
│   ├── opinions/                 # Full opinion text + metadata
│   │   ├── 11113204.json.gz     # Complete opinion data
│   │   ├── 11113205.json.gz     # (3.6KB compressed → 15.8KB raw)
│   │   └── ...
│   ├── clusters/                 # Case cluster metadata
│   │   ├── 10646617.json.gz     # Case name, court, judges
│   │   ├── 10646618.json.gz     # Filing dates, citations
│   │   └── ...
│   ├── dockets/                  # Docket information
│   │   ├── 70633610.json.gz     # Court proceedings
│   │   └── ...
│   └── parties/                  # Party information (when available)
└── TX/                           # Texas cases (from CSV bulk data)
    └── opinions/                 # Bulk-processed opinions
```

### **File Content Structure**

#### **Opinion Files** (`FED/opinions/{id}.json.gz`)
```json
{
  "id": 11113204,
  "cluster_id": 10646617,
  "plain_text": "FULL OPINION TEXT HERE...",
  "html_with_citations": "HTML WITH LEGAL CITATIONS...",
  "author_str": "Judge Name",
  "date_created": "2025-08-02T02:40:12.647204-07:00",
  "type": "100trialcourt",
  "download_url": null,
  "resource_uri": "https://www.courtlistener.com/api/rest/v4/opinions/11113204/"
}
```

#### **Cluster Files** (`FED/clusters/{id}.json.gz`)
```json
{
  "id": 10646617,
  "case_name": "Fortson v. Fender",
  "date_filed": "2025-08-01",
  "court": "Northern District of Ohio",
  "judges": ["Judge Name"],
  "citations": [...],
  "docket_id": 70633610
}
```

---

## 🔍 **How to Access Full Text from API Cases**

### **Method 1: Direct GCS Access (Recommended)**

```python
from google.cloud import storage
import json
import gzip
from dotenv import load_dotenv

class CourtListenerAPIExtractor:
    def __init__(self):
        load_dotenv()
        self.client = storage.Client()
        self.bucket = self.client.bucket('texas-laws-personalinjury')
    
    def get_api_case_opinion(self, case_id: str) -> dict:
        """Get full opinion from API-fetched case."""
        gcs_path = f"FED/opinions/{case_id}.json.gz"
        
        try:
            blob = self.bucket.blob(gcs_path)
            compressed_data = blob.download_as_bytes()
            content = gzip.decompress(compressed_data).decode('utf-8')
            return json.loads(content)
        except Exception as e:
            print(f"Error retrieving API case {case_id}: {e}")
            return None
    
    def get_full_text(self, case_id: str) -> str:
        """Extract full text from API case."""
        opinion_data = self.get_api_case_opinion(case_id)
        if not opinion_data:
            return ""
        
        # Priority order for text extraction
        text_fields = [
            'plain_text',           # Best for analysis
            'html_with_citations',  # Best for research
            'html',                 # Basic HTML
            'html_lawbox'           # Alternative source
        ]
        
        for field in text_fields:
            text = opinion_data.get(field, '')
            if text and len(text.strip()) > 100:
                return text.strip()
        
        return ""
    
    def get_case_metadata(self, case_id: str) -> dict:
        """Get case metadata from cluster file."""
        # First get opinion to find cluster_id
        opinion = self.get_api_case_opinion(case_id)
        if not opinion:
            return {}
        
        cluster_id = opinion.get('cluster_id')
        if not cluster_id:
            return {}
        
        # Get cluster data
        cluster_path = f"FED/clusters/{cluster_id}.json.gz"
        try:
            blob = self.bucket.blob(cluster_path)
            compressed_data = blob.download_as_bytes()
            content = gzip.decompress(compressed_data).decode('utf-8')
            return json.loads(content)
        except Exception as e:
            print(f"Error retrieving cluster {cluster_id}: {e}")
            return {}

# Usage example
extractor = CourtListenerAPIExtractor()

# Get full opinion text
full_text = extractor.get_full_text('11113204')
print(f"Opinion length: {len(full_text)} characters")

# Get case metadata
metadata = extractor.get_case_metadata('11113204')
print(f"Case: {metadata.get('case_name', 'Unknown')}")
```

### **Method 2: Database Query + GCS Retrieval**

```python
from supabase import create_client
import os

# Initialize Supabase
supabase = create_client(
    os.getenv('SUPABASE_URL'),
    os.getenv('SUPABASE_SERVICE_ROLE_KEY')
)

def get_api_cases_with_text(limit=10):
    """Get API-fetched cases with their full text."""
    # Query database for API cases
    response = supabase.table('cases').select(
        'id, case_name, gcs_path, gcs_cluster_path, created_at'
    ).eq('source', 'courtlistener').limit(limit).execute()
    
    cases_with_text = []
    extractor = CourtListenerAPIExtractor()
    
    for case in response.data:
        case_id = case['id']
        full_text = extractor.get_full_text(case_id)
        
        cases_with_text.append({
            'id': case_id,
            'case_name': case['case_name'],
            'full_text': full_text,
            'text_length': len(full_text),
            'gcs_path': case['gcs_path'],
            'created_at': case['created_at']
        })
    
    return cases_with_text

# Get recent API cases with full text
recent_cases = get_api_cases_with_text(5)
for case in recent_cases:
    print(f"{case['id']}: {case['text_length']:,} chars")
```

### **Method 3: Batch Processing API Cases**

```python
def process_all_api_cases():
    """Process all API-fetched cases for analysis."""
    # Get all API case IDs from database
    response = supabase.table('cases').select('id').eq('source', 'courtlistener').execute()
    case_ids = [case['id'] for case in response.data]
    
    print(f"Processing {len(case_ids)} API-fetched cases...")
    
    extractor = CourtListenerAPIExtractor()
    results = []
    
    for case_id in case_ids:
        try:
            # Get full text
            full_text = extractor.get_full_text(case_id)
            
            # Get metadata
            metadata = extractor.get_case_metadata(case_id)
            
            results.append({
                'case_id': case_id,
                'case_name': metadata.get('case_name', ''),
                'court': metadata.get('court', ''),
                'date_filed': metadata.get('date_filed', ''),
                'full_text': full_text,
                'text_length': len(full_text)
            })
            
        except Exception as e:
            print(f"Error processing case {case_id}: {e}")
    
    return results

# Process all API cases
all_api_cases = process_all_api_cases()
print(f"Successfully processed {len(all_api_cases)} API cases")
```

---

## 📋 **Text Quality Analysis**

### **API Cases vs Bulk CSV Cases**

| **Aspect** | **API Cases** | **Bulk CSV Cases** |
|------------|---------------|-------------------|
| **Source** | Live CourtListener API | 49.7GB CSV file |
| **Count** | 500 cases | 273K+ cases |
| **Recency** | Very recent (Aug 2025) | Historical data |
| **Text Quality** | ✅ High (5.2KB avg) | ✅ High (2.6KB avg) |
| **Metadata** | ✅ Rich (clusters, dockets) | ✅ Basic (case info) |
| **Storage** | Multi-file JSON.gz | Single JSON.gz |

### **Text Field Availability**
```python
# API cases typically have:
{
    'plain_text': '5,240 chars',           # ✅ Clean text
    'html_with_citations': '8,548 chars',  # ✅ Rich HTML
    'html': '0 chars',                     # ❌ Often empty
    'html_lawbox': '0 chars'               # ❌ Often empty
}
```

---

## 🚀 **Integration with Existing System**

### **Unified Opinion Extractor**

```python
# Enhanced version of opinion_extractor.py
class UnifiedOpinionExtractor(TexasOpinionExtractor):
    """Extract opinions from both bulk CSV and API sources."""
    
    def get_full_opinion_unified(self, case_id: str) -> dict:
        """Get opinion from any source (API or CSV)."""
        # Try API cases first (FED jurisdiction)
        api_opinion = self.get_api_case_opinion(case_id)
        if api_opinion:
            text = self._extract_best_text_from_api(api_opinion)
            return {
                'case_id': case_id,
                'text': text,
                'source': 'courtlistener_api',
                'source_field': 'plain_text',
                'metadata': api_opinion
            }
        
        # Fallback to bulk CSV cases (TX jurisdiction)
        return self.get_full_opinion(case_id, 'bulk_csv/opinions-2025-07-02.csv.bz2')
    
    def _extract_best_text_from_api(self, api_data: dict) -> str:
        """Extract best text from API response."""
        for field in ['plain_text', 'html_with_citations', 'html']:
            text = api_data.get(field, '')
            if text and len(text.strip()) > 100:
                return text.strip()
        return ""
```

---

## 🎯 **Current Status & Next Steps**

### **✅ What's Available Now**
- **500 API-fetched cases** with full text
- **Multi-file storage** (opinions + clusters + dockets)
- **Rich metadata** including case names, courts, judges
- **Recent data** (August 2025)
- **High-quality text** (5.2KB average per opinion)

### **🔄 How API Fetching Works**
1. **CourtListener client** queries API for recent cases
2. **Full case data** downloaded (opinions + metadata)
3. **Multi-file storage** in GCS (compressed JSON)
4. **Database cross-tracking** with gcs_path links
5. **Ready for analysis** immediately

### **📈 Scaling API Ingestion**
```python
# The system can fetch more recent cases using:
from courtlistener.core.fetch_courtlistener_cases import CourtListenerClient

client = CourtListenerClient()
recent_cases = client.get_recent_opinions(
    jurisdiction='tx',  # or 'fed'
    days_back=7,        # Last 7 days
    max_results=100     # Up to 100 cases
)
```

## 🏆 **Summary**

**The CourtListener API integration is fully operational with:**

✅ **500 recent cases** stored with full text  
✅ **Rich metadata** (clusters, dockets, parties)  
✅ **High-quality text** (plain_text + html_with_citations)  
✅ **Efficient storage** (compressed JSON in GCS)  
✅ **Database integration** (cross-tracking with gcs_path)  
✅ **Ready-to-use tools** (provided code examples)  

**You can immediately access full text from any of the 500 API-fetched cases using the provided extraction methods!** 🚀
