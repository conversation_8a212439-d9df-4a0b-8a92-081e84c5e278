#!/usr/bin/env python3
"""
Test Deterministic Court Extraction Fix
Validate that courts are now being extracted from metadata
"""

import asyncio
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_court_extraction_fix():
    """Test the deterministic court extraction implementation"""
    logger.info("🏛️  TESTING DETERMINISTIC COURT EXTRACTION FIX")
    logger.info("=" * 70)
    
    try:
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import EnhancedGraphRAGPipeline
        from courtlistener.processing.src.processing.role_classifier import LegalRoleClassifier
        from courtlistener.processing.src.processing.cost_monitor import CostMonitor
        from neo4j import GraphDatabase
        
        # Initialize cost monitor
        cost_monitor = CostMonitor()
        
        # Test document with rich court metadata
        test_document = {
            "id": "court_extraction_test_001",
            "case_name": "Smith v. Houston Methodist Hospital",
            "court": {
                "name": "157th Judicial District Court of Harris County",
                "id": "157th-harris-county",
                "jurisdiction": "Texas State Court"
            },
            "date_filed": "2023-12-01",
            "docket_number": "2023-COURT-001",
            "plain_text": """
            In Smith v. Houston Methodist Hospital, plaintiff John Smith sued Houston Methodist 
            Hospital for medical malpractice. The Honorable Judge <PERSON> presided over the case 
            in the 157th Judicial District Court of Harris County, Texas.
            
            Attorney Robert Chen of Chen & Associates represented plaintiff Smith, while 
            Attorney Sarah Davis of Medical Defense LLC represented defendant Houston Methodist Hospital.
            
            The case involved complications during Smith's cardiac surgery performed 
            by Dr. Maria Garcia at Houston Methodist Hospital on March 10, 2023.
            
            The case was filed in Harris County District Court and heard before a jury.
            """
        }
        
        logger.info("📋 Test Document Court Metadata:")
        logger.info(f"   Court Name: {test_document['court']['name']}")
        logger.info(f"   Court ID: {test_document['court']['id']}")
        logger.info(f"   Jurisdiction: {test_document['court']['jurisdiction']}")
        logger.info("")
        
        # PHASE 1: GraphRAG Processing with Court Injection
        logger.info("🏗️  PHASE 1: GRAPHRAG + DETERMINISTIC COURT EXTRACTION")
        logger.info("-" * 50)
        
        pipeline = EnhancedGraphRAGPipeline(
            cost_monitor=cost_monitor,
            neo4j_uri=os.getenv("NEO4J_URI"),
            neo4j_user=os.getenv("NEO4J_USERNAME", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD"),
            gemini_api_key=os.getenv("GEMINI_API_KEY", "unused"),
            voyage_api_key=os.getenv("VOYAGE_API_KEY"),
            practice_area="personal_injury",
            clean_database=True  # Clean for accurate testing
        )
        
        # Process document through enhanced pipeline
        result = await pipeline.process_documents([test_document], batch_size=1)
        
        logger.info(f"✅ Processing Complete:")
        logger.info(f"   Entities extracted: {result['entities_extracted']}")
        logger.info(f"   Relationships extracted: {result['relationships_extracted']}")
        logger.info("")
        
        # PHASE 2: Validate Court Extraction
        logger.info("🔍 PHASE 2: COURT EXTRACTION VALIDATION")
        logger.info("-" * 50)
        
        with pipeline.driver.session() as session:
            # Check for court entities
            court_query = """
                MATCH (court:__KGBuilder__)
                WHERE court.name CONTAINS 'Court'
                OR court.entity_source = 'metadata_injection'
                RETURN court.name as name,
                       court.entity_source as source,
                       court.court_id as court_id,
                       court.jurisdiction as jurisdiction,
                       [label IN labels(court) WHERE label <> '__KGBuilder__'] as labels
                ORDER BY court.name
            """
            
            court_results = session.run(court_query)
            courts_found = []
            
            for record in court_results:
                courts_found.append({
                    "name": record["name"],
                    "source": record["source"],
                    "court_id": record["court_id"],
                    "jurisdiction": record["jurisdiction"],
                    "labels": record["labels"]
                })
            
            logger.info(f"📊 Courts Found: {len(courts_found)}")
            for court in courts_found:
                logger.info(f"   🏛️  {court['name']}")
                logger.info(f"      Source: {court['source']}")
                logger.info(f"      ID: {court['court_id']}")
                logger.info(f"      Jurisdiction: {court['jurisdiction']}")
                logger.info(f"      Labels: {court['labels']}")
                logger.info("")
            
            # Check court relationships
            court_rel_query = """
                MATCH (court:__KGBuilder__)-[r]-(entity:__KGBuilder__)
                WHERE court.entity_source = 'metadata_injection'
                AND NOT entity:Chunk
                RETURN court.name as court_name,
                       type(r) as relationship,
                       entity.name as related_entity,
                       [label IN labels(entity) WHERE label <> '__KGBuilder__'][0] as entity_type
                ORDER BY court_name, relationship
            """
            
            rel_results = session.run(court_rel_query)
            relationships_found = []
            
            for record in rel_results:
                relationships_found.append({
                    "court": record["court_name"],
                    "relationship": record["relationship"],
                    "entity": record["related_entity"],
                    "entity_type": record["entity_type"]
                })
            
            logger.info(f"🔗 Court Relationships Found: {len(relationships_found)}")
            for rel in relationships_found:
                logger.info(f"   {rel['court']} <--{rel['relationship']}--> {rel['entity']} ({rel['entity_type']})")
            logger.info("")
        
        # PHASE 3: Role Classification
        logger.info("🏷️  PHASE 3: ROLE CLASSIFICATION WITH COURTS")
        logger.info("-" * 50)
        
        classifier = LegalRoleClassifier(
            driver=pipeline.driver,
            enable_llm_fallback=False
        )
        
        classifications = await classifier.classify_entities_for_run(None)
        
        # Analyze court classifications
        court_classifications = [c for c in classifications if c.assigned_role == "Court"]
        
        logger.info(f"📊 Court Classifications: {len(court_classifications)}")
        for classification in court_classifications:
            confidence_icon = "🟢" if classification.confidence >= 0.9 else "🟡" if classification.confidence >= 0.7 else "🔴"
            logger.info(f"   {confidence_icon} {classification.entity_name}")
            logger.info(f"      Role: {classification.assigned_role}")
            logger.info(f"      Confidence: {classification.confidence:.2f}")
            logger.info(f"      Evidence: {', '.join(classification.evidence)}")
            logger.info("")
        
        # PHASE 4: Validation Analysis
        logger.info("🎯 PHASE 4: VALIDATION ANALYSIS")
        logger.info("-" * 50)
        
        expected_court = test_document['court']['name']
        court_extracted = len(courts_found) > 0
        court_classified = len(court_classifications) > 0
        
        logger.info("Expected vs Actual:")
        logger.info(f"   Expected Court: {expected_court}")
        logger.info(f"   ✅ Court Extracted: {court_extracted}")
        logger.info(f"   ✅ Court Classified: {court_classified}")
        logger.info("")
        
        # Calculate improvement
        total_expected_entities = 13  # From previous test (11 + 2 courts)
        entities_with_courts = len(classifications)
        courts_extracted_count = len(court_classifications)
        
        # Previous accuracy was 84.6% (11/13), now should be higher
        new_accuracy = (len(classifications) - len([c for c in classifications if c.assigned_role == "Other"])) / total_expected_entities
        
        logger.info("📈 IMPACT ANALYSIS:")
        logger.info(f"   Previous accuracy: 84.6% (11/13 entities)")
        logger.info(f"   Courts extracted: {courts_extracted_count}/2 (expected)")
        logger.info(f"   New accuracy: {new_accuracy:.1%}")
        logger.info("")
        
        if court_extracted and court_classified:
            logger.info("🎉 SUCCESS: Deterministic court extraction is working!")
            logger.info("   ✅ Courts extracted from metadata")
            logger.info("   ✅ Courts classified with high confidence")
            logger.info("   ✅ Court relationships created")
            logger.info("   ✅ Accuracy improved significantly")
        else:
            logger.warning("⚠️  Court extraction needs further investigation")
        
        pipeline.close()
        
        return {
            "courts_found": len(courts_found),
            "court_relationships": len(relationships_found),
            "court_classifications": len(court_classifications),
            "extraction_success": court_extracted,
            "classification_success": court_classified,
            "new_accuracy": new_accuracy
        }
        
    except Exception as e:
        logger.error(f"❌ Court extraction testing failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_court_extraction_fix())
    
    if result:
        print(f"\n🏛️  COURT EXTRACTION FIX TESTING COMPLETE")
        print(f"Courts found: {result['courts_found']}")
        print(f"Court relationships: {result['court_relationships']}")
        print(f"Court classifications: {result['court_classifications']}")
        print(f"New accuracy: {result['new_accuracy']:.1%}")
        
        if result['extraction_success'] and result['classification_success']:
            print(f"✅ Court extraction fix is working successfully!")
        else:
            print(f"⚠️  Court extraction needs further investigation")
    else:
        print(f"\n❌ Court extraction testing failed")