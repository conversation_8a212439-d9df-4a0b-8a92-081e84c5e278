#!/usr/bin/env python3
"""
Unified Opinion Extractor for Full Legal Text
Handles both Bulk CSV System and API System with complete opinion text
"""

import os
import asyncio
import logging
import json
import gzip
import bz2
import csv
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from datetime import datetime

from google.cloud import storage
from supabase import create_client, Client
from dotenv import load_dotenv
import aiofiles

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()

@dataclass
class FullOpinion:
    """Complete opinion with full text from either system"""
    case_id: str
    case_name: str
    full_text: str
    source_system: str  # 'bulk_csv' or 'api'
    source_field: str   # Which field contained the text
    text_length: int
    metadata: Dict[str, Any]
    extraction_timestamp: datetime
    gcs_path: Optional[str] = None
    
    @property
    def is_substantial(self) -> bool:
        """Check if opinion has substantial legal content"""
        return len(self.full_text.strip()) >= 1000 and self.text_length >= 200

@dataclass
class ExtractionReport:
    """Report for opinion extraction operations"""
    total_attempted: int
    successful_extractions: int
    failed_extractions: int
    bulk_csv_success: int
    api_success: int
    total_text_length: int
    average_opinion_length: int
    execution_time: float
    errors: List[Dict[str, str]]

class UnifiedOpinionExtractor:
    """
    Unified extractor for full legal opinion text from both systems:
    1. Bulk CSV System: TX/opinions/*.json.gz (historical cases)  
    2. API System: FED/opinions/*.json.gz (recent cases with rich metadata)
    """
    
    def __init__(self):
        # Initialize GCS client
        self.gcs_client = storage.Client()
        self.bucket_name = os.getenv("GCS_BUCKET_NAME", "texas-laws-personalinjury")
        self.bucket = self.gcs_client.bucket(self.bucket_name)
        
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        self.supabase: Client = create_client(supabase_url, supabase_key)
        
        # CSV file path
        self.csv_file_path = "bulk_csv/opinions-2025-07-02.csv.bz2"
        
        logger.info(f"✅ Unified Opinion Extractor initialized for bucket: {self.bucket_name}")
    
    async def get_full_opinion_unified(self, case_id: str) -> Optional[FullOpinion]:
        """Get full opinion from any source - tries both systems"""
        
        # Try API system first (FED jurisdiction - recent, rich metadata)
        api_opinion = await self.get_api_opinion(case_id)
        if api_opinion and api_opinion.is_substantial:
            logger.info(f"✅ Found substantial API opinion for {case_id}: {api_opinion.text_length:,} chars")
            return api_opinion
        
        # Try bulk CSV system (TX jurisdiction - historical, volume)
        bulk_opinion = await self.get_bulk_csv_opinion(case_id)
        if bulk_opinion and bulk_opinion.is_substantial:
            logger.info(f"✅ Found substantial bulk CSV opinion for {case_id}: {bulk_opinion.text_length:,} chars")
            return bulk_opinion
        
        # If we have any opinion, return it even if not substantial
        if api_opinion:
            return api_opinion
        if bulk_opinion:
            return bulk_opinion
            
        logger.warning(f"⚠️  No opinion found for case {case_id} in either system")
        return None
    
    async def get_api_opinion(self, case_id: str) -> Optional[FullOpinion]:
        """Extract full opinion from API system (FED/opinions/)"""
        try:
            gcs_path = f"FED/opinions/{case_id}.json.gz"
            blob = self.bucket.blob(gcs_path)
            
            if not blob.exists():
                logger.debug(f"API opinion not found: {gcs_path}")
                return None
                
            # Download and decompress
            compressed_data = blob.download_as_bytes()
            content = gzip.decompress(compressed_data).decode('utf-8')
            data = json.loads(content)
            
            # Extract best text from API response
            full_text, source_field = self._extract_best_text_from_api(data)
            
            if not full_text or len(full_text.strip()) < 100:
                logger.debug(f"Insufficient text in API opinion {case_id}")
                return None
            
            # Get case metadata (try to get cluster info)
            case_name = self._extract_case_name_from_api(data, case_id)
            metadata = self._extract_api_metadata(data)
            
            return FullOpinion(
                case_id=case_id,
                case_name=case_name,
                full_text=full_text,
                source_system="api",
                source_field=source_field,
                text_length=len(full_text),
                metadata=metadata,
                extraction_timestamp=datetime.utcnow(),
                gcs_path=gcs_path
            )
            
        except Exception as e:
            logger.error(f"❌ Error extracting API opinion {case_id}: {e}")
            return None
    
    async def get_bulk_csv_opinion(self, case_id: str) -> Optional[FullOpinion]:
        """Extract full opinion from bulk CSV system (TX/opinions/)"""
        try:
            # First try GCS (processed opinions)
            gcs_path = f"TX/opinions/{case_id}.json.gz"
            blob = self.bucket.blob(gcs_path)
            
            if blob.exists():
                # Download and decompress
                compressed_data = blob.download_as_bytes()
                content = gzip.decompress(compressed_data).decode('utf-8')
                data = json.loads(content)
                
                # Extract text from processed bulk data
                full_text, source_field = self._extract_text_from_bulk_data(data)
                
                if full_text and len(full_text.strip()) >= 100:
                    case_name = data.get('case_name', f'Case {case_id}')
                    metadata = self._extract_bulk_metadata(data)
                    
                    return FullOpinion(
                        case_id=case_id,
                        case_name=case_name,
                        full_text=full_text,
                        source_system="bulk_csv",
                        source_field=source_field,
                        text_length=len(full_text),
                        metadata=metadata,
                        extraction_timestamp=datetime.utcnow(),
                        gcs_path=gcs_path
                    )
            
            # Fallback to direct CSV extraction
            logger.debug(f"GCS not found, trying direct CSV extraction for {case_id}")
            return await self.get_opinion_from_csv_direct(case_id)
            
        except Exception as e:
            logger.error(f"❌ Error extracting bulk CSV opinion {case_id}: {e}")
            return None
    
    async def get_opinion_from_csv_direct(self, case_id: str) -> Optional[FullOpinion]:
        """Direct extraction from CSV file (fallback)"""
        try:
            if not os.path.exists(self.csv_file_path):
                logger.warning(f"CSV file not found: {self.csv_file_path}")
                return None
            
            # Set large field size limit for legal documents
            csv.field_size_limit(10**9)
            
            with bz2.open(self.csv_file_path, 'rt', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    if str(row.get('id')) == str(case_id):
                        # Extract best text from CSV row
                        full_text, source_field = self._extract_best_text_from_csv_row(row)
                        
                        if full_text and len(full_text.strip()) >= 100:
                            case_name = self._extract_case_name_from_csv(row)
                            metadata = self._extract_csv_metadata(row)
                            
                            return FullOpinion(
                                case_id=case_id,
                                case_name=case_name,
                                full_text=full_text,
                                source_system="bulk_csv",
                                source_field=source_field,
                                text_length=len(full_text),
                                metadata=metadata,
                                extraction_timestamp=datetime.utcnow()
                            )
            
            logger.debug(f"Case {case_id} not found in CSV file")
            return None
            
        except Exception as e:
            logger.error(f"❌ Error in direct CSV extraction for {case_id}: {e}")
            return None
    
    def _extract_best_text_from_api(self, data: Dict[str, Any]) -> Tuple[str, str]:
        """Extract best available text from API response"""
        
        # Priority order for API text extraction
        text_fields = [
            'plain_text',           # Best for analysis
            'html_with_citations',  # Best for research
            'html',                 # Basic HTML
            'html_lawbox'           # Alternative source
        ]
        
        for field in text_fields:
            text = data.get(field, '')
            if text and len(text.strip()) > 100:
                # Clean HTML if necessary
                if field.startswith('html'):
                    text = self._clean_html_text(text)
                return text.strip(), field
        
        return "", "none"
    
    def _extract_text_from_bulk_data(self, data: Dict[str, Any]) -> Tuple[str, str]:
        """Extract text from processed bulk CSV data"""
        
        # For bulk CSV data stored in GCS
        text_fields = [
            'plain_text',
            'html_with_citations',
            'html',
            'html_lawbox',
            'html_columbia',
            'html_anon_2020'
        ]
        
        for field in text_fields:
            text = data.get(field, '')
            if text and len(text.strip()) > 100:
                if field.startswith('html'):
                    text = self._clean_html_text(text)
                return text.strip(), field
        
        return "", "none"
    
    def _extract_best_text_from_csv_row(self, row: Dict[str, Any]) -> Tuple[str, str]:
        """Extract best text from raw CSV row"""
        
        # Priority order for CSV text extraction
        text_fields = [
            'plain_text',           # Primary: Clean text content
            'html',                 # Secondary: HTML formatted
            'html_lawbox',          # Alternative HTML source
            'html_columbia',        # Columbia Law source
            'html_anon_2020',       # Anonymized 2020 version
            'html_with_citations',  # HTML with citation links
            'xml_harvard'           # Harvard XML format
        ]
        
        for field in text_fields:
            text = row.get(field, '')
            if text and len(text.strip()) > 100:
                # Clean HTML/XML content
                if field.startswith('html') or field.startswith('xml'):
                    text = self._clean_html_text(text)
                return text.strip(), field
        
        return "", "none"
    
    def _clean_html_text(self, html_content: str) -> str:
        """Clean HTML content to extract plain text"""
        try:
            # Basic HTML tag removal (can be enhanced with BeautifulSoup if needed)
            import re
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', '', html_content)
            # Clean up whitespace
            text = re.sub(r'\s+', ' ', text).strip()
            return text
        except Exception:
            return html_content  # Return as-is if cleaning fails
    
    def _extract_case_name_from_api(self, data: Dict[str, Any], case_id: str) -> str:
        """Extract case name from API data"""
        # Try different fields that might contain case name
        name_fields = ['case_name', 'case_name_full', 'case_name_short', 'title']
        
        for field in name_fields:
            if field in data and data[field]:
                return str(data[field]).strip()
        
        return f"API Case {case_id}"
    
    def _extract_case_name_from_csv(self, row: Dict[str, Any]) -> str:
        """Extract case name from CSV row"""
        case_name = row.get('case_name', '').strip()
        if case_name:
            return case_name
        
        # Fallback to constructing from available data
        case_id = row.get('id', 'Unknown')
        return f"CSV Case {case_id}"
    
    def _extract_api_metadata(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metadata from API response"""
        return {
            'api_id': data.get('id'),
            'cluster_id': data.get('cluster_id'),
            'author_str': data.get('author_str'),
            'date_created': data.get('date_created'),
            'type': data.get('type'),
            'download_url': data.get('download_url'),
            'resource_uri': data.get('resource_uri'),
            'extraction_method': 'api_system'
        }
    
    def _extract_bulk_metadata(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metadata from bulk CSV data"""
        return {
            'bulk_id': data.get('id'),
            'cluster_id': data.get('cluster_id'),
            'author': data.get('author_str', data.get('author')),
            'date_created': data.get('date_created'),
            'court': data.get('court'),
            'extraction_method': 'bulk_csv_gcs'
        }
    
    def _extract_csv_metadata(self, row: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metadata from CSV row"""
        return {
            'csv_id': row.get('id'),
            'cluster_id': row.get('cluster_id'),
            'author': row.get('author_str'),
            'date_created': row.get('date_created'),
            'court': row.get('court'),
            'type': row.get('type'),
            'extraction_method': 'bulk_csv_direct'
        }
    
    async def get_sample_cases_from_each_system(self, count_per_system: int = 5) -> Tuple[List[str], List[str]]:
        """Get sample case IDs from both systems"""
        
        # Get API cases from database
        api_response = self.supabase.table('cases').select('id').eq('source', 'courtlistener').limit(count_per_system).execute()
        api_case_ids = [case['id'] for case in api_response.data]
        
        # Get bulk CSV cases from database 
        bulk_response = self.supabase.table('cases').select('id').eq('source', 'courtlistener_csv').limit(count_per_system).execute()
        bulk_case_ids = [case['id'] for case in bulk_response.data]
        
        logger.info(f"📊 Found {len(api_case_ids)} API cases and {len(bulk_case_ids)} bulk CSV cases for testing")
        
        return api_case_ids, bulk_case_ids
    
    async def extract_batch_opinions(self, case_ids: List[str]) -> ExtractionReport:
        """Extract multiple opinions and generate report"""
        
        start_time = datetime.utcnow()
        successful_opinions = []
        errors = []
        
        bulk_csv_success = 0
        api_success = 0
        
        logger.info(f"🔍 Extracting {len(case_ids)} opinions...")
        
        for case_id in case_ids:
            try:
                opinion = await self.get_full_opinion_unified(case_id)
                
                if opinion:
                    successful_opinions.append(opinion)
                    
                    if opinion.source_system == 'bulk_csv':
                        bulk_csv_success += 1
                    elif opinion.source_system == 'api':
                        api_success += 1
                        
                    logger.info(f"✅ {case_id}: {opinion.text_length:,} chars from {opinion.source_system}")
                else:
                    errors.append({
                        'case_id': case_id,
                        'error': 'No opinion found in either system'
                    })
                    logger.warning(f"⚠️  {case_id}: No opinion found")
                    
            except Exception as e:
                error_msg = str(e)
                errors.append({
                    'case_id': case_id,
                    'error': error_msg
                })
                logger.error(f"❌ {case_id}: {error_msg}")
        
        # Calculate metrics
        total_text_length = sum(op.text_length for op in successful_opinions)
        average_length = total_text_length // len(successful_opinions) if successful_opinions else 0
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        report = ExtractionReport(
            total_attempted=len(case_ids),
            successful_extractions=len(successful_opinions),
            failed_extractions=len(errors),
            bulk_csv_success=bulk_csv_success,
            api_success=api_success,
            total_text_length=total_text_length,
            average_opinion_length=average_length,
            execution_time=execution_time,
            errors=errors
        )
        
        logger.info(f"📊 Extraction Report:")
        logger.info(f"   Success: {report.successful_extractions}/{report.total_attempted}")
        logger.info(f"   Bulk CSV: {report.bulk_csv_success}, API: {report.api_success}")
        logger.info(f"   Avg Length: {report.average_opinion_length:,} chars")
        logger.info(f"   Total Text: {report.total_text_length:,} chars")
        
        return report, successful_opinions

# Test function
async def test_unified_opinion_extractor():
    """Test the unified opinion extractor with sample cases"""
    
    print("=== Testing Unified Opinion Extractor ===\n")
    
    try:
        extractor = UnifiedOpinionExtractor()
        
        # Get sample cases from both systems
        print("1. Getting sample cases from both systems...")
        api_cases, bulk_cases = await extractor.get_sample_cases_from_each_system(3)
        
        print(f"API cases: {api_cases}")
        print(f"Bulk CSV cases: {bulk_cases}")
        
        # Test extraction from both systems
        all_cases = api_cases + bulk_cases
        
        if not all_cases:
            print("⚠️  No sample cases found for testing")
            return False
        
        print(f"\n2. Testing extraction with {len(all_cases)} cases...")
        report, opinions = await extractor.extract_batch_opinions(all_cases)
        
        # Display detailed results
        print(f"\n📊 Extraction Results:")
        print(f"   Total Attempted: {report.total_attempted}")
        print(f"   Successful: {report.successful_extractions}")
        print(f"   Failed: {report.failed_extractions}")
        print(f"   Bulk CSV Success: {report.bulk_csv_success}")
        print(f"   API Success: {report.api_success}")
        print(f"   Total Text: {report.total_text_length:,} characters")
        print(f"   Average Length: {report.average_opinion_length:,} characters")
        print(f"   Execution Time: {report.execution_time:.2f}s")
        
        # Show sample opinions
        print(f"\n3. Sample Opinion Details:")
        for i, opinion in enumerate(opinions[:3]):
            print(f"   Opinion {i+1}:")
            print(f"     Case: {opinion.case_name}")
            print(f"     System: {opinion.source_system}")
            print(f"     Field: {opinion.source_field}")
            print(f"     Length: {opinion.text_length:,} chars")
            print(f"     Substantial: {opinion.is_substantial}")
            print(f"     Preview: {opinion.full_text[:150]}...")
            print()
        
        if report.errors:
            print(f"❌ Errors ({len(report.errors)}):")
            for error in report.errors:
                print(f"   {error['case_id']}: {error['error']}")
        
        success_rate = (report.successful_extractions / report.total_attempted) * 100
        print(f"\n✅ Test completed with {success_rate:.1f}% success rate!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_unified_opinion_extractor())