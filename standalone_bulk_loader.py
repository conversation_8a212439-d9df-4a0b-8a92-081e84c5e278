#!/usr/bin/env python3
"""
Standalone Texas Bulk Loader
Processes the full 50GB CourtListener opinions CSV with pickle-based Texas filtering.
Bypasses complex imports and focuses on core functionality.
"""

import bz2
import csv
import pickle
import time
import hashlib
import json
import os
import logging
from typing import Set, Dict, Optional
from datetime import datetime
from supabase import create_client, Client

# Set CSV field size limit for large legal documents
csv.field_size_limit(10**9)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StandaloneBulkLoader:
    """Standalone bulk loader for Texas opinions."""
    
    def __init__(self, batch_size: int = 5000):
        self.batch_size = batch_size
        self.texas_cluster_ids: Set[int] = set()
        self.processed_count = 0
        self.duplicate_count = 0
        self.error_count = 0
        self.texas_matches = 0
        self.pi_mm_cases = 0
        
        # Initialize Supabase
        self.supabase = self._init_supabase()
        
        # Load existing content hashes for deduplication
        self.existing_hashes = self._load_existing_hashes()
        
        # Load Texas cluster IDs
        self._load_texas_cluster_pickle()
    
    def _init_supabase(self) -> Client:
        """Initialize Supabase client."""
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        if not supabase_url or not supabase_key:
            raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set")
        
        return create_client(supabase_url, supabase_key)
    
    def _load_existing_hashes(self) -> Set[str]:
        """Load existing content hashes for deduplication."""
        try:
            logger.info("Loading existing content hashes for deduplication...")
            response = self.supabase.table('cases').select('content_hash').execute()
            
            hashes = {row['content_hash'] for row in response.data if row.get('content_hash')}
            logger.info(f"Loaded {len(hashes)} existing content hashes")
            return hashes
            
        except Exception as e:
            logger.warning(f"Failed to load existing hashes: {e}")
            return set()
    
    def _load_texas_cluster_pickle(self) -> None:
        """Load Texas cluster IDs from pickle file."""
        pickle_file = "tex_clusters.p"
        
        try:
            logger.info(f"Loading Texas cluster IDs from {pickle_file}")
            with open(pickle_file, 'rb') as f:
                self.texas_cluster_ids = pickle.load(f)
            
            logger.info(f"Loaded {len(self.texas_cluster_ids)} Texas cluster IDs from pickle")
            
        except Exception as e:
            logger.error(f"Failed to load Texas cluster pickle: {e}")
            raise
    
    def _generate_content_hash(self, text: str) -> str:
        """Generate content hash for deduplication."""
        return hashlib.sha256(text.encode('utf-8')).hexdigest()
    
    def _is_duplicate(self, content_hash: str) -> bool:
        """Check if content hash already exists."""
        return content_hash in self.existing_hashes
    
    def _classify_practice_area(self, plain_text: str) -> Dict:
        """Simple practice area classification without LLM."""
        content_lower = plain_text.lower()
        
        # Medical Malpractice keywords
        medmal_keywords = [
            "medical malpractice", "doctor", "physician", "hospital", "surgery", 
            "surgical", "medical negligence", "standard of care", "diagnosis",
            "treatment", "medication", "prescription", "nurse", "nursing"
        ]
        
        # Personal Injury keywords
        pi_keywords = [
            "personal injury", "accident", "negligence", "injury", "damages",
            "tort", "liability", "compensation", "pain and suffering", "wrongful death",
            "car accident", "motor vehicle", "slip and fall", "premises liability"
        ]
        
        medmal_score = sum(1 for keyword in medmal_keywords if keyword in content_lower)
        pi_score = sum(1 for keyword in pi_keywords if keyword in content_lower)
        
        if medmal_score >= 2:
            return {
                "practice_areas": ["Medical Malpractice", "Personal Injury"],
                "primary_practice_area": "Medical Malpractice",
                "confidence": min(0.9, 0.6 + medmal_score * 0.1)
            }
        elif pi_score >= 2:
            return {
                "practice_areas": ["Personal Injury"],
                "primary_practice_area": "Personal Injury",
                "confidence": min(0.9, 0.6 + pi_score * 0.1)
            }
        else:
            return {
                "practice_areas": ["Other"],
                "primary_practice_area": "Other",
                "confidence": 0.5
            }
    
    def _extract_case_data(self, opinion_row: Dict) -> Optional[Dict]:
        """Extract case data from opinion row."""
        try:
            case_id = str(opinion_row.get("id"))
            cluster_id = str(opinion_row.get("cluster_id"))
            plain_text = opinion_row.get("plain_text", "")
            
            if not case_id or not cluster_id:
                return None
            
            # Generate content hash
            content_hash = self._generate_content_hash(plain_text)
            
            # Check for duplicates
            if self._is_duplicate(content_hash):
                return None
            
            # Classify practice area
            classification = self._classify_practice_area(plain_text)
            
            # Extract date
            date_created = opinion_row.get("date_created")
            year_filed = None
            if date_created:
                try:
                    date_obj = datetime.fromisoformat(date_created.replace('Z', '+00:00'))
                    year_filed = date_obj.year
                except:
                    pass
            
            # Build case record
            case_record = {
                "id": case_id,
                "case_name": f"Texas Case {case_id}",
                "case_name_full": f"Texas Case {case_id}",
                "court_id": "texas-verified",
                "jurisdiction": "TX",
                "date_filed": date_created,
                "status": "Published",
                "docket_number": None,
                "nature": None,
                "citation": [],
                "precedential": True,
                "source": "courtlistener_csv",
                "source_id": case_id,
                "cluster_id": cluster_id,
                "docket_id": None,
                "opinion_count": 1,
                "citation_count": 0,
                "document_type": opinion_row.get("type", "opinion"),
                "year_filed": year_filed,
                "source_window": "historical_csv",
                "court_slug": "texas-verified",
                "judge_name": opinion_row.get("author_str", "").strip() if opinion_row.get("author_str") else None,
                "content_hash": content_hash,
                "practice_areas": classification.get("practice_areas", []),
                "primary_practice_area": classification.get("primary_practice_area"),
                "practice_area_confidence": classification.get("confidence", 0.0),
                "practice_area_checked_at": datetime.now().isoformat()
            }
            
            return case_record
            
        except Exception as e:
            logger.error(f"Error extracting case data: {e}")
            return None
    
    def _insert_batch(self, batch_records: list) -> bool:
        """Insert batch of records into Supabase."""
        try:
            if not batch_records:
                return True

            logger.info(f"Inserting batch of {len(batch_records)} records...")

            # Clean records to match database schema
            cleaned_records = []
            for record in batch_records:
                # Remove any fields that don't exist in the database
                cleaned_record = {k: v for k, v in record.items()
                                if k not in ['plain_text']}  # Exclude plain_text
                cleaned_records.append(cleaned_record)

            response = self.supabase.table('cases').insert(cleaned_records).execute()

            # Update existing hashes
            for record in batch_records:
                self.existing_hashes.add(record['content_hash'])

            logger.info(f"Successfully inserted {len(batch_records)} records")
            return True

        except Exception as e:
            logger.error(f"Error inserting batch: {e}")
            return False
    
    def process_csv_file(self, opinions_file: str, limit: Optional[int] = None) -> Dict:
        """Process the full CSV file."""
        logger.info(f"Starting full CSV processing: {opinions_file}")
        
        stats = {
            'rows_read': 0,
            'texas_matches': 0,
            'pi_mm_cases': 0,
            'other_cases': 0,
            'duplicates': 0,
            'errors': 0,
            'batches_processed': 0
        }
        
        start_time = time.time()
        batch_records = []
        
        try:
            with bz2.open(opinions_file, 'rt', encoding='utf-8', newline='') as f:
                # Use PostgreSQL-compatible CSV parameters
                reader = csv.DictReader(f, delimiter=',', quotechar='"',
                                      escapechar='\\', doublequote=False,
                                      strict=True)
                
                for row in reader:
                    stats['rows_read'] += 1
                    
                    if limit and stats['rows_read'] > limit:
                        break
                    
                    try:
                        # Check if this is a Texas case using pickle-based filtering
                        cluster_id = row.get('cluster_id', '')
                        if not (cluster_id and cluster_id.isdigit() and int(cluster_id) in self.texas_cluster_ids):
                            continue
                        
                        stats['texas_matches'] += 1
                        
                        # Extract case data
                        case_record = self._extract_case_data(row)
                        if not case_record:
                            stats['duplicates'] += 1
                            continue
                        
                        # Track practice areas
                        primary_area = case_record.get('primary_practice_area', 'Other')
                        if primary_area in ['Personal Injury', 'Medical Malpractice']:
                            stats['pi_mm_cases'] += 1
                        else:
                            stats['other_cases'] += 1
                        
                        batch_records.append(case_record)
                        
                        # Process batch when full
                        if len(batch_records) >= self.batch_size:
                            if self._insert_batch(batch_records):
                                stats['batches_processed'] += 1
                                self.processed_count += len(batch_records)
                            else:
                                stats['errors'] += len(batch_records)
                            
                            batch_records = []
                    
                    except Exception as e:
                        stats['errors'] += 1
                        if stats['errors'] <= 10:
                            logger.error(f"Row {stats['rows_read']} error: {e}")
                    
                    # Progress update every 10K rows
                    if stats['rows_read'] % 10000 == 0:
                        elapsed = time.time() - start_time
                        logger.info(f"[{elapsed:.1f}s] scanned={stats['rows_read']:,} tx_hits={stats['texas_matches']:,} pi_mm={stats['pi_mm_cases']:,} dupes={stats['duplicates']:,} errors={stats['errors']:,}")
                
                # Process final batch
                if batch_records:
                    if self._insert_batch(batch_records):
                        stats['batches_processed'] += 1
                        self.processed_count += len(batch_records)
                    else:
                        stats['errors'] += len(batch_records)
        
        except Exception as e:
            logger.error(f"Fatal error processing CSV: {e}")
            raise
        
        elapsed = time.time() - start_time
        stats['elapsed'] = elapsed
        
        logger.info(f"CSV processing complete in {elapsed:.1f}s")
        return stats

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Standalone Texas Bulk Loader")
    parser.add_argument("--csv-file", required=True, help="Path to opinions CSV file")
    parser.add_argument("--batch-size", type=int, default=5000, help="Batch size")
    parser.add_argument("--limit", type=int, help="Limit rows for testing")
    
    args = parser.parse_args()
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Initialize loader
    loader = StandaloneBulkLoader(batch_size=args.batch_size)
    
    # Process CSV
    stats = loader.process_csv_file(args.csv_file, limit=args.limit)
    
    # Final report
    print(f"\n🎉 BULK IMPORT COMPLETE!")
    print(f"   Total rows scanned: {stats['rows_read']:,}")
    print(f"   Texas matches: {stats['texas_matches']:,}")
    print(f"   PI/MM cases: {stats['pi_mm_cases']:,}")
    print(f"   Other cases: {stats['other_cases']:,}")
    print(f"   Duplicates: {stats['duplicates']:,}")
    print(f"   Errors: {stats['errors']:,}")
    print(f"   Batches processed: {stats['batches_processed']:,}")
    print(f"   Processing time: {stats['elapsed']:.1f}s")

if __name__ == "__main__":
    main()
