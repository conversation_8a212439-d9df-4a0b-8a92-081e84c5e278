#!/usr/bin/env python3
"""
Test LangExtract Basic Functionality
"""

import asyncio
import os
from dotenv import load_dotenv
load_dotenv()

# Test basic LangExtract import and functionality
def test_langextract_import():
    """Test basic LangExtract import"""
    try:
        import langextract
        print("✅ LangExtract imported successfully")
        print(f"   Version: {langextract.__version__ if hasattr(langextract, '__version__') else 'Unknown'}")
        return True
    except ImportError as e:
        print(f"❌ Failed to import LangExtract: {e}")
        return False

def test_langextract_basic():
    """Test basic LangExtract functionality"""
    try:
        import langextract
        
        # Sample legal text for testing
        legal_text = """
        This is a personal injury case where <PERSON><PERSON><PERSON> sued Defendant <PERSON> 
        for damages resulting from a motor vehicle accident. Judge <PERSON> presided over 
        the case. The jury awarded $50,000 in actual damages and $25,000 in punitive damages. 
        The case was decided on March 15, 2023, in the 55th Judicial District Court of Harris County, Texas.
        Attorney <PERSON> represented the plaintiff, while Attorney <PERSON> represented the defendant.
        The court cited precedent from <PERSON><PERSON> v<PERSON>, 123 S.W.3d 456 (Tex. 2020).
        """
        
        # Basic extraction test
        print("\n=== Testing LangExtract Basic Functionality ===")
        
        # Check available extractors
        if hasattr(langextract, 'extractors'):
            print("Available extractors:")
            for extractor in dir(langextract.extractors):
                if not extractor.startswith('_'):
                    print(f"  - {extractor}")
        
        # Try basic entity extraction
        if hasattr(langextract, 'extract_entities'):
            entities = langextract.extract_entities(legal_text)
            print(f"\n✅ Basic entity extraction successful")
            print(f"   Entities found: {len(entities) if entities else 0}")
            if entities:
                for i, entity in enumerate(entities[:3]):  # Show first 3
                    print(f"   {i+1}. {entity}")
        else:
            print("⚠️  extract_entities method not found")
        
        return True
        
    except Exception as e:
        print(f"❌ LangExtract basic test failed: {e}")
        return False

def test_langextract_legal_specific():
    """Test LangExtract legal-specific features"""
    try:
        import langextract
        
        legal_text = """
        The Texas Supreme Court in Smith v. Jones, 123 S.W.3d 456 (Tex. 2020), held that 
        defendants in personal injury cases have a duty to preserve evidence. The court 
        awarded $75,000 in compensatory damages to the plaintiff John Smith. Judge Brown 
        presided over the case. Attorney Wilson represented Smith.
        """
        
        print("\n=== Testing LangExtract Legal Features ===")
        
        # Test different extraction types
        extraction_methods = [
            'extract_entities',
            'extract_relationships', 
            'extract_citations',
            'extract_people',
            'extract_organizations',
            'extract_locations',
            'extract_dates',
            'extract_money'
        ]
        
        results = {}
        for method_name in extraction_methods:
            if hasattr(langextract, method_name):
                try:
                    method = getattr(langextract, method_name)
                    result = method(legal_text)
                    results[method_name] = result
                    print(f"✅ {method_name}: {len(result) if result else 0} items")
                except Exception as e:
                    print(f"⚠️  {method_name}: Error - {e}")
            else:
                print(f"❌ {method_name}: Method not available")
        
        return results
        
    except Exception as e:
        print(f"❌ LangExtract legal test failed: {e}")
        return {}

async def test_langextract_async():
    """Test async capabilities if available"""
    try:
        import langextract
        
        print("\n=== Testing LangExtract Async Capabilities ===")
        
        # Check for async methods
        async_methods = [
            'extract_entities_async',
            'extract_relationships_async',
            'process_async'
        ]
        
        legal_text = "Judge Smith ruled in favor of plaintiff Jones in the case filed in Harris County."
        
        for method_name in async_methods:
            if hasattr(langextract, method_name):
                try:
                    method = getattr(langextract, method_name)
                    result = await method(legal_text)
                    print(f"✅ {method_name}: Success")
                except Exception as e:
                    print(f"⚠️  {method_name}: Error - {e}")
            else:
                print(f"❌ {method_name}: Not available")
        
        return True
        
    except Exception as e:
        print(f"❌ LangExtract async test failed: {e}")
        return False

def main():
    """Run all LangExtract tests"""
    print("=== LangExtract Basic Functionality Test ===\n")
    
    # Test 1: Import
    if not test_langextract_import():
        return False
    
    # Test 2: Basic functionality
    test_langextract_basic()
    
    # Test 3: Legal-specific features
    test_langextract_legal_specific()
    
    # Test 4: Async capabilities
    asyncio.run(test_langextract_async())
    
    print("\n=== LangExtract Test Complete ===")
    return True

if __name__ == "__main__":
    main()