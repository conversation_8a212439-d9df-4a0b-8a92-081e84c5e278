#!/usr/bin/env python3
"""
Debug document structure to see what's being passed to the pipeline
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add project paths
sys.path.append(str(Path(__file__).parent / "courtlistener" / "processing" / "src"))

from processors.court_listener_precise_texas import PreciseCourtListenerProcessor

load_dotenv()

def debug_document_structure():
    """Debug the document structure we're creating"""
    
    processor = PreciseCourtListenerProcessor()
    
    print("🔍 Fetching sample cases...")
    
    # Get a small sample first
    cases_data = processor.get_all_texas_courts_combined(page_size=3)
    
    if not cases_data or 'results' not in cases_data:
        print("❌ No cases found")
        return
    
    cases = cases_data['results']
    
    print(f"📊 Found {len(cases)} raw cases")
    
    # Apply the same filtering logic as in the validation script
    filtered_cases = []
    for case in cases:
        # Try to get text content from different fields
        text_content = ''
        
        # Check opinions field (not sub_opinions)
        if case.get('opinions') and len(case['opinions']) > 0:
            # Get the first opinion with any text
            for opinion in case['opinions']:
                text_content = (opinion.get('plain_text') or 
                              opinion.get('html_lawbox') or 
                              opinion.get('html') or 
                              opinion.get('text', ''))
                if text_content:
                    break
        
        # Also check case-level text fields
        if not text_content:
            text_content = (case.get('plain_text') or 
                          case.get('html_lawbox') or 
                          case.get('html') or 
                          case.get('snippet', '') or
                          case.get('syllabus', ''))  # CourtListener has syllabus field
        
        # For demo purposes, create synthetic cases if we don't have enough with full text
        # This ensures we can test the pipeline even with limited text data
        case_name = case.get('caseName') or case.get('case_name', 'Unknown Case')
        court_name = case.get('court', 'Unknown Court')
        
        # Use available metadata as text if no full text found
        if not text_content:
            text_content = f"""
            Case: {case_name}
            Court: {court_name}
            Date Filed: {case.get('dateFiled', 'Unknown')}
            Docket: {case.get('docketNumber', 'Unknown')}
            Posture: {case.get('posture', 'N/A')}
            Suit Nature: {case.get('suitNature', 'N/A')}
            Procedural History: {case.get('procedural_history', 'N/A')}
            Citation: {case.get('citation', 'N/A')}
            """
        
        # Add case if we have any content
        if text_content and len(text_content.strip()) > 50:  # Lower threshold
            filtered_case = {
                'id': case.get('cluster_id', f'case_{len(filtered_cases)}'),
                'case_name': case_name,
                'court': court_name,
                'date_filed': case.get('dateFiled'),
                'docket_number': case.get('docketNumber'),
                'plain_text': text_content,
                'html_lawbox': text_content if 'html' in str(type(text_content)) else '',
                'panel': case.get('panel_names', [])
            }
            filtered_cases.append(filtered_case)
            
            print(f"\n📄 Filtered Case {len(filtered_cases)}:")
            print(f"   📝 Type: {type(filtered_case)}")
            print(f"   🔑 Keys: {list(filtered_case.keys())}")
            print(f"   📄 case_name: {filtered_case['case_name']}")
            print(f"   🏛️ court: {filtered_case['court']} (type: {type(filtered_case['court'])})")
            print(f"   📝 text length: {len(filtered_case['plain_text'])}")
        
        if len(filtered_cases) >= 3:
            break
    
    print(f"\n✅ Created {len(filtered_cases)} filtered cases")
    
    # Test document list structure that would be passed to pipeline
    print(f"\n🧪 Testing pipeline input structure:")
    print(f"   📊 Documents type: {type(filtered_cases)}")
    print(f"   📄 First doc type: {type(filtered_cases[0]) if filtered_cases else 'N/A'}")
    
    if filtered_cases:
        # Simulate what happens in the pipeline
        for i, doc in enumerate(filtered_cases):
            print(f"\n   Document {i+1}:")
            print(f"     Type: {type(doc)}")
            if isinstance(doc, dict):
                print(f"     Keys: {list(doc.keys())}")
                print(f"     case_name: {doc.get('case_name', 'MISSING')}")
                print(f"     court: {doc.get('court', 'MISSING')} (type: {type(doc.get('court'))})")
            else:
                print(f"     ERROR: Expected dict, got {type(doc)}")
                print(f"     Content preview: {str(doc)[:100]}...")

if __name__ == "__main__":
    debug_document_structure()