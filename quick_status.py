#!/usr/bin/env python3
"""
Quick Status Check
Provides immediate status of all running processes and progress.
"""

import os
import json
from datetime import datetime
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

def quick_status():
    """Get quick status of all processes and progress."""
    
    # Database check
    try:
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        supabase = create_client(supabase_url, supabase_key)
        
        # Get current counts
        total_response = supabase.table('cases').select('id', count='exact').eq('jurisdiction', 'TX').execute()
        total_cases = total_response.count
        
        classified_response = supabase.table('cases').select('id', count='exact').eq('jurisdiction', 'TX').not_.is_('primary_practice_area', 'null').execute()
        classified_cases = classified_response.count
        
        unclassified_cases = total_cases - classified_cases
        classification_percentage = (classified_cases / total_cases * 100) if total_cases > 0 else 0
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return
    
    # Process check
    try:
        screen_output = os.popen('screen -list 2>&1').read()
        bulk_loader_running = 'bulk_loader' in screen_output
        classification_worker_running = 'classification_worker' in screen_output
        progress_monitor_running = 'progress_monitor' in screen_output
        
    except Exception as e:
        print(f"❌ Process check error: {e}")
        return
    
    # State file check
    state_file = 'bulk_csv/opinions-2025-07-02.csv.bz2.state'
    state_info = "No state file"
    if os.path.exists(state_file):
        try:
            with open(state_file, 'r') as f:
                state_data = json.load(f)
            state_info = f"Offset: {state_data.get('byte_offset', 0):,}, TX matches: {state_data.get('texas_matches', 0):,}"
        except:
            state_info = "State file exists but unreadable"
    
    # Display results
    print(f"\n⚡ QUICK STATUS - {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 50)
    
    print(f"📊 DATABASE:")
    print(f"   Total TX Cases: {total_cases:,}")
    print(f"   Classified: {classified_cases:,} ({classification_percentage:.1f}%)")
    print(f"   Remaining: {unclassified_cases:,}")
    
    print(f"\n🔄 PROCESSES:")
    print(f"   Bulk Loader: {'✅ Running' if bulk_loader_running else '❌ Stopped'}")
    print(f"   Classification: {'✅ Running' if classification_worker_running else '❌ Stopped'}")
    print(f"   Monitor: {'✅ Running' if progress_monitor_running else '❌ Stopped'}")
    
    print(f"\n📁 STATE:")
    print(f"   {state_info}")
    
    # Estimated completion
    if classification_worker_running and unclassified_cases > 0:
        # Based on previous performance: ~9,742 cases/hour
        estimated_hours = unclassified_cases / 9742
        print(f"\n⏱️  ESTIMATED COMPLETION:")
        print(f"   Classification: ~{estimated_hours:.1f} hours remaining")

if __name__ == "__main__":
    quick_status()
