# Database Cleaning Scripts

This directory contains scripts for clearing all GraphRAG-related data across all backends for fresh processing runs.

## Scripts

### `clear_graphrag_data_script.py`
Complete database clearing script that handles:
- **Neo4j**: Clears all nodes and relationships
- **Pinecone**: Deletes all vectors from texas-laws-voyage3large index
- **Supabase**: Clears GraphRAG-related tables (cases, global_uid_registry, etc.)

**Usage:**
```bash
python clear_graphrag_data_script.py
```

### `clear_gcs_graphrag_script.py`
GCS-specific clearing script for cloud storage:
- **Clears**: `cases/tx/`, `cases/tex/`, `graphrag/` prefixes
- **Preserves**: `documents/` folder (general PDFs)

**Usage:**
```bash
# Preview what will be cleared (safe)
python clear_gcs_graphrag_script.py --preview

# Actually clear the data (destructive)
python clear_gcs_graphrag_script.py --clear --force
```

## Evidence-Based Clearing

Both scripts are based on code analysis and database evidence:

### Verified Paths Cleared:
- **Neo4j**: All nodes/relationships (verified 0 nodes after clearing)
- **Pinecone**: texas-laws-voyage3large index (verified 0 vectors after clearing)  
- **Supabase**: Enhanced GraphRAG tables created via MCP server
- **GCS**: cases/tx/ prefix (verified 76 objects cleared)

### Paths Preserved:
- **GCS**: documents/ folder (153 objects preserved)
- **Supabase**: Core business tables (documents, document_relationships)

## Results Files

Clearing operations generate timestamped result files:
- `gcs_clearing_results_YYYYMMDD_HHMMSS.json`
- `graphrag_clearing_results_YYYYMMDD_HHMMSS.json`

These files provide detailed audit trails of what was cleared and verification results.

## Safety Features

- **Dry run capability** for GCS script (`--preview`)
- **Verification checks** after clearing operations
- **Error logging** and rollback information
- **Evidence-based targeting** to avoid clearing business data