#!/usr/bin/env python3
"""
Reusable Script: Clear GraphRAG Data for Fresh Processing
This script safely clears all GraphRAG-related data across all backends for fresh processing.
"""

import os
import json
from datetime import datetime
from dotenv import load_dotenv

def clear_graphrag_data():
    """
    Clear all GraphRAG data across Neo4j, Pinecone, and Supabase for fresh processing.
    Based on evidence-based analysis of actual table usage.
    """
    load_dotenv()
    
    print("🧹 CLEARING GRAPHRAG DATA FOR FRESH PROCESSING")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'neo4j': {'cleared': False, 'error': None},
        'pinecone': {'cleared': False, 'error': None},
        'supabase': {'cleared': False, 'error': None},
        'verification': {'passed': False, 'details': {}}
    }
    
    # 1. Clear Neo4j (all nodes and relationships)
    print("1️⃣ CLEARING NEO4J...")
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            os.getenv('NEO4J_URI'),
            auth=(os.getenv('NEO4J_USER'), os.getenv('NEO4J_PASSWORD'))
        )
        
        with driver.session() as session:
            # Count before deletion
            result = session.run('MATCH (n) RETURN count(n) as count')
            before_count = result.single()['count']
            print(f"   📊 Nodes before: {before_count}")
            
            # Delete all nodes and relationships
            session.run('MATCH (n) DETACH DELETE n')
            
            # Verify deletion
            result = session.run('MATCH (n) RETURN count(n) as count')
            after_count = result.single()['count']
            print(f"   📊 Nodes after: {after_count}")
            
            if after_count == 0:
                print("   ✅ Neo4j cleared successfully")
                results['neo4j']['cleared'] = True
            else:
                print(f"   ⚠️  Neo4j still has {after_count} nodes")
        
        driver.close()
        
    except Exception as e:
        print(f"   ❌ Neo4j error: {e}")
        results['neo4j']['error'] = str(e)
    
    # 2. Clear Pinecone (texas-laws-voyage3large index)
    print("\n2️⃣ CLEARING PINECONE...")
    try:
        from pinecone import Pinecone
        
        pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
        index = pc.Index('texas-laws-voyage3large')
        
        # Get stats before deletion
        stats_before = index.describe_index_stats()
        vectors_before = getattr(stats_before, 'total_vector_count', 0)
        print(f"   📊 Vectors before: {vectors_before}")
        
        if vectors_before > 0:
            # Delete all vectors
            index.delete(delete_all=True, namespace='tx')
            print("   🗑️  Deletion initiated...")
            
            # Wait and verify
            import time
            time.sleep(3)
            
            stats_after = index.describe_index_stats()
            vectors_after = getattr(stats_after, 'total_vector_count', 0)
            print(f"   📊 Vectors after: {vectors_after}")
            
            if vectors_after == 0:
                print("   ✅ Pinecone cleared successfully")
                results['pinecone']['cleared'] = True
            else:
                print(f"   ⚠️  Pinecone still has {vectors_after} vectors")
        else:
            print("   ℹ️  Pinecone was already empty")
            results['pinecone']['cleared'] = True
        
    except Exception as e:
        print(f"   ❌ Pinecone error: {e}")
        results['pinecone']['error'] = str(e)
    
    # 3. Clear Supabase GraphRAG tables
    print("\n3️⃣ CLEARING SUPABASE GRAPHRAG TABLES...")
    try:
        # Import the MCP functions (this would need to be adapted based on your MCP setup)
        # For now, we'll show the SQL that should be executed
        
        tables_to_clear = [
            'cases',  # EVIDENCE: Contains GraphRAG metadata (gcs_path, pinecone_id, neo4j_node_id)
            'processing_history',  # Processing events
            'case_processing_batches',  # Batch tracking
            # Enhanced GraphRAG tables (if they exist):
            'global_uid_registry',
            'graphrag_entities', 
            'graphrag_relationships',
            'graphrag_schemas',
            'etl_checkpoints',
            'storage_operations',
            'processing_batches_enhanced',
            'storage_health_metrics'
        ]
        
        # Tables to PRESERVE (evidence-based):
        preserved_tables = [
            'documents',  # Document metadata (not processing-specific)  
            'document_relationships'  # Citation relationships (business data)
        ]
        
        print("   📋 Tables to clear:")
        for table in tables_to_clear:
            print(f"      🗑️  {table}")
        
        print("   📋 Tables preserved:")
        for table in preserved_tables:
            print(f"      💾 {table}")
        
        # Execute clearing (example implementation)
        from supabase import create_client
        
        supabase = create_client(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        )
        
        tables_cleared = 0
        for table in ['cases', 'global_uid_registry', 'graphrag_entities', 'graphrag_relationships', 
                     'processing_batches_enhanced', 'etl_checkpoints', 'storage_operations']:
            try:
                # Check if table exists and clear it
                response = supabase.table(table).delete().neq('id', '').execute()
                tables_cleared += 1
                print(f"      ✅ {table} cleared")
            except Exception as table_error:
                print(f"      ⚠️  {table}: {table_error}")
        
        print(f"   ✅ Supabase cleared: {tables_cleared} tables")
        results['supabase']['cleared'] = tables_cleared > 0
        
    except Exception as e:
        print(f"   ❌ Supabase error: {e}")
        results['supabase']['error'] = str(e)
    
    # 4. Verification
    print("\n4️⃣ VERIFICATION...")
    try:
        # Verify all databases are empty
        verification_passed = (
            results['neo4j']['cleared'] and 
            results['pinecone']['cleared'] and 
            results['supabase']['cleared']
        )
        
        results['verification']['passed'] = verification_passed
        results['verification']['details'] = {
            'neo4j': 'Empty' if results['neo4j']['cleared'] else 'Not cleared',
            'pinecone': 'Empty' if results['pinecone']['cleared'] else 'Not cleared', 
            'supabase': 'GraphRAG tables cleared' if results['supabase']['cleared'] else 'Not cleared'
        }
        
        if verification_passed:
            print("   ✅ All databases cleared successfully")
            print("   🎯 Ready for fresh GraphRAG processing!")
        else:
            print("   ⚠️  Some databases not fully cleared")
        
    except Exception as e:
        print(f"   ❌ Verification error: {e}")
        results['verification']['error'] = str(e)
    
    # Save results
    results_file = f"graphrag_clearing_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {results_file}")
    print("🎯 GraphRAG data clearing complete!")
    
    return results

if __name__ == "__main__":
    clear_graphrag_data()