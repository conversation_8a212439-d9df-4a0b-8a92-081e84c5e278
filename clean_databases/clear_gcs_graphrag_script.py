#!/usr/bin/env python3
"""
GCS GraphRAG Data Clearing Script
Evidence-based script to clear GraphRAG-related data from GCS bucket.

VERIFIED EVIDENCE:
- Bucket: texas-laws-personalinjury
- Texas cases stored in: cases/tx/YYYY/case_id/filename.{txt,json}
- Current state: 76 objects in cases/tx/ prefix
- Path format confirmed in gcs_helper.py line 71: f"cases/{jurisdiction}/{year}/{case_id}/{doc_type}.txt"
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Any
from dotenv import load_dotenv

def clear_gcs_graphrag_data() -> Dict[str, Any]:
    """
    Clear GraphRAG-related data from GCS bucket based on evidence from code analysis.
    
    VERIFIED PATHS TO CLEAR:
    - cases/tx/ - Texas case documents and metadata (confirmed 76 objects)
    - cases/tex/ - Alternative Texas jurisdiction (currently empty)
    - graphrag/ - GraphRAG processing artifacts (currently empty)
    
    PRESERVED PATHS:
    - documents/ - General document PDFs (10 objects, not case-processing specific)
    """
    load_dotenv()
    
    print("🧹 CLEARING GCS GRAPHRAG DATA")
    print("=" * 40)
    print(f"Timestamp: {datetime.now().isoformat()}")
    print()
    
    results = {
        'timestamp': datetime.now().isoformat(),
        'bucket_name': '',
        'prefixes_checked': [],
        'objects_found': 0,
        'objects_deleted': 0,
        'errors': [],
        'dry_run': False
    }
    
    try:
        from google.cloud import storage
        
        client = storage.Client()
        bucket_name = os.getenv('GCS_BUCKET_NAME', 'texas-laws-personalinjury')
        bucket = client.bucket(bucket_name)
        
        results['bucket_name'] = bucket_name
        print(f"🪣 Bucket: {bucket_name}")
        print()
        
        # EVIDENCE-BASED prefixes to clear
        prefixes_to_clear = [
            'cases/tx/',      # VERIFIED: 76 objects, Texas case processing data
            'cases/tex/',     # VERIFIED: 0 objects, alternative Texas jurisdiction
            'graphrag/',      # VERIFIED: 0 objects, GraphRAG processing artifacts
        ]
        
        # Prefixes to preserve (evidence-based)
        prefixes_to_preserve = [
            'documents/',     # VERIFIED: 10 PDF objects, general documents not case-specific
        ]
        
        print("📋 EVIDENCE-BASED CLEARING PLAN:")
        print("   🗑️  Prefixes to clear:")
        for prefix in prefixes_to_clear:
            print(f"      - {prefix}")
        print("   💾 Prefixes to preserve:")
        for prefix in prefixes_to_preserve:
            print(f"      - {prefix}")
        print()
        
        total_found = 0
        total_deleted = 0
        
        # Clear each prefix
        for prefix in prefixes_to_clear:
            print(f"🔍 Checking prefix: {prefix}")
            
            try:
                # List all objects with this prefix
                blobs = list(bucket.list_blobs(prefix=prefix))
                objects_count = len(blobs)
                total_found += objects_count
                
                print(f"   📊 Objects found: {objects_count}")
                
                if objects_count > 0:
                    # Show sample objects
                    print(f"   📄 Sample objects:")
                    for blob in blobs[:3]:
                        size_mb = blob.size / (1024*1024) if blob.size else 0
                        print(f"      - {blob.name} ({size_mb:.2f} MB)")
                    
                    # Delete all objects (BE CAREFUL!)
                    print(f"   🗑️  Deleting {objects_count} objects...")
                    
                    deleted_count = 0
                    for blob in blobs:
                        try:
                            blob.delete()
                            deleted_count += 1
                        except Exception as delete_error:
                            error_msg = f"Failed to delete {blob.name}: {delete_error}"
                            results['errors'].append(error_msg)
                            print(f"      ❌ {error_msg}")
                    
                    total_deleted += deleted_count
                    print(f"   ✅ Deleted {deleted_count}/{objects_count} objects")
                    
                else:
                    print(f"   ℹ️  No objects to delete")
                    
            except Exception as prefix_error:
                error_msg = f"Error processing prefix {prefix}: {prefix_error}"
                results['errors'].append(error_msg)
                print(f"   ❌ {error_msg}")
            
            results['prefixes_checked'].append({
                'prefix': prefix,
                'objects_found': objects_count if 'objects_count' in locals() else 0,
                'objects_deleted': deleted_count if 'deleted_count' in locals() else 0
            })
            
            print()
        
        # Verification - check that prefixes are now empty
        print("🔍 VERIFICATION - Checking cleared prefixes are empty:")
        verification_passed = True
        
        for prefix in prefixes_to_clear:
            remaining_blobs = list(bucket.list_blobs(prefix=prefix))
            remaining_count = len(remaining_blobs)
            
            if remaining_count == 0:
                print(f"   ✅ {prefix}: Empty")
            else:
                print(f"   ⚠️  {prefix}: {remaining_count} objects still remain")
                verification_passed = False
        
        # Check preserved prefixes are still there
        print(f"\\n💾 VERIFICATION - Preserved prefixes:")
        for prefix in prefixes_to_preserve:
            preserved_blobs = list(bucket.list_blobs(prefix=prefix))
            preserved_count = len(preserved_blobs)
            print(f"   📁 {prefix}: {preserved_count} objects (preserved)")
        
        results['objects_found'] = total_found
        results['objects_deleted'] = total_deleted
        results['verification_passed'] = verification_passed
        
        print(f"\\n🎯 CLEARING SUMMARY:")
        print(f"   📊 Objects found: {total_found}")
        print(f"   🗑️  Objects deleted: {total_deleted}")
        print(f"   ❌ Errors: {len(results['errors'])}")
        print(f"   ✅ Verification: {'PASSED' if verification_passed else 'FAILED'}")
        
        if verification_passed:
            print(f"\\n🎉 GCS GraphRAG data cleared successfully!")
            print(f"✅ Ready for fresh case processing")
        else:
            print(f"\\n⚠️  Some objects may not have been cleared")
        
    except Exception as e:
        error_msg = f"GCS clearing error: {e}"
        results['errors'].append(error_msg)
        print(f"❌ {error_msg}")
    
    # Save results
    results_file = f"gcs_clearing_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\\n💾 Results saved to: {results_file}")
    return results

def preview_gcs_contents():
    """
    Preview what will be cleared without actually deleting anything.
    Useful for verification before running the actual clearing.
    """
    load_dotenv()
    
    print("🔍 GCS CONTENTS PREVIEW (DRY RUN)")
    print("=" * 45)
    
    try:
        from google.cloud import storage
        
        client = storage.Client()
        bucket_name = os.getenv('GCS_BUCKET_NAME', 'texas-laws-personalinjury')
        bucket = client.bucket(bucket_name)
        
        print(f"🪣 Bucket: {bucket_name}")
        print()
        
        prefixes = ['cases/tx/', 'cases/tex/', 'graphrag/', 'documents/']
        
        for prefix in prefixes:
            blobs = list(bucket.list_blobs(prefix=prefix, max_results=5))
            print(f"📁 {prefix}: {len(blobs)} objects")
            
            for blob in blobs[:3]:
                size_mb = blob.size / (1024*1024) if blob.size else 0
                print(f"   📄 {blob.name} ({size_mb:.2f} MB)")
            
            if len(blobs) > 3:
                print(f"   ... and {len(blobs) - 3} more objects")
            print()
    
    except Exception as e:
        print(f"❌ Preview error: {e}")

if __name__ == "__main__":
    import sys
    
    print("GCS GraphRAG Data Clearing Script")
    print("=" * 40)
    
    if len(sys.argv) > 1 and sys.argv[1] == '--preview':
        preview_gcs_contents()
    elif len(sys.argv) > 1 and sys.argv[1] == '--clear':
        print("⚠️  WARNING: This will permanently delete GCS objects!")
        print("Evidence-based clearing of GraphRAG data in texas-laws-personalinjury bucket")
        print()
        
        # Auto-confirm when running in script mode
        if len(sys.argv) > 2 and sys.argv[2] == '--force':
            print("🤖 Force mode: Auto-confirming deletion...")
            clear_gcs_graphrag_data()
        else:
            try:
                confirm = input("Type 'CLEAR' to confirm deletion: ")
                if confirm == 'CLEAR':
                    clear_gcs_graphrag_data()
                else:
                    print("❌ Clearing cancelled")
            except EOFError:
                print("❌ No input provided, clearing cancelled")
    else:
        print("Usage:")
        print("  --preview  : Preview what will be cleared (safe)")
        print("  --clear    : Actually clear the data (DESTRUCTIVE)")
        print()
        print("Example:")
        print("  python clear_gcs_graphrag_script.py --preview")
        print("  python clear_gcs_graphrag_script.py --clear")