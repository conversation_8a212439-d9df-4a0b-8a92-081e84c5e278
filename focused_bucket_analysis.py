#!/usr/bin/env python3
"""
Focused GCS Bucket Analysis - Structure only, minimal content sampling
"""

import os
import json
import gzip
from collections import defaultdict
from google.cloud import storage
from dotenv import load_dotenv

load_dotenv()

def analyze_bucket_structure_focused():
    print("=== FOCUSED GCS BUCKET ANALYSIS ===\n")
    
    try:
        # Initialize GCS client
        client = storage.Client()
        bucket = client.bucket("texas-laws-personalinjury")
        
        # Analyze folder structure without downloading content
        print("🔍 Scanning bucket structure (metadata only)...")
        
        folder_stats = defaultdict(lambda: {"count": 0, "size": 0, "extensions": set(), "samples": []})
        total_files = 0
        
        # Use iterator to handle large buckets efficiently
        for blob in bucket.list_blobs():
            total_files += 1
            
            path_parts = blob.name.split('/')
            top_folder = path_parts[0] if len(path_parts) > 1 else "root"
            
            # Get file extension
            ext = "." + blob.name.split('.')[-1] if '.' in blob.name else "no_ext"
            
            # Update stats
            folder_stats[top_folder]["count"] += 1
            folder_stats[top_folder]["size"] += blob.size or 0
            folder_stats[top_folder]["extensions"].add(ext)
            
            # Keep limited samples
            if len(folder_stats[top_folder]["samples"]) < 3:
                folder_stats[top_folder]["samples"].append(blob.name)
            
            # Progress indicator
            if total_files % 1000 == 0:
                print(f"   Processed {total_files:,} files...")
        
        print(f"\n📊 BUCKET OVERVIEW:")
        print(f"   Total Files: {total_files:,}")
        total_size_gb = sum(stats["size"] for stats in folder_stats.values()) / (1024**3)
        print(f"   Total Size: {total_size_gb:.2f} GB")
        
        # Display folder analysis
        print(f"\n📁 TOP-LEVEL FOLDERS:")
        print("-" * 60)
        
        for folder, stats in sorted(folder_stats.items(), key=lambda x: x[1]["count"], reverse=True):
            size_mb = stats["size"] / (1024 * 1024)
            print(f"\n📂 {folder}/")
            print(f"   Files: {stats['count']:,}")
            print(f"   Size: {size_mb:.1f} MB")
            print(f"   Types: {', '.join(sorted(stats['extensions']))}")
            print(f"   Samples: {stats['samples']}")
        
        # Focus on TX/ folder 
        print(f"\n" + "=" * 60)
        print(f"🎯 TX/ FOLDER DETAILED ANALYSIS")
        print("=" * 60)
        
        if "TX" in folder_stats:
            tx_stats = folder_stats["TX"]
            print(f"📊 TX/ Summary:")
            print(f"   Files: {tx_stats['count']:,}")
            print(f"   Size: {tx_stats['size'] / (1024**2):.1f} MB")
            print(f"   Extensions: {sorted(tx_stats['extensions'])}")
            
            # Analyze TX/ substructure
            print(f"\n🔍 Analyzing TX/ subfolders...")
            tx_subfolders = defaultdict(lambda: {"count": 0, "samples": []})
            
            # Re-scan just TX/ files for subfolder analysis
            tx_file_count = 0
            for blob in bucket.list_blobs(prefix="TX/"):
                tx_file_count += 1
                path_parts = blob.name.split('/')
                if len(path_parts) >= 3:  # TX/subfolder/file
                    subfolder = path_parts[1]
                    tx_subfolders[subfolder]["count"] += 1
                    if len(tx_subfolders[subfolder]["samples"]) < 2:
                        tx_subfolders[subfolder]["samples"].append(blob.name)
                
                if tx_file_count % 5000 == 0:
                    print(f"   Analyzed {tx_file_count:,} TX/ files...")
            
            print(f"\n📁 TX/ Subfolders:")
            for subfolder, stats in sorted(tx_subfolders.items(), key=lambda x: x[1]["count"], reverse=True):
                print(f"   📂 TX/{subfolder}/ ({stats['count']:,} files)")
                for sample in stats["samples"]:
                    print(f"      - {sample}")
            
            # Sample one TX file to understand structure
            print(f"\n🔍 SAMPLING TX/ FILE STRUCTURE:")
            sample_blob = next(bucket.list_blobs(prefix="TX/", max_results=1), None)
            
            if sample_blob:
                try:
                    print(f"   📄 Sample: {sample_blob.name}")
                    print(f"   Size: {sample_blob.size:,} bytes")
                    
                    # Download and analyze structure
                    content = sample_blob.download_as_bytes()
                    
                    if sample_blob.name.endswith('.gz'):
                        try:
                            content = gzip.decompress(content)
                            print(f"   ✅ Decompressed from {sample_blob.size:,} to {len(content):,} bytes")
                        except Exception as e:
                            print(f"   ⚠️  Decompression failed: {e}")
                    
                    # Try JSON parsing
                    try:
                        data = json.loads(content.decode('utf-8'))
                        print(f"   📊 JSON Structure Analysis:")
                        
                        if isinstance(data, dict):
                            all_keys = list(data.keys())
                            print(f"      Total keys: {len(all_keys)}")
                            print(f"      Key preview: {all_keys[:15]}")
                            
                            # Look for legal document fields
                            legal_fields = ['plain_text', 'html', 'case_name', 'docket_id', 'court', 'opinions', 'cluster']
                            found_legal_fields = [k for k in legal_fields if k in data]
                            if found_legal_fields:
                                print(f"      ✅ Legal fields: {found_legal_fields}")
                                
                                # Check content size
                                for field in ['plain_text', 'html']:
                                    if field in data and isinstance(data[field], str):
                                        content_len = len(data[field])
                                        words = len(data[field].split())
                                        print(f"         {field}: {content_len:,} chars, ~{words:,} words")
                            
                            # Check for Texas indicators
                            data_str = json.dumps(data).lower()
                            tx_indicators = ['texas', 'tex.', 'tx', 'dallas', 'houston', 'austin']
                            found_tx = [ind for ind in tx_indicators if ind in data_str]
                            if found_tx:
                                print(f"      🎯 Texas indicators: {found_tx}")
                        
                        elif isinstance(data, list):
                            print(f"      List with {len(data)} items")
                    
                    except json.JSONDecodeError as e:
                        print(f"   ❌ Not JSON: {e}")
                        
                        # Try plain text
                        try:
                            text = content.decode('utf-8')
                            print(f"   📝 Plain text: {len(text):,} characters")
                        except:
                            print(f"   ⚠️  Binary content")
                
                except Exception as e:
                    print(f"   ❌ Error sampling: {e}")
        else:
            print("❌ No TX/ folder found")
        
        # Generate recommendations
        print(f"\n" + "=" * 60)
        print(f"🎯 ENHANCED GCS CLIENT RECOMMENDATIONS")
        print("=" * 60)
        
        if "TX" in folder_stats:
            print(f"✅ PRIMARY DATA SOURCE: TX/ folder ({folder_stats['TX']['count']:,} files)")
            print(f"✅ UPDATE enhanced_gcs_client.py:")
            print(f"   - Change find_texas_cases() prefix from 'courtlistener/opinions/' to 'TX/'")
            print(f"   - Add JSON parsing capability")
            if '.gz' in str(folder_stats['TX']['extensions']):
                print(f"   - Add gzip decompression support")
        
        other_folders = [f for f in folder_stats.keys() if f != "TX" and folder_stats[f]["count"] > 100]
        if other_folders:
            print(f"⚠️  OTHER SIGNIFICANT FOLDERS: {other_folders}")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_bucket_structure_focused()