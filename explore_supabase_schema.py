#!/usr/bin/env python3
"""
Explore Supabase Schema
Find existing tables and their structure
"""

import os
from supabase import create_client
from dotenv import load_dotenv

load_dotenv()

def explore_supabase_schema():
    """Explore the existing Supabase schema"""
    print("=== Exploring Supabase Schema ===\n")
    
    try:
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_KEY")
        
        if not supabase_url or not supabase_key:
            print("❌ Missing SUPABASE_URL or SUPABASE_KEY environment variables")
            return
        
        supabase = create_client(supabase_url, supabase_key)
        print(f"✅ Connected to Supabase: {supabase_url}")
        
        # Try to find tables by querying information_schema
        print("\n1. Querying information_schema for tables...")
        try:
            # This might not work with Supabase client, but let's try
            tables_query = """
            SELECT table_name, table_type 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
            """
            # Note: Supabase client might not support raw SQL like this
            print("   ⚠️  Cannot query information_schema directly with Supabase client")
        except Exception as e:
            print(f"   ❌ Failed to query information_schema: {e}")
        
        # Try some common table names that might exist
        print("\n2. Testing common table names...")
        
        common_tables = [
            "documents",
            "legal_documents", 
            "cases",
            "court_cases",
            "opinions",
            "legal_opinions",
            "text_documents",
            "processed_documents",
            "chunks",
            "document_chunks",
            "embeddings",
            "legal_data",
            "courtlistener_cases",
            "cap_cases",
            "texas_cases",
            "personal_injury_cases"
        ]
        
        existing_tables = []
        
        for table_name in common_tables:
            try:
                # Try to select just count from each table
                response = supabase.table(table_name).select("*", count="exact").limit(1).execute()
                
                if hasattr(response, 'count') and response.count is not None:
                    print(f"   ✅ Found table: {table_name} ({response.count} rows)")
                    existing_tables.append(table_name)
                elif response.data is not None:
                    print(f"   ✅ Found table: {table_name} ({len(response.data)} sample rows)")
                    existing_tables.append(table_name)
                    
            except Exception as e:
                error_msg = str(e)
                if "does not exist" in error_msg:
                    print(f"   ❌ Table does not exist: {table_name}")
                else:
                    print(f"   ⚠️  Error accessing {table_name}: {error_msg[:50]}...")
        
        # Explore structure of found tables
        if existing_tables:
            print(f"\n3. Exploring structure of found tables...")
            
            for table_name in existing_tables:
                try:
                    print(f"\n   📊 Table: {table_name}")
                    
                    # Get a sample row to see the structure
                    response = supabase.table(table_name).select("*").limit(1).execute()
                    
                    if response.data:
                        sample_row = response.data[0]
                        print(f"      Columns: {list(sample_row.keys())}")
                        
                        # Show sample values (truncated)
                        for key, value in sample_row.items():
                            if isinstance(value, str) and len(value) > 50:
                                value = value[:50] + "..."
                            print(f"         {key}: {type(value).__name__} = {value}")
                    else:
                        print(f"      No data found in {table_name}")
                        
                except Exception as e:
                    print(f"      ❌ Error exploring {table_name}: {e}")
        else:
            print("\n3. No tables found with common names")
            print("   📝 You may need to create the legal_documents table first")
            
            # Show what the table structure should look like
            print("\n4. Expected legal_documents table structure:")
            print("""
            CREATE TABLE legal_documents (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                case_name TEXT,
                practice_area TEXT,
                court_name TEXT,
                file_date DATE,
                gcs_path TEXT,
                processing_status TEXT DEFAULT 'unprocessed',
                processing_metadata JSONB,
                last_processed TIMESTAMP,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
            """)
        
        print(f"\n=== Schema Exploration Complete ===")
        print(f"Found {len(existing_tables)} tables: {existing_tables}")
        
        return existing_tables
        
    except Exception as e:
        print(f"❌ Failed to explore Supabase schema: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    explore_supabase_schema()