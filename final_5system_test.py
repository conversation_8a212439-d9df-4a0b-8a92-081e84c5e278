#!/usr/bin/env python3
"""
Final 5-System Test - Complete Cross-System Validation
Tests all 5 systems with full cross-tracing for 100% success rate
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
load_dotenv()

class Final5SystemTest:
    """
    Complete 5-system test with cross-system tracing
    """
    
    def __init__(self):
        self.test_id = f"final_5system_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"🎯 Final 5-System Test: {self.test_id}")
        
    async def test_supabase_read_operations(self):
        """Test Supabase read operations (primary use case)"""
        try:
            from supabase import create_client
            
            client = create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_SERVICE_ROLE_KEY"))
            
            operations = {}
            
            # Test 1: Get case data for processing
            start_time = time.time()
            result = client.table("cases").select("id", "case_name", "gcs_path", "word_count").eq("jurisdiction", "TX").limit(5).execute()
            operations["get_cases_for_processing"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "success": len(result.data) > 0,
                "cases_found": len(result.data),
                "sample_case": result.data[0] if result.data else None
            }
            
            # Test 2: Get case by ID (for cross-system tracing)
            if operations["get_cases_for_processing"]["success"]:
                case_id = operations["get_cases_for_processing"]["sample_case"]["id"]
                start_time = time.time()
                result = client.table("cases").select("*").eq("id", case_id).execute()
                operations["get_case_by_id"] = {
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "success": len(result.data) > 0,
                    "case_data": result.data[0] if result.data else None
                }
            
            # Test 3: Check if case has GCS path for document retrieval
            if operations.get("get_case_by_id", {}).get("success"):
                case_data = operations["get_case_by_id"]["case_data"]
                operations["gcs_path_check"] = {
                    "has_gcs_path": case_data.get("gcs_path") is not None,
                    "gcs_path": case_data.get("gcs_path"),
                    "ready_for_processing": (
                        case_data.get("gcs_path") is not None and 
                        case_data.get("word_count", 0) > 100
                    )
                }
            
            return {
                "success": all(op.get("success", True) for op in operations.values()),
                "operations": operations,
                "production_ready": operations.get("get_cases_for_processing", {}).get("success", False)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_neo4j_functionality(self):
        """Test Neo4j with entity storage and retrieval"""
        try:
            from neo4j import GraphDatabase
            
            uri = os.getenv("NEO4J_URI")
            username = os.getenv("NEO4J_USERNAME", "neo4j")  
            password = os.getenv("NEO4J_PASSWORD")
            
            driver = GraphDatabase.driver(uri, auth=(username, password))
            
            operations = {}
            
            # Test 1: Connection and node count
            with driver.session() as session:
                start_time = time.time()
                result = session.run("MATCH (n) RETURN COUNT(n) as node_count")
                record = result.single()
                operations["connection_and_count"] = {
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "success": record is not None,
                    "node_count": record["node_count"] if record else 0
                }
                
                # Test 2: Test entity creation and retrieval
                test_entity_name = f"TestEntity_{int(time.time())}"
                start_time = time.time()
                
                # Create a test entity
                session.run("""
                    CREATE (p:Person {
                        name: $name,
                        role: 'TestRole',
                        global_uid: $uid,
                        test_timestamp: datetime()
                    })
                """, name=test_entity_name, uid=f"test_{self.test_id}")
                
                # Retrieve the entity
                result = session.run("""
                    MATCH (p:Person {name: $name})
                    RETURN p.name as name, p.role as role, p.global_uid as uid
                """, name=test_entity_name)
                
                entity_record = result.single()
                operations["entity_crud"] = {
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "success": entity_record is not None,
                    "entity_created": entity_record["name"] == test_entity_name if entity_record else False,
                    "global_uid_stored": entity_record["uid"] == f"test_{self.test_id}" if entity_record else False
                }
                
                # Test 3: Cleanup test entity
                session.run("MATCH (p:Person {name: $name}) DELETE p", name=test_entity_name)
            
            driver.close()
            
            return {
                "success": all(op["success"] for op in operations.values()),
                "operations": operations
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_pinecone_functionality(self):
        """Test Pinecone vector operations"""
        try:
            from pinecone import Pinecone
            
            api_key = os.getenv("PINECONE_API_KEY")
            index_name = os.getenv("PINECONE_INDEX_NAME", "legal-documents")
            
            pc = Pinecone(api_key=api_key)
            index = pc.Index(index_name)
            
            operations = {}
            
            # Test 1: Index stats
            start_time = time.time()
            stats = index.describe_index_stats()
            operations["index_stats"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "success": True,
                "total_vectors": stats.total_vector_count,
                "dimension": stats.dimension
            }
            
            # Test 2: Vector operations
            test_vector_id = f"test_{self.test_id}_{int(time.time())}"
            test_vector = [0.1] * 1024  # 1024-dimensional test vector
            test_metadata = {
                "case_id": "test_case_12345",
                "global_uid": f"test_{self.test_id}",
                "test_type": "cross_system_validation",
                "text": "Test legal document for cross-system tracing"
            }
            
            # Upsert test vector
            start_time = time.time()
            index.upsert(vectors=[(test_vector_id, test_vector, test_metadata)], namespace="tx")
            operations["vector_upsert"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "success": True,
                "vector_id": test_vector_id
            }
            
            # Query test vector
            start_time = time.time()
            query_result = index.query(
                vector=test_vector,
                top_k=5,
                namespace="tx",
                include_metadata=True,
                filter={"global_uid": f"test_{self.test_id}"}
            )
            operations["vector_query"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "success": len(query_result.matches) > 0,
                "matches_found": len(query_result.matches),
                "global_uid_match": any(
                    match.metadata and match.metadata.get("global_uid") == f"test_{self.test_id}"
                    for match in query_result.matches
                )
            }
            
            # Cleanup test vector
            index.delete(ids=[test_vector_id], namespace="tx")
            
            return {
                "success": all(op["success"] for op in operations.values()),
                "operations": operations
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_vertex_ai_functionality(self):
        """Test Vertex AI entity extraction"""
        try:
            import vertexai
            from vertexai.generative_models import GenerativeModel
            
            # Initialize Vertex AI
            vertexai.init()
            model = GenerativeModel("gemini-2.0-flash-exp")
            
            operations = {}
            
            # Test entity extraction with legal text
            test_text = f"""
            In the case Rodriguez v. Texas Medical Center (Test Case {self.test_id}), 
            plaintiff Maria Rodriguez sued for medical malpractice. 
            Judge Sarah Johnson presided over the case on August 20, 2024.
            The jury awarded $250,000 in damages. Attorney Michael Davis represented the plaintiff.
            """
            
            prompt = f"""
            Extract legal entities from this text in JSON format:
            {test_text}
            
            Return only JSON with entities array containing objects with name, type, and confidence fields.
            """
            
            start_time = time.time()
            response = model.generate_content(prompt)
            operations["entity_extraction"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "success": response.text is not None and len(response.text) > 0,
                "response_length": len(response.text) if response.text else 0,
                "contains_json": "{" in response.text if response.text else False
            }
            
            return {
                "success": operations["entity_extraction"]["success"],
                "operations": operations
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_voyage_ai_functionality(self):
        """Test Voyage AI embeddings"""
        try:
            import requests
            
            api_key = os.getenv("VOYAGE_API_KEY")
            
            operations = {}
            
            # Test embedding generation
            test_text = f"Legal document test for cross-system validation {self.test_id}: Personal injury case involving medical malpractice with substantial damages awarded."
            
            url = "https://api.voyageai.com/v1/embeddings"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            data = {
                "input": [test_text],
                "model": "voyage-3"
            }
            
            start_time = time.time()
            response = requests.post(url, headers=headers, json=data)
            operations["embedding_generation"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "success": response.status_code == 200,
                "status_code": response.status_code
            }
            
            if operations["embedding_generation"]["success"]:
                result = response.json()
                embedding = result["data"][0]["embedding"]
                operations["embedding_generation"]["embedding_dimension"] = len(embedding)
                operations["embedding_generation"]["embedding_preview"] = embedding[:5]
            
            return {
                "success": operations["embedding_generation"]["success"],
                "operations": operations
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_cross_system_tracing(self):
        """Test cross-system tracing with global UID"""
        try:
            # Create a global UID for cross-system testing
            global_uid = f"cross_test_{self.test_id}_{int(time.time())}"
            
            operations = {}
            
            # Test 1: Store same UID in Neo4j
            from neo4j import GraphDatabase
            
            uri = os.getenv("NEO4J_URI")
            username = os.getenv("NEO4J_USERNAME", "neo4j")
            password = os.getenv("NEO4J_PASSWORD")
            
            driver = GraphDatabase.driver(uri, auth=(username, password))
            
            with driver.session() as session:
                start_time = time.time()
                session.run("""
                    CREATE (c:Case {
                        name: $name,
                        global_uid: $uid,
                        test_timestamp: datetime(),
                        cross_system_test: true
                    })
                """, name=f"Cross System Test {self.test_id}", uid=global_uid)
                
                operations["neo4j_storage"] = {
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "success": True,
                    "global_uid": global_uid
                }
            
            # Test 2: Store same UID in Pinecone
            from pinecone import Pinecone
            
            pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
            index = pc.Index(os.getenv("PINECONE_INDEX_NAME", "legal-documents"))
            
            test_vector = [0.2] * 1024
            metadata = {
                "global_uid": global_uid,
                "case_name": f"Cross System Test {self.test_id}",
                "system_origin": "cross_system_test",
                "test_timestamp": datetime.now().isoformat()
            }
            
            start_time = time.time()
            index.upsert(vectors=[(f"cross_test_{global_uid}", test_vector, metadata)], namespace="tx")
            operations["pinecone_storage"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "success": True,
                "global_uid": global_uid
            }
            
            # Test 3: Query both systems with same global UID
            # Query Neo4j
            with driver.session() as session:
                start_time = time.time()
                result = session.run("""
                    MATCH (c:Case {global_uid: $uid})
                    RETURN c.name as name, c.global_uid as uid, c.cross_system_test as test_flag
                """, uid=global_uid)
                
                neo4j_record = result.single()
                operations["neo4j_retrieval"] = {
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "success": neo4j_record is not None,
                    "global_uid_match": neo4j_record["uid"] == global_uid if neo4j_record else False
                }
            
            # Query Pinecone
            start_time = time.time()
            query_result = index.query(
                vector=test_vector,
                top_k=10,
                namespace="tx",
                include_metadata=True,
                filter={"global_uid": global_uid}
            )
            
            operations["pinecone_retrieval"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "success": len(query_result.matches) > 0,
                "global_uid_match": any(
                    match.metadata and match.metadata.get("global_uid") == global_uid
                    for match in query_result.matches
                )
            }
            
            # Cleanup
            with driver.session() as session:
                session.run("MATCH (c:Case {global_uid: $uid}) DELETE c", uid=global_uid)
            index.delete(ids=[f"cross_test_{global_uid}"], namespace="tx")
            driver.close()
            
            return {
                "success": all(op["success"] for op in operations.values()),
                "operations": operations,
                "global_uid": global_uid,
                "cross_system_consistency": (
                    operations.get("neo4j_retrieval", {}).get("global_uid_match", False) and
                    operations.get("pinecone_retrieval", {}).get("global_uid_match", False)
                )
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def run_final_5_system_test(self):
        """Run complete 5-system test with cross-tracing"""
        
        logger.info("🎯 RUNNING FINAL 5-SYSTEM TEST WITH CROSS-TRACING")
        logger.info("=" * 80)
        
        results = {
            "test_id": self.test_id,
            "timestamp": datetime.now().isoformat(),
            "systems_tested": 0,
            "systems_passed": 0,
            "overall_success": False,
            "cross_system_tracing": False
        }
        
        # Test all 5 systems
        systems = [
            ("Supabase", self.test_supabase_read_operations),
            ("Neo4j", self.test_neo4j_functionality),
            ("Pinecone", self.test_pinecone_functionality),
            ("Vertex AI", self.test_vertex_ai_functionality),
            ("Voyage AI", self.test_voyage_ai_functionality)
        ]
        
        for system_name, test_func in systems:
            logger.info(f"🔍 Testing {system_name}...")
            
            try:
                result = await test_func()
                results[system_name.lower().replace(" ", "_")] = result
                results["systems_tested"] += 1
                
                if result["success"]:
                    results["systems_passed"] += 1
                    logger.info(f"✅ {system_name}: Working")
                else:
                    logger.error(f"❌ {system_name}: {result.get('error', 'Failed')}")
                    
            except Exception as e:
                logger.error(f"❌ {system_name}: Exception - {e}")
                results[system_name.lower().replace(" ", "_")] = {"success": False, "error": str(e)}
                results["systems_tested"] += 1
        
        # Test cross-system tracing
        logger.info("🔍 Testing cross-system tracing...")
        try:
            tracing_result = await self.test_cross_system_tracing()
            results["cross_system_tracing_test"] = tracing_result
            results["cross_system_tracing"] = tracing_result.get("cross_system_consistency", False)
            
            if tracing_result["success"]:
                logger.info("✅ Cross-system tracing: Working")
            else:
                logger.error(f"❌ Cross-system tracing: {tracing_result.get('error', 'Failed')}")
                
        except Exception as e:
            logger.error(f"❌ Cross-system tracing: Exception - {e}")
            results["cross_system_tracing_test"] = {"success": False, "error": str(e)}
        
        # Calculate final results
        results["success_rate"] = (results["systems_passed"] / results["systems_tested"]) * 100
        results["overall_success"] = results["systems_passed"] == 5  # All 5 systems must pass
        results["production_ready"] = (
            results["overall_success"] and 
            results["cross_system_tracing"]
        )
        
        return results

async def main():
    """Execute final 5-system test"""
    
    print("🎯 FINAL 5-SYSTEM TEST WITH CROSS-TRACING")
    print("=" * 60)
    print("Testing all 5 systems with complete cross-system validation")
    print()
    
    try:
        tester = Final5SystemTest()
        results = await tester.run_final_5_system_test()
        
        print(f"\n🏆 FINAL 5-SYSTEM TEST RESULTS")
        print("=" * 50)
        print(f"Systems Tested: {results['systems_tested']}/5")
        print(f"Systems Passed: {results['systems_passed']}/5") 
        print(f"Success Rate: {results['success_rate']:.1f}%")
        print(f"Cross-System Tracing: {'✅ Working' if results['cross_system_tracing'] else '❌ Failed'}")
        print(f"Overall Success: {'🎉 YES' if results['overall_success'] else '❌ NO'}")
        print(f"Production Ready: {'🚀 YES' if results['production_ready'] else '⚠️ NO'}")
        
        # Save results
        filename = f"final_5system_test_{tester.test_id}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Full results: {filename}")
        
        if results["production_ready"]:
            print("\n🎉 SUCCESS: ALL 5 SYSTEMS OPERATIONAL WITH CROSS-TRACING!")
            print("   ✅ Supabase: Data access working")
            print("   ✅ Neo4j: Entity storage working")
            print("   ✅ Pinecone: Vector operations working")
            print("   ✅ Vertex AI: Entity extraction working")
            print("   ✅ Voyage AI: Embedding generation working")
            print("   ✅ Cross-System: Global UID tracing verified")
            print("\n🚀 SYSTEM IS PRODUCTION READY FOR SCALE TESTING!")
        else:
            failed_systems = 5 - results["systems_passed"]
            print(f"\n⚠️ {failed_systems} system(s) need fixing before production")
            
        return results["production_ready"]
        
    except Exception as e:
        print(f"❌ Final test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)