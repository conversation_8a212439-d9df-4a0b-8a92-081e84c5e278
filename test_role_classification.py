#!/usr/bin/env python3
"""
Test Role Classification System
Validate the two-phase entity extraction with role classification
"""

import asyncio
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_role_classification():
    """Test the complete two-phase entity extraction system"""
    logger.info("🧪 TESTING ROLE CLASSIFICATION SYSTEM")
    logger.info("=" * 70)
    
    try:
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import EnhancedGraphRAGPipeline
        from courtlistener.processing.src.processing.role_classifier import LegalRoleClassifier
        from courtlistener.processing.src.processing.cost_monitor import CostMonitor
        from neo4j import GraphDatabase
        
        # Initialize cost monitor
        cost_monitor = CostMonitor()
        
        # Test document with rich entity content for classification
        test_document = {
            "id": "role_classification_test_001",
            "case_name": "<PERSON> v. Memorial Hermann Hospital",
            "court": {"name": "157th Judicial District Court of Harris County"},
            "date_filed": "2023-11-01",
            "docket_number": "2023-RC-001",
            "plain_text": """
            In Anderson v. Memorial Hermann Hospital, plaintiff Sarah Anderson sued Memorial Hermann 
            Hospital for medical malpractice. The Honorable Judge Robert Wilson presided over the case 
            in the 157th Judicial District Court of Harris County, Texas.
            
            Attorney Jennifer Martinez of Martinez Law Firm represented plaintiff Anderson, while 
            Attorney Michael Davis of Healthcare Defense Group represented defendant Memorial Hermann Hospital.
            
            The case involved surgical complications during Anderson's gallbladder removal performed 
            by Dr. Lisa Chen, a specialist at Memorial Hermann Hospital, on January 15, 2022. 
            
            Expert witness Dr. James Rodriguez, a gastroenterology specialist from Houston Methodist Hospital,
            testified that the standard of care was breached during the surgical procedure.
            
            The jury awarded plaintiff $150,000 in damages. State Farm Insurance provided coverage
            for the hospital's liability under their professional liability policy.
            
            The case was heard before a jury in Harris County District Court, with Judge Wilson
            providing jury instructions on medical malpractice standards in Texas.
            """
        }
        
        logger.info("📋 Test Document Contains:")
        logger.info("   • Judges: Robert Wilson")
        logger.info("   • Attorneys: Jennifer Martinez (plaintiff), Michael Davis (defense)")
        logger.info("   • Plaintiff: Sarah Anderson")
        logger.info("   • Defendant: Memorial Hermann Hospital")
        logger.info("   • Doctors: Dr. Lisa Chen (treating), Dr. James Rodriguez (expert)")
        logger.info("   • Organizations: Martinez Law Firm, Healthcare Defense Group, Houston Methodist")
        logger.info("   • Court: 157th Judicial District Court, Harris County District Court")
        logger.info("   • Insurance: State Farm Insurance")
        logger.info("")
        
        # Phase 1: GraphRAG Entity Extraction
        logger.info("🏗️  PHASE 1: COARSE-GRAINED ENTITY EXTRACTION")
        logger.info("-" * 50)
        
        pipeline = EnhancedGraphRAGPipeline(
            cost_monitor=cost_monitor,
            neo4j_uri=os.getenv("NEO4J_URI"),
            neo4j_user=os.getenv("NEO4J_USERNAME", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD"),
            gemini_api_key=os.getenv("GEMINI_API_KEY", "unused"),
            voyage_api_key=os.getenv("VOYAGE_API_KEY"),
            practice_area="personal_injury",
            clean_database=True  # Clean for accurate testing
        )
        
        # Process document through GraphRAG
        result = await pipeline.process_documents([test_document], batch_size=1)
        
        logger.info(f"✅ Phase 1 Complete:")
        logger.info(f"   Entities extracted: {result['entities_extracted']}")
        logger.info(f"   Relationships extracted: {result['relationships_extracted']}")
        logger.info("")
        
        # Examine extracted entities before classification
        with pipeline.driver.session() as session:
            entities_query = """
                MATCH (n:__KGBuilder__)
                WHERE NOT n:Chunk AND n.name IS NOT NULL
                RETURN n.name as name, 
                       [label IN labels(n) WHERE label <> '__KGBuilder__'][0] as type,
                       properties(n) as props
                ORDER BY type, n.name
            """
            
            entities_result = session.run(entities_query)
            extracted_entities = []
            for record in entities_result:
                extracted_entities.append({
                    "name": record["name"],
                    "type": record["type"],
                    "properties": dict(record["props"])
                })
        
        logger.info("📊 Coarse-Grained Entities Extracted:")
        entity_types = {}
        for entity in extracted_entities:
            entity_type = entity["type"]
            if entity_type not in entity_types:
                entity_types[entity_type] = []
            entity_types[entity_type].append(entity["name"])
        
        for entity_type, names in sorted(entity_types.items()):
            logger.info(f"   {entity_type} ({len(names)}): {', '.join(names[:5])}")
            if len(names) > 5:
                logger.info(f"      ... and {len(names) - 5} more")
        logger.info("")
        
        # Phase 2: Role Classification
        logger.info("🏷️  PHASE 2: ROLE CLASSIFICATION")
        logger.info("-" * 50)
        
        # Initialize role classifier
        classifier = LegalRoleClassifier(
            driver=pipeline.driver,
            enable_llm_fallback=False  # Use rule-based and context-based only for now
        )
        
        # Classify entities
        classifications = await classifier.classify_entities_for_run(None)
        
        logger.info(f"✅ Phase 2 Complete:")
        logger.info(f"   Entities classified: {len(classifications)}")
        logger.info("")
        
        # Display classification results
        logger.info("🎯 ROLE CLASSIFICATION RESULTS:")
        logger.info("")
        
        # Group by role for analysis
        role_groups = {}
        for classification in classifications:
            role = classification.assigned_role
            if role not in role_groups:
                role_groups[role] = []
            role_groups[role].append(classification)
        
        for role, entities in sorted(role_groups.items()):
            logger.info(f"   {role} ({len(entities)} entities):")
            for classification in entities:
                confidence_icon = "🟢" if classification.confidence >= 0.8 else "🟡" if classification.confidence >= 0.6 else "🔴"
                logger.info(f"      {confidence_icon} {classification.entity_name}")
                logger.info(f"         Confidence: {classification.confidence:.2f}")
                logger.info(f"         Evidence: {', '.join(classification.evidence[:2])}")
            logger.info("")
        
        # Get classification summary
        summary = classifier.get_classification_summary()
        logger.info("📈 CLASSIFICATION SUMMARY:")
        logger.info(f"   Total entities classified: {summary['total_entities_classified']}")
        logger.info(f"   Success rate: {summary['success_rate']:.1%}")
        logger.info(f"   High confidence (≥80%): {summary['confidence_distribution']['high_confidence']}")
        logger.info(f"   Medium confidence (≥70%): {summary['confidence_distribution']['medium_confidence']}")
        logger.info(f"   Low confidence (≥50%): {summary['confidence_distribution']['low_confidence']}")
        logger.info("")
        
        # Validation analysis
        logger.info("🔍 VALIDATION ANALYSIS:")
        
        # Expected roles based on test document
        expected_roles = {
            "Judge": ["Robert Wilson"],
            "Attorney": ["Jennifer Martinez", "Michael Davis"],
            "Plaintiff": ["Sarah Anderson"],
            "Hospital": ["Memorial Hermann Hospital", "Houston Methodist Hospital"],
            "Expert": ["Lisa Chen", "James Rodriguez"],
            "LawFirm": ["Martinez Law Firm", "Healthcare Defense Group"],
            "Court": ["157th Judicial District Court", "Harris County District Court"],
            "Insurance": ["State Farm Insurance"]
        }
        
        # Check accuracy
        correct_classifications = 0
        total_expected = sum(len(entities) for entities in expected_roles.values())
        
        for role, expected_entities in expected_roles.items():
            classified_in_role = [c.entity_name for c in classifications if c.assigned_role == role]
            
            for expected_entity in expected_entities:
                # Check if any classified entity contains the expected name (partial matching)
                found = any(expected_entity.lower() in classified.lower() or 
                           classified.lower() in expected_entity.lower() 
                           for classified in classified_in_role)
                
                if found:
                    correct_classifications += 1
                    status = "✅"
                else:
                    status = "❌"
                
                logger.info(f"   {status} Expected {role}: {expected_entity}")
        
        accuracy = correct_classifications / max(total_expected, 1)
        logger.info("")
        logger.info(f"🎯 Overall Classification Accuracy: {accuracy:.1%}")
        logger.info(f"   Correct: {correct_classifications}/{total_expected}")
        
        # Get detailed validation metrics
        validation_results = await classifier.validate_classifications()
        logger.info("")
        logger.info("📊 DETAILED VALIDATION METRICS:")
        logger.info(f"   Average confidence: {validation_results['average_confidence']:.2f}")
        logger.info(f"   Method breakdown:")
        for method, count in validation_results['method_breakdown'].items():
            logger.info(f"      {method}: {count} entities")
        
        # Close pipeline
        pipeline.close()
        
        return {
            "phase1_entities": len(extracted_entities),
            "phase1_relationships": result.get("relationships_extracted", 0),
            "phase2_classifications": len(classifications),
            "accuracy": accuracy,
            "classification_summary": summary,
            "validation_results": validation_results
        }
        
    except Exception as e:
        logger.error(f"❌ Role classification testing failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_role_classification())
    
    if result:
        print(f"\n🎉 ROLE CLASSIFICATION TESTING COMPLETE")
        print(f"Phase 1 (GraphRAG): {result['phase1_entities']} entities, {result['phase1_relationships']} relationships")
        print(f"Phase 2 (Classification): {result['phase2_classifications']} entities classified")
        print(f"Overall accuracy: {result['accuracy']:.1%}")
        print(f"Success rate: {result['classification_summary']['success_rate']:.1%}")
        
        if result['accuracy'] >= 0.7:
            print(f"✅ Role classification system is working effectively!")
        else:
            print(f"⚠️  Role classification needs improvement")
    else:
        print(f"\n❌ Role classification testing failed")