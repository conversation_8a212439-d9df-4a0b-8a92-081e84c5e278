#!/usr/bin/env python3
"""
Investigate Entity Extraction Quality
Check what entities are actually being extracted and stored in Neo4j
"""

import asyncio
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def investigate_entity_extraction():
    """Investigate what entities are actually being extracted"""
    logger.info("🔍 INVESTIGATING ENTITY EXTRACTION QUALITY")
    logger.info("=" * 60)
    
    try:
        from neo4j import GraphDatabase
        
        # Connect to Neo4j to inspect what was actually created
        neo4j_uri = os.getenv("NEO4J_URI")
        neo4j_user = os.getenv("NEO4J_USERNAME", "neo4j")
        neo4j_password = os.getenv("NEO4J_PASSWORD")
        
        driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        
        with driver.session() as session:
            # Query all nodes that were created by GraphRAG
            logger.info("1. Querying all GraphRAG nodes...")
            node_result = session.run("""
                MATCH (n:__KGBuilder__) 
                RETURN n.name as name, labels(n) as labels, n.__tmp_internal_id as internal_id, 
                       properties(n) as properties
                ORDER BY n.__tmp_internal_id
            """)
            
            nodes = []
            for record in node_result:
                nodes.append({
                    "name": record["name"],
                    "labels": record["labels"], 
                    "internal_id": record["internal_id"],
                    "properties": dict(record["properties"])
                })
            
            logger.info(f"   Found {len(nodes)} nodes in Neo4j")
            
            # Show first 10 nodes
            for i, node in enumerate(nodes[:10]):
                logger.info(f"   Node {i+1}: {node['name']} | Labels: {node['labels']} | Props: {list(node['properties'].keys())}")
            
            if len(nodes) > 10:
                logger.info(f"   ... and {len(nodes) - 10} more nodes")
            
            # Query all relationships
            logger.info("")
            logger.info("2. Querying all GraphRAG relationships...")
            rel_result = session.run("""
                MATCH (start:__KGBuilder__)-[r]->(end:__KGBuilder__)
                RETURN type(r) as rel_type, start.name as start_name, end.name as end_name,
                       properties(r) as properties
                LIMIT 20
            """)
            
            relationships = []
            for record in rel_result:
                relationships.append({
                    "type": record["rel_type"],
                    "start": record["start_name"],
                    "end": record["end_name"],
                    "properties": dict(record["properties"])
                })
            
            logger.info(f"   Found {len(relationships)} relationships in Neo4j")
            
            for i, rel in enumerate(relationships):
                logger.info(f"   Rel {i+1}: {rel['start']} --{rel['type']}--> {rel['end']}")
            
            # Check for different node types
            logger.info("")
            logger.info("3. Analyzing node types distribution...")
            type_result = session.run("""
                MATCH (n:__KGBuilder__)
                UNWIND labels(n) as label
                WITH label, count(*) as count
                WHERE label <> '__KGBuilder__'
                RETURN label, count
                ORDER BY count DESC
            """)
            
            node_types = []
            for record in type_result:
                node_types.append((record["label"], record["count"]))
                logger.info(f"   {record['label']}: {record['count']} nodes")
            
            # Check for recent entities (from our demo)
            logger.info("")
            logger.info("4. Checking recent entity creation...")
            recent_result = session.run("""
                MATCH (n:__KGBuilder__)
                WHERE n.__tmp_internal_id IS NOT NULL
                RETURN count(n) as total_recent,
                       collect(DISTINCT [l IN labels(n) WHERE l <> '__KGBuilder__'][0])[0..10] as sample_types
            """)
            
            recent_record = recent_result.single()
            if recent_record:
                logger.info(f"   Total recent entities: {recent_record['total_recent']}")
                logger.info(f"   Sample types: {recent_record['sample_types']}")
            
        driver.close()
        
        # Now test a single document extraction to see detailed output
        logger.info("")
        logger.info("5. Testing single document extraction with detailed logging...")
        
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import EnhancedGraphRAGPipeline
        from courtlistener.processing.src.processing.cost_monitor import CostMonitor
        
        cost_monitor = CostMonitor()
        
        # Simple test document
        test_document = {
            "id": "detailed_test_001",
            "case_name": "Johnson v. Smith Law Firm",
            "court": {"name": "Dallas County District Court"},
            "date_filed": "2023-09-15",
            "docket_number": "2023-TEST-001",
            "plain_text": """
            In this legal malpractice case, plaintiff Robert Johnson sued defendant Smith Law Firm 
            for professional negligence. Judge Mary Williams presided over the trial in the 
            95th Judicial District Court of Dallas County, Texas.
            
            Attorney Lisa Chen represented the plaintiff, while Attorney David Rodriguez represented 
            the defendant law firm. The case involved allegations that Smith Law Firm failed to file 
            a timely appeal in Johnson's underlying personal injury case.
            
            Expert witness Dr. Patricia Davis, a legal ethics professor at SMU Law School, testified 
            regarding the standard of care for appellate practice. The jury awarded plaintiff $125,000 
            in damages for the lost opportunity to appeal.
            """
        }
        
        # Create pipeline with debug logging
        pipeline = EnhancedGraphRAGPipeline(
            cost_monitor=cost_monitor,
            neo4j_uri=neo4j_uri,
            neo4j_user=neo4j_user,
            neo4j_password=neo4j_password,
            gemini_api_key=os.getenv("GEMINI_API_KEY", "unused"),
            voyage_api_key=os.getenv("VOYAGE_API_KEY"),
            practice_area="personal_injury"
        )
        
        # Process single document
        logger.info("   Processing single test document...")
        result = await pipeline._process_single_document(test_document)
        
        logger.info(f"   Single document result:")
        logger.info(f"   Entities extracted: {result['entities_count']}")
        logger.info(f"   Relationships extracted: {result['relationships_count']}")
        
        # Check what was actually created for this document
        with driver.session() as session:
            # Count entities created in the last minute
            count_result = session.run("""
                MATCH (n:__KGBuilder__)
                WHERE n.__tmp_internal_id IS NOT NULL
                RETURN count(n) as recent_count
            """)
            
            recent_count = count_result.single()["recent_count"]
            logger.info(f"   Total entities in Neo4j after test: {recent_count}")
            
            # Show sample of what was created
            sample_result = session.run("""
                MATCH (n:__KGBuilder__)
                WHERE n.__tmp_internal_id IS NOT NULL
                RETURN n.name as name, [l IN labels(n) WHERE l <> '__KGBuilder__'][0] as type
                ORDER BY n.__tmp_internal_id DESC
                LIMIT 10
            """)
            
            logger.info("   Sample entities created:")
            for record in sample_result:
                logger.info(f"   - {record['type']}: {record['name']}")
        
        pipeline.close()
        
        logger.info("")
        logger.info("📊 INVESTIGATION SUMMARY:")
        logger.info(f"   Total nodes in Neo4j: {len(nodes)}")
        logger.info(f"   Total relationships: {len(relationships)}")
        logger.info(f"   Node types found: {len(node_types)}")
        logger.info("   Expected entities per document: 8-15 (judges, attorneys, parties, courts, etc.)")
        logger.info("   Actual entities per document: ~1")
        logger.info("")
        logger.info("🤔 POSSIBLE ISSUES:")
        logger.info("   1. LLM might be extracting entities but they're not being stored correctly")
        logger.info("   2. Entity counting logic might be incorrect") 
        logger.info("   3. Neo4j query for counting might be wrong")
        logger.info("   4. Pipeline might be processing but not persisting all extracted entities")
        
        return {
            "total_nodes": len(nodes),
            "total_relationships": len(relationships),
            "node_types": node_types,
            "entities_per_doc_expected": 10,
            "entities_per_doc_actual": 1
        }
        
    except Exception as e:
        logger.error(f"❌ Investigation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(investigate_entity_extraction())
    
    if result:
        print(f"\n📊 Investigation completed")
        print(f"Found {result['total_nodes']} total nodes and {result['total_relationships']} relationships")
        print(f"Entity extraction rate: {result['entities_per_doc_actual']}/{result['entities_per_doc_expected']} per document")
    else:
        print("\n❌ Investigation failed")