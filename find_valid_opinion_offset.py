#!/usr/bin/env python3
"""
Find Valid Opinion Offset Scanner
Efficiently scans the 50GB opinions CSV to find where well-formed Texas opinion rows begin.
"""

import bz2
import csv
import re
import time
from typing import Set

def load_texas_cluster_sample(clusters_file: str, sample_size: int = 10000) -> Set[str]:
    """Load a sample of Texas cluster IDs for fast lookup."""
    print(f"🔍 Loading sample of Texas cluster IDs from {clusters_file}")
    texas_clusters = set()
    
    try:
        with bz2.open(clusters_file, 'rt', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for i, row in enumerate(reader):
                if i >= sample_size:
                    break
                
                # Check for Texas indicators in case name
                case_name = (row.get('case_name') or '').lower()
                case_name_full = (row.get('case_name_full') or '').lower()
                
                texas_indicators = [
                    'texas', 'tex.', 'state of texas', 'tex app', 'tex sup',
                    'dallas', 'houston', 'austin', 'san antonio', 'fort worth'
                ]
                
                text_to_check = f"{case_name} {case_name_full}"
                if any(indicator in text_to_check for indicator in texas_indicators):
                    cluster_id = row.get('id')
                    if cluster_id and cluster_id.isdigit():
                        texas_clusters.add(cluster_id)
        
        print(f"✅ Loaded {len(texas_clusters)} Texas cluster IDs for matching")
        return texas_clusters
        
    except Exception as e:
        print(f"❌ Error loading Texas clusters: {e}")
        return set()

def scan_for_valid_opinions(opinions_file: str, texas_clusters: Set[str], 
                          block_size_mb: int = 100) -> tuple:
    """
    Scan opinions file in blocks to find first well-formed Texas opinion row.
    Returns (byte_offset, line_number, cluster_id) of first match.
    """
    print(f"🔍 Scanning {opinions_file} in {block_size_mb}MB blocks...")
    block_size = block_size_mb * 1024 * 1024  # Convert to bytes
    
    try:
        with bz2.open(opinions_file, 'rb') as f:
            # Skip header
            header_line = f.readline()
            current_offset = f.tell()
            
            block_num = 0
            total_lines_scanned = 0
            
            while True:
                block_num += 1
                block_start_offset = current_offset
                
                print(f"📊 Scanning block {block_num} starting at byte {current_offset:,}")
                
                # Read block
                block_data = f.read(block_size)
                if not block_data:
                    break
                
                # Decode and split into lines
                try:
                    block_text = block_data.decode('utf-8', errors='ignore')
                except:
                    print(f"⚠️  Decoding issues in block {block_num}, skipping...")
                    current_offset = f.tell()
                    continue
                
                lines = block_text.split('\n')
                line_offset = block_start_offset
                
                for i, line in enumerate(lines):
                    total_lines_scanned += 1
                    line = line.strip()
                    
                    if not line or not line.startswith('"'):
                        line_offset += len(line.encode('utf-8')) + 1
                        continue
                    
                    # Try to extract cluster_id (last field)
                    cluster_match = re.search(r',\"?([0-9]+)\"?\s*$', line)
                    if cluster_match:
                        cluster_id = cluster_match.group(1)
                        
                        # Check if this cluster_id is in our Texas set
                        if cluster_id in texas_clusters:
                            print(f"🎯 FOUND TEXAS OPINION!")
                            print(f"   Byte offset: {line_offset:,}")
                            print(f"   Line number: ~{total_lines_scanned:,}")
                            print(f"   Cluster ID: {cluster_id}")
                            print(f"   Sample line: {line[:100]}...")
                            return line_offset, total_lines_scanned, cluster_id
                    
                    line_offset += len(line.encode('utf-8')) + 1
                
                current_offset = f.tell()
                
                # Progress update every 10 blocks
                if block_num % 10 == 0:
                    gb_scanned = (current_offset / (1024**3))
                    print(f"📈 Progress: {block_num} blocks, {gb_scanned:.1f}GB scanned, {total_lines_scanned:,} lines")
        
        print(f"❌ No Texas opinions found in entire file ({total_lines_scanned:,} lines scanned)")
        return None, total_lines_scanned, None
        
    except Exception as e:
        print(f"❌ Error scanning file: {e}")
        return None, 0, None

def main():
    """Main scanner function."""
    print("🚀 Texas Opinion Offset Scanner")
    print("=" * 50)
    
    clusters_file = "bulk_csv/opinion-clusters-2025-07-02.csv.bz2"
    opinions_file = "bulk_csv/opinions-2025-07-02.csv.bz2"
    
    start_time = time.time()
    
    # Step 1: Load Texas cluster sample
    texas_clusters = load_texas_cluster_sample(clusters_file, sample_size=50000)
    if not texas_clusters:
        print("❌ Failed to load Texas clusters")
        return 1
    
    # Step 2: Scan for valid opinions
    offset, lines_scanned, cluster_id = scan_for_valid_opinions(opinions_file, texas_clusters)
    
    elapsed = time.time() - start_time
    
    print(f"\n📊 SCAN RESULTS:")
    print(f"   Time elapsed: {elapsed:.1f}s")
    print(f"   Lines scanned: {lines_scanned:,}")
    
    if offset is not None:
        print(f"   ✅ FOUND VALID OFFSET: {offset:,} bytes")
        print(f"   ✅ First Texas cluster: {cluster_id}")
        print(f"\n🎯 NEXT STEP:")
        print(f"   Use --start-bytes {offset} in the loader")
        return 0
    else:
        print(f"   ❌ No valid Texas opinions found")
        print(f"   💡 May need to implement line-count fixer")
        return 1

if __name__ == "__main__":
    exit(main())
