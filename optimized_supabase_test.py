#!/usr/bin/env python3
"""
Optimized Supabase Connection Test
Fixes timeout issues with efficient query patterns
"""

import asyncio
import os
import time
import logging
from datetime import datetime
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
load_dotenv()

class OptimizedSupabaseTest:
    """
    Test Supabase with optimized queries that avoid timeouts
    """
    
    def __init__(self):
        self.test_id = f"supabase_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"🔧 Optimized Supabase Test: {self.test_id}")
    
    async def test_connection_only(self):
        """Test basic connection without expensive operations"""
        try:
            from supabase import create_client
            
            supabase_url = os.getenv("SUPABASE_URL")
            supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
            
            if not supabase_url or not supabase_key:
                return {"success": False, "error": "Missing Supabase credentials"}
            
            client = create_client(supabase_url, supabase_key)
            
            # Simple connection test - just get table schema info
            start_time = time.time()
            
            # This is much faster than COUNT(*) - just checks if table exists and gets first row
            result = client.table("cases").select("id").limit(1).execute()
            
            response_time = (time.time() - start_time) * 1000  # Convert to ms
            
            # Check if we got data
            has_data = len(result.data) > 0
            first_case_id = result.data[0]["id"] if has_data else None
            
            return {
                "success": True,
                "connection": "working",
                "response_time_ms": round(response_time, 2),
                "table_accessible": True,
                "has_data": has_data,
                "sample_case_id": first_case_id,
                "url": supabase_url[:30] + "..."
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_efficient_queries(self):
        """Test efficient query patterns that won't timeout"""
        try:
            from supabase import create_client
            
            client = create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_SERVICE_ROLE_KEY"))
            
            tests = {}
            
            # Test 1: Simple row retrieval (should be fast)
            start_time = time.time()
            result = client.table("cases").select("id", "case_name").limit(5).execute()
            tests["row_retrieval"] = {
                "success": True,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "rows_returned": len(result.data)
            }
            
            # Test 2: Filtered query (with index if available)
            start_time = time.time()
            result = client.table("cases").select("id", "case_name").eq("jurisdiction", "TX").limit(3).execute()
            tests["filtered_query"] = {
                "success": True,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "rows_returned": len(result.data)
            }
            
            # Test 3: Insert capability (create a test record)
            start_time = time.time()
            test_case = {
                "case_name": f"Test Case {self.test_id}",
                "jurisdiction": "TX",
                "practice_area": "test",
                "word_count": 100,
                "source": "test_connection"
            }
            
            insert_result = client.table("cases").insert(test_case).execute()
            tests["insert_capability"] = {
                "success": len(insert_result.data) > 0,
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "inserted_id": insert_result.data[0]["id"] if insert_result.data else None
            }
            
            # Test 4: Update capability (modify the test record)
            if tests["insert_capability"]["success"]:
                start_time = time.time()
                test_id = tests["insert_capability"]["inserted_id"]
                
                update_result = client.table("cases").update({
                    "case_name": f"Updated Test Case {self.test_id}"
                }).eq("id", test_id).execute()
                
                tests["update_capability"] = {
                    "success": len(update_result.data) > 0,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "updated_rows": len(update_result.data)
                }
                
                # Test 5: Delete the test record (cleanup)
                start_time = time.time()
                delete_result = client.table("cases").delete().eq("id", test_id).execute()
                tests["delete_capability"] = {
                    "success": len(delete_result.data) > 0,
                    "response_time_ms": round((time.time() - start_time) * 1000, 2),
                    "deleted_rows": len(delete_result.data)
                }
            
            return {"success": True, "tests": tests}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_index_optimization(self):
        """Test if proper indexes exist for common queries"""
        try:
            from supabase import create_client
            
            client = create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_SERVICE_ROLE_KEY"))
            
            # Test queries that should be fast with proper indexing
            index_tests = {}
            
            # Test jurisdiction-based queries (should have index)
            start_time = time.time()
            result = client.table("cases").select("id").eq("jurisdiction", "TX").limit(1).execute()
            index_tests["jurisdiction_index"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "fast_query": (time.time() - start_time) < 1.0  # Should be under 1 second
            }
            
            # Test practice_area queries (should have index)
            start_time = time.time()
            result = client.table("cases").select("id").eq("practice_area", "personal_injury").limit(1).execute()
            index_tests["practice_area_index"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "fast_query": (time.time() - start_time) < 1.0
            }
            
            # Test source-based queries 
            start_time = time.time()
            result = client.table("cases").select("id").eq("source", "courtlistener").limit(1).execute()
            index_tests["source_index"] = {
                "response_time_ms": round((time.time() - start_time) * 1000, 2),
                "fast_query": (time.time() - start_time) < 1.0
            }
            
            return {"success": True, "index_tests": index_tests}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def run_complete_optimization_test(self):
        """Run complete optimized Supabase test suite"""
        
        logger.info("🚀 RUNNING OPTIMIZED SUPABASE TEST SUITE")
        logger.info("=" * 60)
        
        results = {
            "test_id": self.test_id,
            "timestamp": datetime.now().isoformat(),
            "overall_success": False
        }
        
        # Test 1: Basic Connection
        logger.info("🔍 Testing basic connection...")
        connection_result = await self.test_connection_only()
        results["connection_test"] = connection_result
        
        if connection_result["success"]:
            logger.info(f"✅ Connection: {connection_result['response_time_ms']}ms")
        else:
            logger.error(f"❌ Connection failed: {connection_result['error']}")
            return results
        
        # Test 2: Efficient Queries
        logger.info("🔍 Testing efficient query patterns...")
        query_result = await self.test_efficient_queries()
        results["query_tests"] = query_result
        
        if query_result["success"]:
            logger.info("✅ Query patterns: All operations working")
            for test_name, test_result in query_result["tests"].items():
                logger.info(f"   {test_name}: {test_result.get('response_time_ms', 0)}ms")
        else:
            logger.error(f"❌ Query tests failed: {query_result['error']}")
        
        # Test 3: Index Optimization
        logger.info("🔍 Testing index performance...")
        index_result = await self.test_index_optimization()
        results["index_tests"] = index_result
        
        if index_result["success"]:
            fast_queries = sum(1 for test in index_result["index_tests"].values() if test["fast_query"])
            total_queries = len(index_result["index_tests"])
            logger.info(f"✅ Index performance: {fast_queries}/{total_queries} queries under 1 second")
        else:
            logger.error(f"❌ Index tests failed: {index_result['error']}")
        
        # Overall success determination
        results["overall_success"] = (
            connection_result["success"] and 
            query_result["success"] and 
            index_result["success"]
        )
        
        return results

async def main():
    """Execute optimized Supabase testing"""
    
    print("🔧 OPTIMIZED SUPABASE CONNECTION TEST")
    print("=" * 50)
    print("Testing optimized query patterns to avoid timeouts")
    print()
    
    try:
        tester = OptimizedSupabaseTest()
        results = await tester.run_complete_optimization_test()
        
        print(f"\n🏆 OPTIMIZED SUPABASE TEST RESULTS")
        print("=" * 40)
        print(f"Overall Success: {'✅ YES' if results['overall_success'] else '❌ NO'}")
        
        if results.get("connection_test", {}).get("success"):
            conn = results["connection_test"]
            print(f"Connection Time: {conn['response_time_ms']}ms")
            print(f"Data Available: {'Yes' if conn['has_data'] else 'No'}")
        
        if results.get("query_tests", {}).get("success"):
            print("All CRUD operations: ✅ Working")
        
        if results.get("index_tests", {}).get("success"):
            index_tests = results["index_tests"]["index_tests"]
            fast_count = sum(1 for test in index_tests.values() if test["fast_query"])
            print(f"Fast queries: {fast_count}/{len(index_tests)}")
        
        # Save results
        import json
        filename = f"optimized_supabase_test_{tester.test_id}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📄 Detailed results: {filename}")
        
        if results["overall_success"]:
            print("\n🎉 SUCCESS: Supabase optimization complete!")
            print("   All query patterns working efficiently")
        else:
            print("\n⚠️ Issues found - review detailed results")
            
        return results["overall_success"]
        
    except Exception as e:
        print(f"❌ Optimization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)