#!/usr/bin/env python3
"""
Enhanced Legal Processing Pipeline
Combines GCS/Supabase data with GraphRAG + optional LangExtract enhancement
"""

import asyncio
import time
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from dotenv import load_dotenv

from legal_data_reader import LegalDataReader, LegalDocument

load_dotenv()

@dataclass
class ProcessingResult:
    """Result of processing a legal document"""
    document_id: str
    case_name: str
    processing_type: str  # "standard" or "enhanced"
    processing_time: float
    graphrag_results: Dict[str, Any]
    langextract_results: Optional[Dict[str, Any]] = None
    final_results: Optional[Dict[str, Any]] = None
    complexity_analysis: Optional[Dict[str, Any]] = None
    improvement_metrics: Optional[Dict[str, Any]] = None
    errors: List[str] = None

class DocumentComplexityClassifier:
    """Determine if document needs LangExtract enhancement"""
    
    def analyze_complexity(self, document: LegalDocument, 
                         graphrag_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze document characteristics and GraphRAG results
        to determine if LangExtract enhancement would improve accuracy
        """
        import re
        
        complexity_factors = {
            # Citation density - indicates complex legal reasoning
            "citation_count": len(re.findall(r'\d+\s+S\.W\.\d+d?\s+\d+', getattr(document, 'plain_text', getattr(document, 'content', '')))),
            
            # Entity confidence from GraphRAG
            "low_confidence_entities": sum(1 for e in graphrag_results.get('entities', []) 
                                         if e.get('confidence', 1.0) < 0.7),
            
            # Document characteristics
            "document_length": len(getattr(document, 'plain_text', getattr(document, 'content', ''))),
            "court_complexity": self._assess_court_complexity(document),
            "party_count": self._count_parties(document),
            "damage_complexity": self._assess_damages(document),
            
            # Practice area complexity
            "practice_area_complexity": self._get_practice_area_score(document.practice_area)
        }
        
        # Calculate complexity score (0.0 to 1.0)
        complexity_score = self._calculate_complexity_score(complexity_factors)
        
        return {
            "complexity_score": complexity_score,
            "needs_enhancement": complexity_score > 0.6,
            "factors": complexity_factors,
            "enhancement_reason": self._get_enhancement_reason(complexity_factors)
        }
    
    def _assess_court_complexity(self, document) -> float:
        """Assess court complexity (appellate courts = more complex)"""
        court_text = (getattr(document, 'court_name', getattr(document, 'court_id', '')) or "").lower()
        
        if any(term in court_text for term in ["supreme", "court of appeals", "appellate"]):
            return 1.0
        elif any(term in court_text for term in ["district", "county"]):
            return 0.5
        else:
            return 0.3
    
    def _count_parties(self, document) -> int:
        """Count approximate number of parties"""
        import re
        
        # Simple heuristic: count "plaintiff" and "defendant" mentions
        text = getattr(document, 'plain_text', getattr(document, 'content', '')).lower()
        plaintiff_matches = len(re.findall(r'plaintiff[s]?', text))
        defendant_matches = len(re.findall(r'defendant[s]?', text))
        
        return min(plaintiff_matches + defendant_matches, 10)  # Cap at 10
    
    def _assess_damages(self, document) -> float:
        """Assess damage complexity based on monetary amounts"""
        import re
        
        # Find monetary amounts
        money_pattern = r'\$[\d,]+(?:\.\d{2})?'
        amounts = re.findall(money_pattern, getattr(document, 'plain_text', getattr(document, 'content', '')))
        
        if not amounts:
            return 0.0
        
        # Convert to numbers and assess complexity
        try:
            values = []
            for amount in amounts:
                clean_amount = amount.replace('$', '').replace(',', '')
                values.append(float(clean_amount))
            
            max_amount = max(values) if values else 0
            
            if max_amount > 1000000:  # > $1M
                return 1.0
            elif max_amount > 100000:  # > $100K
                return 0.7
            elif max_amount > 10000:   # > $10K
                return 0.4
            else:
                return 0.2
                
        except:
            return 0.3
    
    def _get_practice_area_score(self, practice_area: str) -> float:
        """Get complexity score for practice area"""
        complexity_map = {
            "mass_tort": 1.0,
            "class_action": 1.0,
            "complex_litigation": 0.9,
            "commercial": 0.7,
            "personal_injury": 0.5,
            "family_law": 0.4,
            "criminal_defense": 0.6,
            "immigration_law": 0.4,
            "bankruptcy": 0.6,
            "real_estate": 0.3,
            "estate_planning": 0.3
        }
        return complexity_map.get(practice_area, 0.5)
    
    def _calculate_complexity_score(self, factors: Dict[str, Any]) -> float:
        """Calculate overall complexity score"""
        weights = {
            "citation_count": 0.2,
            "low_confidence_entities": 0.15,
            "document_length": 0.1,
            "court_complexity": 0.15,
            "party_count": 0.1,
            "damage_complexity": 0.15,
            "practice_area_complexity": 0.15
        }
        
        normalized_factors = {
            "citation_count": min(factors["citation_count"] / 5.0, 1.0),
            "low_confidence_entities": min(factors["low_confidence_entities"] / 3.0, 1.0),
            "document_length": min(factors["document_length"] / 10000.0, 1.0),
            "court_complexity": factors["court_complexity"],
            "party_count": min(factors["party_count"] / 6.0, 1.0),
            "damage_complexity": factors["damage_complexity"],
            "practice_area_complexity": factors["practice_area_complexity"]
        }
        
        score = sum(weights[key] * normalized_factors[key] for key in weights.keys())
        return min(score, 1.0)
    
    def _get_enhancement_reason(self, factors: Dict[str, Any]) -> str:
        """Get human-readable reason for enhancement"""
        reasons = []
        
        if factors["citation_count"] > 3:
            reasons.append("High citation density")
        if factors["low_confidence_entities"] > 2:
            reasons.append("Low entity confidence")
        if factors["document_length"] > 8000:
            reasons.append("Long document")
        if factors["party_count"] > 4:
            reasons.append("Multi-party case")
        if factors["damage_complexity"] > 0.7:
            reasons.append("High-value damages")
        if factors["practice_area_complexity"] > 0.8:
            reasons.append("Complex practice area")
        
        return "; ".join(reasons) if reasons else "Standard complexity"

class EnhancedLegalPipeline:
    """Main pipeline combining GraphRAG + LangExtract"""
    
    def __init__(self):
        self.data_reader = LegalDataReader()
        self.complexity_classifier = DocumentComplexityClassifier()
        self._setup_processors()
    
    def _setup_processors(self):
        """Initialize GraphRAG and LangExtract processors"""
        try:
            # Import GraphRAG pipeline
            from setup_legal_graphrag import LegalGraphRAGPipeline
            self.graphrag_pipeline = LegalGraphRAGPipeline()
            print("✅ GraphRAG pipeline initialized")
            
            # Setup LangExtract (lazy load)
            self.langextract_ready = False
            self._setup_langextract()
            
        except Exception as e:
            print(f"❌ Failed to setup processors: {e}")
            self.graphrag_pipeline = None
    
    def _setup_langextract(self):
        """Setup LangExtract with legal examples"""
        try:
            import langextract
            from langextract import data
            
            # Create comprehensive legal examples
            example_text = """
            Judge Robert Wilson presided over the case Smith v. Jones in Harris County District Court. 
            Attorney Sarah Brown represented plaintiff John Smith against defendant Mary Jones. 
            The court awarded $25,000 in compensatory damages and $10,000 in punitive damages.
            The case was filed on January 15, 2023, and decided on March 20, 2023.
            """
            
            extractions = [
                data.Extraction("JUDGE", "Robert Wilson", description="Presiding judge"),
                data.Extraction("CASE", "Smith v. Jones", description="Case name"),
                data.Extraction("COURT", "Harris County District Court", description="Court venue"),
                data.Extraction("ATTORNEY", "Sarah Brown", description="Representing attorney"),
                data.Extraction("PLAINTIFF", "John Smith", description="Plaintiff party"),
                data.Extraction("DEFENDANT", "Mary Jones", description="Defendant party"),
                data.Extraction("MONEY", "$25,000", description="Compensatory damages"),
                data.Extraction("MONEY", "$10,000", description="Punitive damages"),
                data.Extraction("DATE", "January 15, 2023", description="Filing date"),
                data.Extraction("DATE", "March 20, 2023", description="Decision date")
            ]
            
            self.langextract_examples = [data.ExampleData(
                text=example_text,
                extractions=extractions
            )]
            
            self.langextract_ready = True
            print("✅ LangExtract examples initialized")
            
        except Exception as e:
            print(f"⚠️  LangExtract setup failed: {e}")
            self.langextract_ready = False
    
    async def process_document(self, document: LegalDocument) -> ProcessingResult:
        """
        Process document through GraphRAG + optional LangExtract enhancement
        """
        start_time = time.time()
        errors = []
        
        print(f"🔄 Processing: {document.case_name}")
        
        try:
            # Step 1: Always run GraphRAG (PRIMARY)
            print("   📊 Running GraphRAG extraction...")
            graphrag_results = await self._run_graphrag(document)
            
            # Step 2: Analyze complexity
            print("   🧮 Analyzing document complexity...")
            complexity_analysis = self.complexity_classifier.analyze_complexity(
                document, graphrag_results
            )
            
            # Step 3: Enhance if needed
            langextract_results = None
            final_results = graphrag_results
            improvement_metrics = None
            processing_type = "standard"
            
            if complexity_analysis["needs_enhancement"] and self.langextract_ready:
                print(f"   🚀 Enhancement needed: {complexity_analysis['enhancement_reason']}")
                
                try:
                    langextract_results = await self._run_langextract(document)
                    
                    if langextract_results:
                        # Step 4: Fuse results for improved accuracy
                        final_results = await self._fuse_results(
                            graphrag_results, langextract_results, document
                        )
                        
                        improvement_metrics = self._calculate_improvement_metrics(
                            graphrag_results, final_results
                        )
                        processing_type = "enhanced"
                        print("   ✨ Enhancement completed")
                
                except Exception as e:
                    errors.append(f"Enhancement failed: {e}")
                    print(f"   ⚠️  Enhancement failed, using standard results: {e}")
            else:
                print(f"   ✅ Standard processing: {complexity_analysis['enhancement_reason']}")
            
            processing_time = time.time() - start_time
            
            result = ProcessingResult(
                document_id=document.id,
                case_name=document.case_name,
                processing_type=processing_type,
                processing_time=processing_time,
                graphrag_results=graphrag_results,
                langextract_results=langextract_results,
                final_results=final_results,
                complexity_analysis=complexity_analysis,
                improvement_metrics=improvement_metrics,
                errors=errors
            )
            
            # Update Supabase with results
            await self._update_document_status(document.id, result)
            
            print(f"   ✅ Completed in {processing_time:.2f}s ({processing_type})")
            
            return result
            
        except Exception as e:
            errors.append(str(e))
            processing_time = time.time() - start_time
            
            print(f"   ❌ Processing failed: {e}")
            
            return ProcessingResult(
                document_id=document.id,
                case_name=document.case_name,
                processing_type="error",
                processing_time=processing_time,
                graphrag_results={},
                errors=errors
            )
    
    async def _run_graphrag(self, document: LegalDocument) -> Dict[str, Any]:
        """Run GraphRAG extraction"""
        if not self.graphrag_pipeline:
            raise Exception("GraphRAG pipeline not initialized")
        
        # Convert to format expected by GraphRAG pipeline
        doc_dict = {
            "id": document.id,
            "plain_text": getattr(document, 'plain_text', getattr(document, 'content', '')),
            "case_name": getattr(document, 'case_name', 'Unknown'),
            "practice_area": getattr(document, 'practice_area', 'unknown'),
            "court_name": getattr(document, 'court_name', getattr(document, 'court_id', 'unknown')),
            "file_date": getattr(document, 'file_date', getattr(document, 'date_filed', None))
        }
        
        return await self.graphrag_pipeline.process_legal_document(doc_dict)
    
    async def _run_langextract(self, document: LegalDocument) -> Dict[str, Any]:
        """Run LangExtract extraction"""
        import langextract
        import os
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            raise Exception("No GEMINI_API_KEY found")
        
        # Perform LangExtract extraction
        result = langextract.extract(
            text_or_documents=getattr(document, 'plain_text', getattr(document, 'content', '')),
            examples=self.langextract_examples,
            prompt_description="Extract comprehensive legal information including judges, attorneys, parties, courts, monetary awards, dates, and case information from court documents.",
            api_key=api_key,
            temperature=0.0,
            model_id='gemini-2.5-flash',
            debug=False
        )
        
        # Convert to standardized format
        extractions = []
        if hasattr(result, 'extractions') and result.extractions:
            for extraction in result.extractions:
                extractions.append({
                    "type": extraction.extraction_class,
                    "text": extraction.extraction_text,
                    "confidence": 0.9  # LangExtract doesn't provide confidence
                })
        
        return {
            "extractions": extractions,
            "total_extractions": len(extractions)
        }
    
    async def _fuse_results(self, graphrag_results: Dict, 
                          langextract_results: Dict,
                          document: LegalDocument) -> Dict[str, Any]:
        """Intelligently merge GraphRAG and LangExtract results"""
        
        # Keep ALL GraphRAG relationships (critical for graph)
        final_relationships = graphrag_results.get('relationships', [])
        
        # Start with GraphRAG entities
        graphrag_entities = graphrag_results.get('entities', [])
        langextract_extractions = langextract_results.get('extractions', [])
        
        # Enhance entities with LangExtract precision
        enhanced_entities = []
        
        for graphrag_entity in graphrag_entities:
            # Find matching LangExtract entity
            match = self._find_matching_entity(graphrag_entity, langextract_extractions)
            
            if match:
                # Enhance with LangExtract precision
                enhanced_entity = {
                    **graphrag_entity,
                    "enhanced": True,
                    "langextract_text": match["text"],
                    "confidence": max(
                        graphrag_entity.get('confidence', 0.0),
                        match.get('confidence', 0.9)
                    ),
                    "extraction_source": "graphrag+langextract"
                }
                enhanced_entities.append(enhanced_entity)
            else:
                # Keep original GraphRAG entity
                enhanced_entities.append({
                    **graphrag_entity,
                    "enhanced": False,
                    "extraction_source": "graphrag_only"
                })
        
        # Add any new entities found only by LangExtract
        additional_entities = []
        for extraction in langextract_extractions:
            if not self._entity_exists_in_graphrag(extraction, graphrag_entities):
                additional_entities.append({
                    "id": f"langextract_{len(additional_entities)}",
                    "type": extraction["type"],
                    "text": extraction["text"],
                    "confidence": extraction.get("confidence", 0.9),
                    "extraction_source": "langextract_only",
                    "enhanced": False
                })
        
        return {
            "entities": enhanced_entities + additional_entities,
            "relationships": final_relationships,
            "fusion_metadata": {
                "graphrag_entity_count": len(graphrag_entities),
                "langextract_entity_count": len(langextract_extractions),
                "final_entity_count": len(enhanced_entities + additional_entities),
                "entities_enhanced": len([e for e in enhanced_entities if e.get("enhanced")]),
                "entities_added": len(additional_entities)
            }
        }
    
    def _find_matching_entity(self, graphrag_entity: Dict, 
                             langextract_extractions: List[Dict]) -> Optional[Dict]:
        """Find matching LangExtract entity for GraphRAG entity"""
        graphrag_text = graphrag_entity.get('text', '').lower()
        graphrag_type = graphrag_entity.get('type', '').lower()
        
        for extraction in langextract_extractions:
            extraction_text = extraction.get('text', '').lower()
            extraction_type = extraction.get('type', '').lower()
            
            # Type mapping
            type_matches = (
                (graphrag_type == extraction_type) or
                (graphrag_type == 'judge' and extraction_type == 'judge') or
                (graphrag_type == 'attorney' and extraction_type == 'attorney') or
                (graphrag_type == 'damages' and extraction_type == 'money')
            )
            
            # Text similarity (simple overlap check)
            if type_matches and (
                graphrag_text in extraction_text or 
                extraction_text in graphrag_text or
                self._calculate_text_similarity(graphrag_text, extraction_text) > 0.7
            ):
                return extraction
        
        return None
    
    def _entity_exists_in_graphrag(self, extraction: Dict, 
                                 graphrag_entities: List[Dict]) -> bool:
        """Check if LangExtract entity already exists in GraphRAG results"""
        extraction_text = extraction.get('text', '').lower()
        
        for entity in graphrag_entities:
            entity_text = entity.get('text', '').lower()
            if (extraction_text in entity_text or 
                entity_text in extraction_text or
                self._calculate_text_similarity(extraction_text, entity_text) > 0.8):
                return True
        
        return False
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Simple text similarity calculation"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _calculate_improvement_metrics(self, original_results: Dict, 
                                     enhanced_results: Dict) -> Dict[str, Any]:
        """Calculate improvement metrics from enhancement"""
        original_entities = len(original_results.get('entities', []))
        enhanced_entities = len(enhanced_results.get('entities', []))
        
        fusion_metadata = enhanced_results.get('fusion_metadata', {})
        
        return {
            "entity_count_improvement": enhanced_entities - original_entities,
            "entities_enhanced": fusion_metadata.get('entities_enhanced', 0),
            "entities_added": fusion_metadata.get('entities_added', 0),
            "enhancement_ratio": fusion_metadata.get('entities_enhanced', 0) / max(original_entities, 1)
        }
    
    async def _update_document_status(self, doc_id: str, result: ProcessingResult):
        """Update document processing status in Supabase"""
        try:
            if result.errors:
                status = "error"
            elif result.processing_type == "enhanced":
                status = "enhanced"
            else:
                status = "processed"
            
            self.data_reader.mark_processing_status(
                doc_id=doc_id,
                status=status,
                graphrag_results=result.graphrag_results,
                enhancement_results={
                    "processing_type": result.processing_type,
                    "improvement_metrics": result.improvement_metrics
                } if result.improvement_metrics else None,
                processing_metadata={
                    "processing_time": result.processing_time,
                    "complexity_score": result.complexity_analysis.get('complexity_score', 0.0) if result.complexity_analysis else 0.0
                }
            )
        except Exception as e:
            print(f"⚠️  Failed to update document status: {e}")

async def test_enhanced_pipeline():
    """Test the enhanced pipeline with real data"""
    print("=== Testing Enhanced Legal Pipeline ===\n")
    
    try:
        pipeline = EnhancedLegalPipeline()
        
        if not pipeline.graphrag_pipeline:
            print("❌ GraphRAG pipeline not available")
            return
        
        # Test 1: Get documents from Supabase/GCS
        print("1. Fetching documents from GCS/Supabase...")
        documents = await pipeline.data_reader.get_documents_for_processing(
            batch_size=3,
            priority_filter="unprocessed",
            practice_area="personal_injury"
        )
        
        if not documents:
            print("   ⚠️  No documents found, cannot test pipeline")
            return
        
        # Test 2: Process each document
        results = []
        for doc in documents:
            print(f"\n2. Processing document: {doc.case_name}")
            result = await pipeline.process_document(doc)
            results.append(result)
            
            # Print summary
            print(f"   📊 Entities: {len(result.final_results.get('entities', []))}")
            print(f"   🔗 Relationships: {len(result.final_results.get('relationships', []))}")
            print(f"   ⏱️  Time: {result.processing_time:.2f}s")
            print(f"   🎯 Type: {result.processing_type}")
        
        # Test 3: Summary
        print(f"\n=== Pipeline Test Summary ===")
        print(f"Documents processed: {len(results)}")
        print(f"Standard processing: {len([r for r in results if r.processing_type == 'standard'])}")
        print(f"Enhanced processing: {len([r for r in results if r.processing_type == 'enhanced'])}")
        print(f"Errors: {len([r for r in results if r.errors])}")
        
        avg_time = sum(r.processing_time for r in results) / len(results)
        print(f"Average processing time: {avg_time:.2f}s")
        
        return results
        
    except Exception as e:
        print(f"❌ Enhanced pipeline test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_enhanced_pipeline())