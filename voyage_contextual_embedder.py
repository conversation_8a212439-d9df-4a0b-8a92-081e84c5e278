#!/usr/bin/env python3
"""
Voyage-Context-3 Contextual Embedder
Optimized for legal document processing with global context awareness
"""

import os
import asyncio
import logging
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import json

import voyageai
from neo4j_graphrag.embeddings.base import Embedder
import pinecone
from pinecone import Pinecone, ServerlessSpec
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

@dataclass
class ContextualChunk:
    """Enhanced chunk with legal context for voyage-context-3"""
    id: str
    text: str
    document_context: Dict[str, Any]
    section_index: int
    chunk_index: int
    legal_section_type: Optional[str]
    word_count: int
    
    @property
    def is_processable(self) -> bool:
        """Check if chunk has sufficient content for embedding"""
        return len(self.text.strip()) >= 50 and self.word_count >= 10

@dataclass
class EmbeddedChunk:
    """Chunk with voyage-context-3 contextual embedding"""
    id: str
    text: str
    embedding: List[float]
    document_context: Dict[str, Any]
    legal_section_type: Optional[str]
    embedding_model: str
    embedding_dimension: int
    processing_timestamp: datetime
    
    @property
    def metadata(self) -> Dict[str, Any]:
        """Generate metadata for storage systems"""
        return {
            "chunk_id": self.id,
            "case_name": self.document_context.get("case_name", "Unknown"),
            "court": self.document_context.get("court", "Unknown"),
            "jurisdiction": self.document_context.get("jurisdiction", "Unknown"),
            "practice_area": self.document_context.get("practice_area", "Unknown"),
            "legal_section": self.legal_section_type or "general",
            "text_length": len(self.text),
            "embedding_model": self.embedding_model,
            "embedding_dimension": self.embedding_dimension,
            "processed_at": self.processing_timestamp.isoformat()
        }

class ContextAwareLegalChunker:
    """
    Legal document chunker optimized for voyage-context-3 contextual processing
    """
    
    def __init__(self, chunk_size: int = 2000, overlap: int = 200):
        self.chunk_size = chunk_size
        self.overlap = overlap
        
        # Legal document structure patterns
        self.legal_separators = [
            "\n\n### HOLDING:",     # Court holdings
            "\n\n### DISPOSITION:", # Court dispositions  
            "\n\nHELD:",           # Legal holdings
            "\n\nCONCLUSION:",     # Legal conclusions
            "\n\nWHEREFORE:",      # Prayer for relief
            "\n\nIT IS ORDERED:",  # Court orders
            "\n\nORDERED:",        # Alternative order format
            "\n\nAFFIRMED:",       # Affirmations
            "\n\nREVERSED:",       # Reversals
            "\n\nREMANDED:",       # Remands
            "\n\n---",             # Section breaks
            "\n\n",                # Paragraph breaks
        ]
        
        # Legal section type patterns
        self.section_patterns = {
            "holding": ["HELD:", "HOLDING:", "WE HOLD"],
            "disposition": ["DISPOSITION:", "AFFIRMED", "REVERSED", "REMANDED"],
            "facts": ["FACTS:", "BACKGROUND:", "PROCEDURAL HISTORY"],
            "analysis": ["ANALYSIS:", "DISCUSSION:", "LEGAL STANDARD"],
            "conclusion": ["CONCLUSION:", "WHEREFORE:", "IT IS ORDERED"],
            "opinion": ["OPINION:", "MEMORANDUM OPINION", "PER CURIAM"]
        }
    
    async def create_contextual_chunks(self, 
                                     document: Dict[str, Any]) -> List[ContextualChunk]:
        """
        Create legal-aware chunks optimized for voyage-context-3 processing
        """
        
        text = document.get("content") or document.get("plain_text", "")
        if not text:
            logger.warning(f"No text content found for document {document.get('id')}")
            return []
        
        # 1. Extract document-level context
        document_context = {
            "case_name": document.get("case_name", "Unknown Case"),
            "court": document.get("court_name") or document.get("court", "Unknown Court"),
            "jurisdiction": document.get("jurisdiction", "Unknown"),
            "practice_area": document.get("practice_area", "general"),
            "date_filed": document.get("date_filed"),
            "document_id": document.get("id"),
            "word_count": len(text.split())
        }
        
        # 2. Split by legal structure while preserving context
        sections = self._split_by_legal_structure(text)
        
        chunks = []
        for section_idx, section in enumerate(sections):
            # 3. Create overlapping chunks within each section
            section_chunks = self._chunk_with_overlap(section["text"])
            
            for chunk_idx, chunk_text in enumerate(section_chunks):
                chunk = ContextualChunk(
                    id=f"{document.get('id', 'unknown')}_s{section_idx}_c{chunk_idx}",
                    text=chunk_text,
                    document_context=document_context,
                    section_index=section_idx,
                    chunk_index=chunk_idx,
                    legal_section_type=section["type"],
                    word_count=len(chunk_text.split())
                )
                
                if chunk.is_processable:
                    chunks.append(chunk)
        
        logger.info(f"✅ Created {len(chunks)} contextual chunks for document {document.get('id')}")
        return chunks
    
    def _split_by_legal_structure(self, text: str) -> List[Dict[str, Any]]:
        """Split text by legal document structure"""
        
        sections = []
        current_section = {"text": "", "type": "general"}
        
        lines = text.split('\n')
        
        for line in lines:
            line_upper = line.upper().strip()
            
            # Check if this line indicates a new legal section
            new_section_type = None
            for section_type, patterns in self.section_patterns.items():
                for pattern in patterns:
                    if pattern in line_upper:
                        new_section_type = section_type
                        break
                if new_section_type:
                    break
            
            # If new section found, save current and start new
            if new_section_type and current_section["text"].strip():
                sections.append(current_section)
                current_section = {"text": line + "\n", "type": new_section_type}
            else:
                current_section["text"] += line + "\n"
        
        # Add final section
        if current_section["text"].strip():
            sections.append(current_section)
        
        return sections
    
    def _chunk_with_overlap(self, text: str) -> List[str]:
        """Create overlapping chunks optimized for voyage-context-3"""
        
        if len(text) <= self.chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + self.chunk_size
            
            # Find natural sentence boundary near the end
            if end < len(text):
                # Look for sentence endings within last 200 chars
                sentence_endings = ['. ', '! ', '? ', '.\n', '!\n', '?\n']
                best_end = end
                
                for i in range(max(end - 200, start), end):
                    for ending in sentence_endings:
                        if text[i:i+len(ending)] == ending:
                            best_end = i + len(ending)
                
                end = best_end
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position with overlap
            start = max(start + self.chunk_size - self.overlap, end - self.overlap)
            
            if start >= len(text):
                break
        
        return chunks
    
    def identify_legal_section(self, text: str) -> str:
        """Identify the type of legal section from text content"""
        
        text_upper = text.upper()
        
        for section_type, patterns in self.section_patterns.items():
            for pattern in patterns:
                if pattern in text_upper:
                    return section_type
        
        return "general"

class VoyageContextualEmbedder(Embedder):
    """
    Voyage-Context-3 embedder optimized for legal documents
    Implements Neo4j GraphRAG Embedder interface with Pinecone storage
    """
    
    def __init__(self, 
                 model: str = "voyage-context-3",
                 api_key: Optional[str] = None,
                 output_dimension: int = 1024,
                 enable_pinecone: bool = True):
        
        self.model = model
        self.output_dimension = output_dimension
        self.client = voyageai.Client(api_key=api_key or os.getenv("VOYAGE_API_KEY"))
        self.enable_pinecone = enable_pinecone
        
        if not api_key and not os.getenv("VOYAGE_API_KEY"):
            raise ValueError("Voyage API key required")
        
        # Initialize Pinecone if enabled
        self.pinecone_client = None
        self.pinecone_index = None
        
        if self.enable_pinecone:
            try:
                pinecone_api_key = os.getenv("PINECONE_API_KEY")
                index_name = os.getenv("PINECONE_INDEX_NAME", "texas-laws-voyage3large")
                
                if pinecone_api_key:
                    # Initialize Pinecone client
                    self.pinecone_client = Pinecone(api_key=pinecone_api_key)
                    
                    # Get or create index
                    if index_name not in [index.name for index in self.pinecone_client.list_indexes()]:
                        logger.info(f"Creating Pinecone index: {index_name}")
                        self.pinecone_client.create_index(
                            name=index_name,
                            dimension=self.output_dimension,
                            metric="cosine",
                            spec=ServerlessSpec(
                                cloud="aws",
                                region="us-east-1"
                            )
                        )
                    
                    self.pinecone_index = self.pinecone_client.Index(index_name)
                    logger.info(f"✅ Pinecone index connected: {index_name}")
                else:
                    logger.warning("⚠️  Pinecone API key not found, vector storage disabled")
                    self.enable_pinecone = False
                    
            except Exception as e:
                logger.error(f"❌ Pinecone initialization failed: {e}")
                self.enable_pinecone = False
        
        logger.info(f"✅ Initialized Voyage-Context-3 embedder with {output_dimension}d output"
                   f"{' + Pinecone storage' if self.enable_pinecone else ''}")
    
    def embed_query(self, text: str) -> List[float]:
        """
        Embed single query text (required by Neo4j GraphRAG interface)
        """
        try:
            # Use contextualized embedding for queries with voyage-context-3
            result = self.client.contextualized_embed(
                inputs=[[text]],  # Single query in list format
                model=self.model,
                input_type="query",
                output_dimension=self.output_dimension
            )
            return result.results[0].embeddings[0]
            
        except Exception as e:
            logger.error(f"Error embedding query: {e}")
            return [0.0] * self.output_dimension
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed document texts (required by Neo4j GraphRAG interface)
        This method will be used by GraphRAG for individual chunks
        """
        try:
            # Use contextualized embedding - each text as separate document context
            inputs = [[text] for text in texts]
            result = self.client.contextualized_embed(
                inputs=inputs,
                model=self.model,
                input_type="document",
                output_dimension=self.output_dimension
            )
            return [result_item.embeddings[0] for result_item in result.results]
                
        except Exception as e:
            logger.error(f"Error embedding documents: {e}")
            return [[0.0] * self.output_dimension for _ in texts]
    
    async def embed_document_with_full_context(self, 
                                             chunks: List[ContextualChunk]) -> List[EmbeddedChunk]:
        """
        Embed entire document using voyage-context-3 contextual approach
        This preserves global document context in each chunk embedding
        """
        try:
            if not chunks:
                return []
            
            # Prepare contextualized inputs - all chunks from same document
            chunk_texts = [chunk.text for chunk in chunks]
            contextualized_inputs = [chunk_texts]  # Single document context
            
            logger.info(f"🔄 Embedding {len(chunks)} chunks with voyage-context-3 contextual approach")
            
            # Generate contextualized embeddings in single pass
            result = self.client.contextualized_embed(
                inputs=contextualized_inputs,
                model=self.model,
                input_type="document",
                output_dimension=self.output_dimension
            )
            
            # Map embeddings back to chunks with enhanced context
            embedded_chunks = []
            chunk_embeddings = result.results[0].embeddings
            
            for chunk, embedding in zip(chunks, chunk_embeddings):
                embedded_chunk = EmbeddedChunk(
                    id=chunk.id,
                    text=chunk.text,
                    embedding=embedding,  # Contains global document context
                    document_context=chunk.document_context,
                    legal_section_type=chunk.legal_section_type,
                    embedding_model=self.model,
                    embedding_dimension=self.output_dimension,
                    processing_timestamp=datetime.utcnow()
                )
                embedded_chunks.append(embedded_chunk)
            
            # Step 6: Store vectors in Pinecone if enabled
            if self.enable_pinecone and self.pinecone_index:
                logger.info(f"🔄 Storing {len(embedded_chunks)} vectors in Pinecone...")
                await self._store_vectors_in_pinecone(embedded_chunks)
            
            logger.info(f"✅ Generated {len(embedded_chunks)} contextual embeddings"
                       f"{' and stored in Pinecone' if self.enable_pinecone else ''}")
            return embedded_chunks
            
        except Exception as e:
            logger.error(f"❌ Error in contextual embedding: {e}")
            return []
    
    async def _store_vectors_in_pinecone(self, embedded_chunks: List[EmbeddedChunk]) -> bool:
        """
        Store embedded chunks in Pinecone with metadata
        """
        try:
            vectors_to_upsert = []
            
            for embedded_chunk in embedded_chunks:
                # Create metadata for Pinecone
                metadata = {
                    "chunk_id": embedded_chunk.id,
                    "case_name": embedded_chunk.document_context.get("case_name", "Unknown"),
                    "court": embedded_chunk.document_context.get("court", "Unknown"),
                    "jurisdiction": embedded_chunk.document_context.get("jurisdiction", "Unknown"), 
                    "practice_area": embedded_chunk.document_context.get("practice_area", "Unknown"),
                    "legal_section": embedded_chunk.legal_section_type or "general",
                    "text_preview": embedded_chunk.text[:200],  # First 200 chars for preview
                    "text_length": len(embedded_chunk.text),
                    "word_count": len(embedded_chunk.text.split()),
                    "embedding_model": embedded_chunk.embedding_model,
                    "processed_at": embedded_chunk.processing_timestamp.isoformat()
                }
                
                # Create vector entry
                vector_entry = {
                    "id": embedded_chunk.id,
                    "values": embedded_chunk.embedding,
                    "metadata": metadata
                }
                vectors_to_upsert.append(vector_entry)
            
            # Batch upsert to Pinecone
            batch_size = 100  # Pinecone batch limit
            for i in range(0, len(vectors_to_upsert), batch_size):
                batch = vectors_to_upsert[i:i + batch_size]
                self.pinecone_index.upsert(vectors=batch, namespace="texas-legal-contextual")
            
            logger.info(f"✅ Stored {len(embedded_chunks)} vectors in Pinecone namespace: texas-legal-contextual")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error storing vectors in Pinecone: {e}")
            return False
    
    async def search_similar_chunks(self, 
                                  query_text: str, 
                                  top_k: int = 10,
                                  filter_metadata: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:
        """
        Search for similar chunks in Pinecone using contextual embeddings
        """
        try:
            if not self.enable_pinecone or not self.pinecone_index:
                logger.warning("Pinecone not enabled or available")
                return []
            
            # Generate query embedding
            query_embedding = self.embed_query(query_text)
            
            # Build filter if provided
            pinecone_filter = {}
            if filter_metadata:
                for key, value in filter_metadata.items():
                    pinecone_filter[key] = {"$eq": value}
            
            # Search in Pinecone
            search_results = self.pinecone_index.query(
                vector=query_embedding,
                top_k=top_k,
                include_metadata=True,
                namespace="texas-legal-contextual",
                filter=pinecone_filter if pinecone_filter else None
            )
            
            # Format results
            similar_chunks = []
            for match in search_results['matches']:
                result = {
                    "chunk_id": match['id'],
                    "similarity_score": match['score'],
                    "metadata": match['metadata']
                }
                similar_chunks.append(result)
            
            logger.info(f"✅ Found {len(similar_chunks)} similar chunks for query")
            return similar_chunks
            
        except Exception as e:
            logger.error(f"❌ Error searching Pinecone: {e}")
            return []

# Test function
async def test_voyage_contextual_embedder():
    """Test the voyage contextual embedder with legal document"""
    
    print("=== Testing Voyage-Context-3 Contextual Embedder ===\n")
    
    try:
        # Sample legal document
        sample_document = {
            "id": "test_case_001",
            "case_name": "Regalado v. Rodriguez",
            "court_name": "13th Court of Appeals",
            "jurisdiction": "TX",
            "practice_area": "family_law",
            "content": """NUMBER 13-25-00295-CV

COURT OF APPEALS
THIRTEENTH DISTRICT OF TEXAS
CORPUS CHRISTI – EDINBURG

IN THE MATTER OF THE MARRIAGE OF
SANDRA REGALADO AND SERGIO RODRIGUEZ

On Appeal from the 430th Judicial District Court
Hidalgo County, Texas

MEMORANDUM OPINION

This is a divorce case. Sandra Regalado and Sergio Rodriguez were married. The trial court granted a divorce and divided the community property. Rodriguez appeals the property division.

HELD: The trial court did not abuse its discretion in dividing the community property. The evidence supports the trial court's finding that the property was community property.

DISPOSITION: The judgment of the trial court is AFFIRMED.

THE HONORABLE JUDGE MARY SMITH PRESIDING
Attorney for Appellant: John Davis
Attorney for Appellee: Sarah Wilson"""
        }
        
        # 1. Test legal chunker
        print("1. Testing legal-aware chunker...")
        chunker = ContextAwareLegalChunker(chunk_size=300, overlap=50)
        chunks = await chunker.create_contextual_chunks(sample_document)
        
        print(f"✅ Created {len(chunks)} contextual chunks:")
        for i, chunk in enumerate(chunks):
            print(f"   Chunk {i+1}: {chunk.legal_section_type} ({chunk.word_count} words)")
            print(f"   Preview: {chunk.text[:100]}...")
            print(f"   Processable: {chunk.is_processable}")
            print()
        
        # 2. Test voyage-context-3 embedder
        print("2. Testing Voyage-Context-3 embedder...")
        embedder = VoyageContextualEmbedder()
        
        # Test GraphRAG interface methods
        print("   Testing GraphRAG interface methods...")
        query_embedding = embedder.embed_query("What was the court's holding?")
        print(f"   ✅ Query embedding: {len(query_embedding)}d vector")
        
        doc_embeddings = embedder.embed_documents([chunk.text for chunk in chunks[:2]])
        print(f"   ✅ Document embeddings: {len(doc_embeddings)} x {len(doc_embeddings[0])}d")
        
        # 3. Test contextual embedding with full document context
        print("\n3. Testing contextual embedding with full document context...")
        embedded_chunks = await embedder.embed_document_with_full_context(chunks)
        
        print(f"✅ Generated {len(embedded_chunks)} contextual embeddings:")
        for i, emb_chunk in enumerate(embedded_chunks):
            print(f"   Chunk {i+1}:")
            print(f"     ID: {emb_chunk.id}")
            print(f"     Embedding dim: {emb_chunk.embedding_dimension}")
            print(f"     Legal section: {emb_chunk.legal_section_type}")
            print(f"     Context: {emb_chunk.document_context['case_name']}")
            print(f"     Vector preview: [{emb_chunk.embedding[0]:.6f}, {emb_chunk.embedding[1]:.6f}, ...]")
            print()
        
        # 4. Test metadata generation
        print("4. Testing metadata generation...")
        for i, emb_chunk in enumerate(embedded_chunks):
            metadata = emb_chunk.metadata
            print(f"   Chunk {i+1} metadata:")
            for key, value in metadata.items():
                print(f"     {key}: {value}")
            print()
            break  # Just show first one
        
        # 5. Test Pinecone search if enabled
        if embedder.enable_pinecone:
            print("\\n5. Testing Pinecone semantic search...")
            search_results = await embedder.search_similar_chunks(
                query_text="What was the court's decision?",
                top_k=3
            )
            
            if search_results:
                print(f"   ✅ Found {len(search_results)} similar chunks:")
                for i, result in enumerate(search_results):
                    print(f"     Result {i+1}: {result['similarity_score']:.3f} similarity")
                    print(f"       Chunk: {result['metadata']['chunk_id']}")
                    print(f"       Section: {result['metadata']['legal_section']}")
                    print(f"       Preview: {result['metadata']['text_preview'][:100]}...")
                    print()
            else:
                print("   ⚠️  No search results (vectors may still be indexing)")
        
        print("✅ Voyage-Context-3 Contextual Embedder with Pinecone test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_voyage_contextual_embedder())