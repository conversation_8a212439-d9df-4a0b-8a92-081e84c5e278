#!/bin/bash
# Vertex AI Setup Script for Texas Legal GraphRAG Pipeline

echo "=== Setting up Vertex AI for Texas Legal Pipeline ==="

# Set project
export PROJECT_ID="newtexaslaw-*************"
gcloud config set project $PROJECT_ID

# Enable required services
echo "Enabling AI Platform services..."
gcloud services enable aiplatform.googleapis.com

# Create service account
echo "Creating ailex-gemini-sa service account..."
gcloud iam service-accounts create ailex-gemini-sa \
  --display-name="AiLex Gemini SA"

# Grant necessary permissions
echo "Granting AI Platform permissions..."
gcloud projects add-iam-policy-binding "$PROJECT_ID" \
  --member="serviceAccount:ailex-gemini-sa@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/aiplatform.user"

# Create credentials directory
echo "Setting up local credentials..."
mkdir -p "$HOME/.config/gcp"

# Generate service account key
gcloud iam service-accounts keys create "$HOME/.config/gcp/ailex-gemini-sa.json" \
  --iam-account="ailex-gemini-sa@$PROJECT_ID.iam.gserviceaccount.com"

# Add to shell configuration (zsh)
echo 'export GOOGLE_APPLICATION_CREDENTIALS="$HOME/.config/gcp/ailex-gemini-sa.json"' >> ~/.zshrc
echo 'export VERTEX_PROJECT_ID="newtexaslaw-*************"' >> ~/.zshrc
echo 'export VERTEX_LOCATION="us-central1"' >> ~/.zshrc
echo 'export VERTEX_MODEL="gemini-2.5-pro"' >> ~/.zshrc

# Source the configuration
source ~/.zshrc

echo "=== Vertex AI Setup Complete ==="
echo "Please run: source ~/.zshrc"
echo "Then test with: python verify_vertex.py"