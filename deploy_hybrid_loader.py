#!/usr/bin/env python3
"""
Hybrid Loader Deployment Script
==============================

Progressive deployment and testing of the hybrid enhanced loader:
1. Environment validation
2. Small-scale testing
3. Performance benchmarking
4. Production deployment
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from pathlib import Path
import logging

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from hybrid_config import HybridConfig, EnvironmentChecker, setup_logging
from test_hybrid_loader import HybridLoaderTestSuite

logger = logging.getLogger(__name__)

class HybridLoaderDeployment:
    """Manages deployment and testing of hybrid loader."""
    
    def __init__(self, config: HybridConfig):
        self.config = config
        self.deployment_log = []
    
    def log_step(self, step: str, status: str, details: str = ""):
        """Log deployment step."""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'step': step,
            'status': status,
            'details': details
        }
        self.deployment_log.append(entry)
        
        emoji = "✅" if status == "SUCCESS" else "❌" if status == "FAILED" else "⏳"
        logger.info(f"{emoji} {step}: {status}")
        if details:
            logger.info(f"   {details}")
    
    def validate_environment(self) -> bool:
        """Step 1: Validate environment and configuration."""
        self.log_step("Environment Validation", "RUNNING")
        
        try:
            # Check configuration
            config_errors = self.config.validate()
            if config_errors:
                error_msg = "; ".join(config_errors)
                self.log_step("Environment Validation", "FAILED", f"Config errors: {error_msg}")
                return False
            
            # Run environment checks
            if not EnvironmentChecker.run_full_check(self.config):
                self.log_step("Environment Validation", "FAILED", "Environment checks failed")
                return False
            
            self.log_step("Environment Validation", "SUCCESS")
            return True
            
        except Exception as e:
            self.log_step("Environment Validation", "FAILED", str(e))
            return False
    
    def run_unit_tests(self) -> bool:
        """Step 2: Run unit tests."""
        self.log_step("Unit Tests", "RUNNING")
        
        try:
            suite = HybridLoaderTestSuite()
            results = suite.run_all_tests()
            
            if results['tests_failed'] == 0:
                details = f"{results['tests_passed']}/{results['tests_run']} passed"
                self.log_step("Unit Tests", "SUCCESS", details)
                return True
            else:
                details = f"{results['tests_failed']}/{results['tests_run']} failed"
                self.log_step("Unit Tests", "FAILED", details)
                return False
                
        except Exception as e:
            self.log_step("Unit Tests", "FAILED", str(e))
            return False
    
    def run_small_scale_test(self, csv_file: str) -> bool:
        """Step 3: Run small-scale test with real data."""
        self.log_step("Small Scale Test", "RUNNING")
        
        if not os.path.exists(csv_file):
            self.log_step("Small Scale Test", "FAILED", f"CSV file not found: {csv_file}")
            return False
        
        try:
            from hybrid_enhanced_loader import HybridEnhancedLoader
            
            # Test with limited records
            test_loader = HybridEnhancedLoader(
                batch_size=min(100, self.config.batch_size),
                gcs_bucket=self.config.gcs_bucket,
                skip_classification=True,  # Speed up test
                csv_file=csv_file,
                enable_bulk_gcs=False,  # Disable for testing
                enable_bulk_db=False    # Disable for testing
            )
            
            # Process small sample
            start_time = time.time()
            stats = test_loader.process_csv_file_hybrid(csv_file, limit=1000)
            elapsed = time.time() - start_time
            
            if stats['total_processed'] > 0:
                rate = stats['total_processed'] / elapsed
                details = f"{stats['total_processed']} records, {rate:.0f} rec/sec"
                self.log_step("Small Scale Test", "SUCCESS", details)
                return True
            else:
                self.log_step("Small Scale Test", "FAILED", "No records processed")
                return False
                
        except Exception as e:
            self.log_step("Small Scale Test", "FAILED", str(e))
            return False
    
    def run_performance_benchmark(self, csv_file: str) -> Dict:
        """Step 4: Run performance benchmark."""
        self.log_step("Performance Benchmark", "RUNNING")
        
        try:
            from hybrid_enhanced_loader import HybridEnhancedLoader
            
            # Benchmark configuration
            benchmark_loader = HybridEnhancedLoader(
                batch_size=self.config.batch_size,
                gcs_bucket=self.config.gcs_bucket,
                skip_classification=self.config.skip_classification,
                csv_file=csv_file,
                num_workers=self.config.num_workers,
                enable_bulk_gcs=self.config.enable_bulk_gcs,
                enable_bulk_db=self.config.enable_bulk_db
            )
            
            # Run benchmark with larger sample
            logger.info("Running performance benchmark with 10K records...")
            start_time = time.time()
            stats = benchmark_loader.process_csv_file_hybrid(csv_file, limit=10000)
            elapsed = time.time() - start_time
            
            if stats['total_processed'] > 0 and elapsed > 0:
                rate = stats['total_processed'] / elapsed
                
                # Project performance
                estimated_hours = (680000 / rate) / 3600
                
                benchmark_results = {
                    'records_processed': stats['total_processed'],
                    'elapsed_seconds': elapsed,
                    'rate_per_second': rate,
                    'estimated_hours_for_680k': estimated_hours,
                    'texas_hit_rate': (stats['texas_matches'] / stats['rows_read']) * 100 if stats['rows_read'] > 0 else 0
                }
                
                details = f"{rate:.0f} rec/sec, ETA for 680K: {estimated_hours:.1f}h"
                self.log_step("Performance Benchmark", "SUCCESS", details)
                
                return benchmark_results
            else:
                self.log_step("Performance Benchmark", "FAILED", "No meaningful performance data")
                return {}
                
        except Exception as e:
            self.log_step("Performance Benchmark", "FAILED", str(e))
            return {}
    
    def deploy_production(self, csv_file: str, dry_run: bool = False) -> bool:
        """Step 5: Deploy to production."""
        step_name = "Production Deployment (DRY RUN)" if dry_run else "Production Deployment"
        self.log_step(step_name, "RUNNING")
        
        if dry_run:
            self.log_step(step_name, "SUCCESS", "Dry run completed - ready for production")
            return True
        
        try:
            from hybrid_enhanced_loader import HybridEnhancedLoader
            
            # Production loader
            prod_loader = HybridEnhancedLoader(
                batch_size=self.config.batch_size,
                gcs_bucket=self.config.gcs_bucket,
                skip_classification=self.config.skip_classification,
                csv_file=csv_file,
                num_workers=self.config.num_workers,
                enable_bulk_gcs=self.config.enable_bulk_gcs,
                enable_bulk_db=self.config.enable_bulk_db
            )
            
            logger.info("🚀 Starting PRODUCTION deployment...")
            logger.info("   This will process the FULL dataset")
            logger.info("   Press Ctrl+C within 10 seconds to cancel...")
            
            # 10 second grace period
            for i in range(10, 0, -1):
                print(f"   Starting in {i}...", end='\r')
                time.sleep(1)
            
            print("   🎯 PRODUCTION STARTED!                ")
            
            # Process full dataset
            start_time = time.time()
            stats = prod_loader.process_csv_file_hybrid(csv_file)
            elapsed = time.time() - start_time
            
            # Log final results
            rate = stats['total_processed'] / elapsed if elapsed > 0 else 0
            details = f"{stats['total_processed']} records in {elapsed/3600:.1f}h ({rate:.0f} rec/sec)"
            self.log_step("Production Deployment", "SUCCESS", details)
            
            return True
            
        except KeyboardInterrupt:
            self.log_step("Production Deployment", "CANCELLED", "User cancelled")
            return False
        except Exception as e:
            self.log_step("Production Deployment", "FAILED", str(e))
            return False
    
    def generate_report(self) -> str:
        """Generate deployment report."""
        report = [
            "=" * 60,
            "HYBRID LOADER DEPLOYMENT REPORT",
            "=" * 60,
            f"Timestamp: {datetime.now().isoformat()}",
            f"Configuration: {json.dumps(self.config.to_dict(), indent=2)}",
            "",
            "Deployment Steps:",
        ]
        
        for entry in self.deployment_log:
            status_emoji = "✅" if entry['status'] == "SUCCESS" else "❌" if entry['status'] == "FAILED" else "⏳"
            report.append(f"  {status_emoji} {entry['step']}: {entry['status']}")
            if entry['details']:
                report.append(f"     {entry['details']}")
            report.append(f"     Time: {entry['timestamp']}")
            report.append("")
        
        return "\n".join(report)
    
    def save_report(self, filename: str = None):
        """Save deployment report to file."""
        if filename is None:
            filename = f"hybrid_deployment_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(filename, 'w') as f:
            f.write(self.generate_report())
        
        logger.info(f"📋 Deployment report saved: {filename}")

def main():
    """Main deployment orchestrator."""
    parser = argparse.ArgumentParser(description="Hybrid Loader Deployment")
    parser.add_argument("--csv-file", required=True, help="Path to CSV file")
    parser.add_argument("--skip-tests", action="store_true", help="Skip unit tests")
    parser.add_argument("--skip-benchmark", action="store_true", help="Skip performance benchmark")
    parser.add_argument("--dry-run", action="store_true", help="Dry run (don't execute production)")
    parser.add_argument("--production", action="store_true", help="Run production deployment")
    parser.add_argument("--force", action="store_true", help="Skip confirmations")
    
    args = parser.parse_args()
    
    # Load environment
    from dotenv import load_dotenv
    load_dotenv()
    
    # Create configuration
    config = HybridConfig.from_env()
    setup_logging(config)
    
    # Initialize deployment
    deployment = HybridLoaderDeployment(config)
    
    logger.info("🚀 Starting Hybrid Loader Deployment")
    logger.info(f"   CSV File: {args.csv_file}")
    logger.info(f"   Batch Size: {config.batch_size}")
    logger.info(f"   Workers: {config.num_workers}")
    logger.info(f"   Bulk GCS: {config.enable_bulk_gcs}")
    logger.info(f"   Bulk DB: {config.enable_bulk_db}")
    
    success = True
    
    # Step 1: Validate Environment
    if not deployment.validate_environment():
        success = False
    
    # Step 2: Unit Tests
    if success and not args.skip_tests:
        if not deployment.run_unit_tests():
            success = False
    
    # Step 3: Small Scale Test
    if success:
        if not deployment.run_small_scale_test(args.csv_file):
            success = False
    
    # Step 4: Performance Benchmark
    benchmark_results = {}
    if success and not args.skip_benchmark:
        benchmark_results = deployment.run_performance_benchmark(args.csv_file)
        if not benchmark_results:
            success = False
    
    # Show performance projection
    if benchmark_results:
        print(f"\n📊 PERFORMANCE PROJECTION:")
        print(f"   Rate: {benchmark_results['rate_per_second']:.0f} records/second")
        print(f"   ETA for 680K records: {benchmark_results['estimated_hours_for_680k']:.1f} hours")
        print(f"   Texas hit rate: {benchmark_results['texas_hit_rate']:.1f}%")
    
    # Step 5: Production Deployment
    if success and args.production:
        if not args.force:
            response = input("\n🚨 Deploy to production? This will process the FULL dataset. [y/N]: ")
            if response.lower() != 'y':
                logger.info("🛑 Production deployment cancelled by user")
                success = False
        
        if success:
            if not deployment.deploy_production(args.csv_file, args.dry_run):
                success = False
    
    # Generate report
    deployment.save_report()
    print("\n" + deployment.generate_report())
    
    if success:
        print("\n🎉 Deployment completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Deployment failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()