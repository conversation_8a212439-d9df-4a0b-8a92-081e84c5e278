#!/usr/bin/env python3
"""
End-to-End Validation Pipeline for Legal Data Processing
Validates complete data flow: Ingestion → Chunking → Embedding → GraphRAG → Storage
"""

import os
import asyncio
import logging
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import traceback

# Import our custom modules
from setup_legal_graphrag import LegalGraphRAGPipeline
from setup_langextract_legal import LegalEntityExtractor

# Import existing infrastructure
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "courtlistener" / "processing" / "src"))

from processing.storage.supabase_connector import SupabaseConnector
from processing.storage.pinecone_connector import PineconeConnector
from api.courtlistener.workers.chunk_embed import embed_texts, det_namespace
import voyageai

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Comprehensive validation result for end-to-end pipeline"""
    success: bool
    stage: str
    document_id: str
    timestamp: datetime
    processing_time: float
    data_quality: Dict[str, float]
    error_message: Optional[str] = None
    validation_details: Optional[Dict[str, Any]] = None

@dataclass
class PipelineMetrics:
    """Pipeline performance and quality metrics"""
    total_documents: int = 0
    successful_documents: int = 0
    failed_documents: int = 0
    total_chunks: int = 0
    total_entities: int = 0
    total_relationships: int = 0
    avg_processing_time: float = 0.0
    quality_scores: Dict[str, float] = None
    
    def __post_init__(self):
        if self.quality_scores is None:
            self.quality_scores = {}

class EndToEndValidationPipeline:
    """Complete validation pipeline for legal data processing"""
    
    def __init__(self):
        """Initialize all pipeline components"""
        logger.info("Initializing End-to-End Validation Pipeline...")
        
        # Initialize storage connectors
        self.supabase = SupabaseConnector()
        self.pinecone = PineconeConnector()
        self.voyage_client = voyageai.Client(api_key=os.getenv("VOYAGE_API_KEY"))
        
        # Initialize GraphRAG pipeline
        self.graphrag_pipeline = None
        self.entity_extractor = None
        
        # Validation metrics
        self.metrics = PipelineMetrics()
        self.validation_results = []
        
        # Quality thresholds
        self.quality_thresholds = {
            "citation_accuracy": 0.95,
            "entity_extraction_confidence": 0.8,
            "cross_system_consistency": 1.0,
            "chunk_embedding_quality": 0.85,
            "relationship_accuracy": 0.75
        }
        
        logger.info("Validation pipeline initialized successfully")
    
    async def initialize_components(self):
        """Initialize async components"""
        try:
            logger.info("Initializing GraphRAG pipeline...")
            self.graphrag_pipeline = LegalGraphRAGPipeline()
            
            logger.info("Initializing entity extractor...")
            self.entity_extractor = LegalEntityExtractor("gemini")
            
            logger.info("All components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
            raise
    
    async def validate_complete_pipeline(self, test_documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Run complete end-to-end validation with test documents"""
        
        logger.info(f"Starting end-to-end validation with {len(test_documents)} test documents")
        
        await self.initialize_components()
        
        # Reset metrics
        self.metrics = PipelineMetrics()
        self.validation_results = []
        
        # Process each test document
        for doc in test_documents:
            try:
                result = await self._validate_single_document(doc)
                self.validation_results.append(result)
                
                if result.success:
                    self.metrics.successful_documents += 1
                else:
                    self.metrics.failed_documents += 1
                    
            except Exception as e:
                logger.error(f"Critical error processing document {doc.get('id')}: {e}")
                self.metrics.failed_documents += 1
        
        self.metrics.total_documents = len(test_documents)
        
        # Calculate overall metrics
        final_metrics = self._calculate_final_metrics()
        
        logger.info(f"End-to-end validation completed. Success rate: {self.metrics.successful_documents}/{self.metrics.total_documents}")
        
        return {
            "validation_summary": final_metrics,
            "individual_results": [asdict(result) for result in self.validation_results],
            "quality_assessment": self._assess_overall_quality(),
            "recommendations": self._generate_recommendations()
        }
    
    async def _validate_single_document(self, document: Dict[str, Any]) -> ValidationResult:
        """Validate complete processing pipeline for a single document"""
        
        start_time = datetime.utcnow()
        doc_id = document.get('id', 'unknown')
        
        logger.info(f"Starting validation for document {doc_id}")
        
        try:
            # Stage 1: Text Extraction & Preprocessing
            validation_data = await self._validate_text_extraction(document)
            if not validation_data['success']:
                return ValidationResult(
                    success=False,
                    stage="text_extraction",
                    document_id=doc_id,
                    timestamp=start_time,
                    processing_time=0.0,
                    data_quality={},
                    error_message=validation_data['error']
                )
            
            # Stage 2: Chunking & Embedding
            chunking_result = await self._validate_chunking_embedding(document, validation_data)
            if not chunking_result['success']:
                return ValidationResult(
                    success=False,
                    stage="chunking_embedding",
                    document_id=doc_id,
                    timestamp=start_time,
                    processing_time=(datetime.utcnow() - start_time).total_seconds(),
                    data_quality={},
                    error_message=chunking_result['error']
                )
            
            # Stage 3: Entity Extraction (LangExtract)
            entity_result = await self._validate_entity_extraction(document, validation_data)
            if not entity_result['success']:
                return ValidationResult(
                    success=False,
                    stage="entity_extraction",
                    document_id=doc_id,
                    timestamp=start_time,
                    processing_time=(datetime.utcnow() - start_time).total_seconds(),
                    data_quality={},
                    error_message=entity_result['error']
                )
            
            # Stage 4: GraphRAG Processing
            graphrag_result = await self._validate_graphrag_processing(document, validation_data)
            if not graphrag_result['success']:
                return ValidationResult(
                    success=False,
                    stage="graphrag_processing",
                    document_id=doc_id,
                    timestamp=start_time,
                    processing_time=(datetime.utcnow() - start_time).total_seconds(),
                    data_quality={},
                    error_message=graphrag_result['error']
                )
            
            # Stage 5: Storage Integration
            storage_result = await self._validate_storage_integration(document, validation_data, chunking_result, entity_result, graphrag_result)
            if not storage_result['success']:
                return ValidationResult(
                    success=False,
                    stage="storage_integration",
                    document_id=doc_id,
                    timestamp=start_time,
                    processing_time=(datetime.utcnow() - start_time).total_seconds(),
                    data_quality={},
                    error_message=storage_result['error']
                )
            
            # Stage 6: Cross-System Validation
            cross_validation = await self._validate_cross_system_consistency(document, storage_result)
            
            # Calculate final quality scores
            quality_scores = self._calculate_document_quality_scores(
                validation_data, chunking_result, entity_result, graphrag_result, storage_result, cross_validation
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Update pipeline metrics
            self.metrics.total_chunks += chunking_result.get('chunk_count', 0)
            self.metrics.total_entities += entity_result.get('entity_count', 0)
            self.metrics.total_relationships += graphrag_result.get('relationship_count', 0)
            
            logger.info(f"Document {doc_id} validation completed successfully in {processing_time:.2f}s")
            
            return ValidationResult(
                success=True,
                stage="completed",
                document_id=doc_id,
                timestamp=start_time,
                processing_time=processing_time,
                data_quality=quality_scores,
                validation_details={
                    "text_extraction": validation_data,
                    "chunking_embedding": chunking_result,
                    "entity_extraction": entity_result,
                    "graphrag_processing": graphrag_result,
                    "storage_integration": storage_result,
                    "cross_validation": cross_validation
                }
            )
            
        except Exception as e:
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            logger.error(f"Error validating document {doc_id}: {e}")
            logger.error(traceback.format_exc())
            
            return ValidationResult(
                success=False,
                stage="error",
                document_id=doc_id,
                timestamp=start_time,
                processing_time=processing_time,
                data_quality={},
                error_message=str(e)
            )
    
    async def _validate_text_extraction(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """Validate text extraction and preprocessing"""
        
        try:
            # Extract text using existing logic
            text_fields = ['plain_text', 'html_with_citations', 'html_lawbox', 'html']
            extracted_text = ""
            
            for field in text_fields:
                if field in document and document[field]:
                    extracted_text = str(document[field])
                    break
            
            if not extracted_text or len(extracted_text.strip()) < 100:
                return {"success": False, "error": "Insufficient text content"}
            
            # Basic quality checks
            text_quality = {
                "length": len(extracted_text),
                "word_count": len(extracted_text.split()),
                "contains_legal_terms": self._check_legal_terms(extracted_text),
                "has_citations": self._check_citations(extracted_text),
                "encoding_issues": self._check_encoding_issues(extracted_text)
            }
            
            return {
                "success": True,
                "extracted_text": extracted_text,
                "text_quality": text_quality,
                "source_field": next(field for field in text_fields if field in document and document[field])
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _validate_chunking_embedding(self, document: Dict[str, Any], validation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate chunking and embedding process"""
        
        try:
            from api.courtlistener.workers.chunk_embed import chunk_by_tokens
            
            text = validation_data['extracted_text']
            
            # Chunk the text
            spans = chunk_by_tokens(text, 2000, 200)
            chunks = [text[start:end] for start, end in spans]
            
            if not chunks:
                return {"success": False, "error": "No chunks generated"}
            
            # Generate embeddings for chunks
            embeddings = embed_texts(self.voyage_client, chunks, model="voyage-context-3")
            
            if len(embeddings) != len(chunks):
                return {"success": False, "error": "Embedding count mismatch"}
            
            # Validate embedding quality
            embedding_quality = self._validate_embedding_quality(embeddings, chunks)
            
            return {
                "success": True,
                "chunk_count": len(chunks),
                "chunks": chunks,
                "embeddings": embeddings,
                "embedding_quality": embedding_quality,
                "avg_chunk_length": sum(len(chunk) for chunk in chunks) / len(chunks)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _validate_entity_extraction(self, document: Dict[str, Any], validation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate LangExtract entity extraction"""
        
        try:
            text = validation_data['extracted_text']
            practice_area = document.get('practice_area', 'personal_injury')
            
            # Extract entities using LangExtract
            entity_result = await self.entity_extractor.extract_legal_entities(text, practice_area)
            
            if 'error' in entity_result:
                return {"success": False, "error": entity_result['error']}
            
            # Validate entity quality
            entity_count = entity_result['total_entities']
            quality_metrics = entity_result['quality_metrics']
            
            if quality_metrics['average_confidence'] < self.quality_thresholds['entity_extraction_confidence']:
                logger.warning(f"Entity extraction confidence below threshold: {quality_metrics['average_confidence']}")
            
            return {
                "success": True,
                "entity_count": entity_count,
                "entities": entity_result['entities'],
                "quality_metrics": quality_metrics,
                "extraction_timestamp": entity_result['extraction_timestamp']
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _validate_graphrag_processing(self, document: Dict[str, Any], validation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate Neo4j GraphRAG processing"""
        
        try:
            practice_area = document.get('practice_area', 'personal_injury')
            
            # Process through GraphRAG pipeline
            graphrag_result = await self.graphrag_pipeline.process_legal_document(document, practice_area)
            
            if 'error' in graphrag_result:
                return {"success": False, "error": graphrag_result['error']}
            
            # Validate GraphRAG output quality
            entity_count = graphrag_result.get('entities_extracted', 0)
            relationship_count = graphrag_result.get('relationships_extracted', 0)
            quality_metrics = graphrag_result.get('quality_metrics', {})
            
            return {
                "success": True,
                "entity_count": entity_count,
                "relationship_count": relationship_count,
                "entities": graphrag_result.get('entities', []),
                "relationships": graphrag_result.get('relationships', []),
                "quality_metrics": quality_metrics,
                "processing_timestamp": graphrag_result.get('processing_timestamp')
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _validate_storage_integration(self, document: Dict[str, Any], validation_data: Dict[str, Any], 
                                          chunking_result: Dict[str, Any], entity_result: Dict[str, Any], 
                                          graphrag_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate storage across all systems"""
        
        try:
            doc_id = document.get('id', 'test_doc')
            global_uid = f"validation_{doc_id}_{int(datetime.utcnow().timestamp())}"
            
            # Store in Supabase
            case_record = {
                "id": global_uid,
                "case_name": document.get('case_name', 'Test Case'),
                "jurisdiction": "TX",
                "source": "validation_test",
                "document_type": "opinion",
                "user_id": "system",
                "user_role": "system", 
                "tenant_id": "validation"
            }
            
            supabase_result = self.supabase.insert_record("cases", case_record)
            
            # Store chunks in Pinecone
            chunks = chunking_result['chunks']
            embeddings = chunking_result['embeddings']
            namespace = det_namespace("TX", document.get('practice_area'))
            
            vectors = []
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                vector_id = f"{global_uid}_chunk_{i}"
                metadata = {
                    "global_uid": global_uid,
                    "chunk_index": i,
                    "practice_area": document.get('practice_area', 'personal_injury'),
                    "jurisdiction": "TX"
                }
                vectors.append((vector_id, embedding, metadata))
            
            # Batch upsert to Pinecone
            if vectors:
                self.pinecone.index.upsert(vectors=vectors, namespace=namespace)
            
            return {
                "success": True,
                "global_uid": global_uid,
                "supabase_stored": bool(supabase_result),
                "pinecone_vectors": len(vectors),
                "namespace": namespace,
                "storage_timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _validate_cross_system_consistency(self, document: Dict[str, Any], storage_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data consistency across all storage systems"""
        
        try:
            global_uid = storage_result['global_uid']
            
            # Check Supabase record
            supabase_record = self.supabase.get_record("cases", global_uid)
            
            # Check Pinecone vectors
            namespace = storage_result['namespace']
            expected_vectors = storage_result['pinecone_vectors']
            
            # Query Pinecone to verify vectors exist
            query_vector = [0.0] * 1024  # Dummy vector for existence check
            query_result = self.pinecone.index.query(
                vector=query_vector,
                top_k=expected_vectors,
                namespace=namespace,
                filter={"global_uid": global_uid},
                include_metadata=True
            )
            
            found_vectors = len(query_result.matches)
            
            consistency_score = 1.0 if (supabase_record and found_vectors == expected_vectors) else 0.0
            
            return {
                "success": True,
                "consistency_score": consistency_score,
                "supabase_exists": bool(supabase_record),
                "pinecone_vectors_found": found_vectors,
                "pinecone_vectors_expected": expected_vectors,
                "meets_threshold": consistency_score >= self.quality_thresholds['cross_system_consistency']
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _validate_embedding_quality(self, embeddings: List[List[float]], chunks: List[str]) -> Dict[str, float]:
        """Validate embedding quality metrics"""
        
        if not embeddings:
            return {"quality_score": 0.0}
        
        # Check dimension consistency
        dimensions = [len(emb) for emb in embeddings]
        dimension_consistency = 1.0 if all(d == 1024 for d in dimensions) else 0.0
        
        # Check for zero vectors (sign of embedding failure)
        zero_vectors = sum(1 for emb in embeddings if all(val == 0.0 for val in emb))
        zero_vector_rate = zero_vectors / len(embeddings)
        
        # Calculate average magnitude (embeddings should have reasonable magnitude)
        magnitudes = [sum(val**2 for val in emb)**0.5 for emb in embeddings]
        avg_magnitude = sum(magnitudes) / len(magnitudes)
        magnitude_quality = 1.0 if 0.5 <= avg_magnitude <= 2.0 else 0.5
        
        quality_score = (dimension_consistency + (1.0 - zero_vector_rate) + magnitude_quality) / 3
        
        return {
            "quality_score": quality_score,
            "dimension_consistency": dimension_consistency,
            "zero_vector_rate": zero_vector_rate,
            "average_magnitude": avg_magnitude
        }
    
    def _check_legal_terms(self, text: str) -> bool:
        """Check if text contains legal terminology"""
        legal_terms = ["court", "judge", "plaintiff", "defendant", "damages", "verdict", "appeal", "statute"]
        text_lower = text.lower()
        return sum(1 for term in legal_terms if term in text_lower) >= 3
    
    def _check_citations(self, text: str) -> bool:
        """Check if text contains legal citations"""
        import re
        citation_patterns = [
            r"\d+\s+S\.W\.\d+d\s+\d+",
            r"\d+\s+Tex\.\s+\d+",
            r"\d+\s+F\.\d+d\s+\d+"
        ]
        return any(re.search(pattern, text) for pattern in citation_patterns)
    
    def _check_encoding_issues(self, text: str) -> bool:
        """Check for text encoding issues"""
        # Look for common encoding problem indicators
        encoding_issues = ["â€™", "â€œ", "â€", "Ã©", "Ã¡"]
        return any(issue in text for issue in encoding_issues)
    
    def _calculate_document_quality_scores(self, *stage_results) -> Dict[str, float]:
        """Calculate comprehensive quality scores for document processing"""
        
        scores = {}
        
        # Text extraction quality
        if stage_results[0]['success']:
            text_quality = stage_results[0]['text_quality']
            scores['text_extraction'] = 1.0 if text_quality['length'] > 500 and text_quality['contains_legal_terms'] else 0.7
        
        # Embedding quality
        if stage_results[1]['success']:
            scores['embedding_quality'] = stage_results[1]['embedding_quality']['quality_score']
        
        # Entity extraction quality
        if stage_results[2]['success']:
            scores['entity_extraction'] = stage_results[2]['quality_metrics']['quality_score']
        
        # GraphRAG processing quality
        if stage_results[3]['success']:
            entity_count = stage_results[3]['entity_count']
            relationship_count = stage_results[3]['relationship_count']
            scores['graphrag_quality'] = min(1.0, (entity_count + relationship_count) / 20)  # Normalize to expected counts
        
        # Storage consistency
        if len(stage_results) > 5 and stage_results[5]['success']:
            scores['storage_consistency'] = stage_results[5]['consistency_score']
        
        # Overall quality score
        scores['overall_quality'] = sum(scores.values()) / len(scores) if scores else 0.0
        
        return scores
    
    def _calculate_final_metrics(self) -> Dict[str, Any]:
        """Calculate final pipeline metrics"""
        
        total_docs = self.metrics.total_documents
        if total_docs == 0:
            return {}
        
        success_rate = self.metrics.successful_documents / total_docs
        
        # Calculate average processing time
        processing_times = [r.processing_time for r in self.validation_results if r.success]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0.0
        
        # Calculate average quality scores
        quality_scores = [r.data_quality.get('overall_quality', 0) for r in self.validation_results if r.success]
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
        
        return {
            "success_rate": success_rate,
            "total_documents": total_docs,
            "successful_documents": self.metrics.successful_documents,
            "failed_documents": self.metrics.failed_documents,
            "average_processing_time": avg_processing_time,
            "average_quality_score": avg_quality,
            "total_chunks_processed": self.metrics.total_chunks,
            "total_entities_extracted": self.metrics.total_entities,
            "total_relationships_extracted": self.metrics.total_relationships
        }
    
    def _assess_overall_quality(self) -> Dict[str, Any]:
        """Assess overall pipeline quality against thresholds"""
        
        successful_results = [r for r in self.validation_results if r.success]
        
        if not successful_results:
            return {"assessment": "FAILED", "reason": "No successful document processing"}
        
        # Check quality thresholds
        quality_checks = {}
        
        for threshold_name, threshold_value in self.quality_thresholds.items():
            if threshold_name == "cross_system_consistency":
                scores = [r.data_quality.get('storage_consistency', 0) for r in successful_results]
            elif threshold_name == "entity_extraction_confidence":
                scores = [r.data_quality.get('entity_extraction', 0) for r in successful_results]
            else:
                scores = [r.data_quality.get('overall_quality', 0) for r in successful_results]
            
            avg_score = sum(scores) / len(scores) if scores else 0.0
            quality_checks[threshold_name] = {
                "average_score": avg_score,
                "threshold": threshold_value,
                "meets_threshold": avg_score >= threshold_value
            }
        
        # Overall assessment
        all_thresholds_met = all(check["meets_threshold"] for check in quality_checks.values())
        
        if all_thresholds_met:
            assessment = "EXCELLENT"
        elif sum(check["meets_threshold"] for check in quality_checks.values()) >= len(quality_checks) * 0.8:
            assessment = "GOOD"
        elif sum(check["meets_threshold"] for check in quality_checks.values()) >= len(quality_checks) * 0.6:
            assessment = "ACCEPTABLE"
        else:
            assessment = "NEEDS_IMPROVEMENT"
        
        return {
            "assessment": assessment,
            "quality_checks": quality_checks,
            "overall_score": sum(check["average_score"] for check in quality_checks.values()) / len(quality_checks)
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results"""
        
        recommendations = []
        
        # Analyze failure patterns
        failed_results = [r for r in self.validation_results if not r.success]
        if failed_results:
            failure_stages = [r.stage for r in failed_results]
            common_failures = max(set(failure_stages), key=failure_stages.count)
            recommendations.append(f"Focus on improving {common_failures} stage - most common failure point")
        
        # Analyze quality scores
        successful_results = [r for r in self.validation_results if r.success]
        if successful_results:
            avg_scores = {}
            for key in ['text_extraction', 'embedding_quality', 'entity_extraction', 'graphrag_quality']:
                scores = [r.data_quality.get(key, 0) for r in successful_results if key in r.data_quality]
                if scores:
                    avg_scores[key] = sum(scores) / len(scores)
            
            # Find lowest scoring areas
            if avg_scores:
                lowest_score_area = min(avg_scores, key=avg_scores.get)
                if avg_scores[lowest_score_area] < 0.8:
                    recommendations.append(f"Improve {lowest_score_area} - currently lowest scoring area")
        
        # Success rate recommendations
        success_rate = self.metrics.successful_documents / max(self.metrics.total_documents, 1)
        if success_rate < 0.9:
            recommendations.append("Investigate error handling - success rate below 90%")
        
        if not recommendations:
            recommendations.append("Pipeline performing well - consider scaling up processing volume")
        
        return recommendations

# Test function with sample documents
async def run_validation_test():
    """Run validation test with sample Texas legal documents"""
    
    sample_documents = [
        {
            "id": "test_pi_case_001",
            "case_name": "Smith v. Jones Auto Repair",
            "practice_area": "personal_injury",
            "court": "Harris County District Court",
            "plain_text": """
            In the matter of Smith v. Jones Auto Repair, Case No. 2023-54321, heard in the 55th Judicial District Court
            of Harris County, Texas, the Honorable Judge Maria Rodriguez presided over this personal injury lawsuit.
            
            Plaintiff Jennifer Smith, represented by Attorney Michael Thompson of Thompson & Associates, filed suit
            against Defendant Jones Auto Repair for negligence resulting in personal injuries sustained during
            vehicle service. Defense counsel Lisa Chen of Chen Law Group represented the defendant.
            
            The evidence showed that on March 15, 2023, plaintiff's vehicle fell from a hydraulic lift due to
            improper positioning, causing severe back injuries requiring extensive medical treatment.
            The jury awarded $125,000 in actual damages and $50,000 in punitive damages.
            
            The court's decision relied on precedent established in Garcia v. Auto Services Inc., 
            567 S.W.3d 890 (Tex. 2022), and followed Texas Transportation Code Section 501.002.
            Expert witness Dr. Sarah Wilson testified regarding the extent of plaintiff's injuries
            and the necessity of ongoing physical therapy.
            """
        },
        {
            "id": "test_pi_case_002", 
            "case_name": "Brown v. Construction Co.",
            "practice_area": "personal_injury",
            "court": "Dallas County District Court",
            "plain_text": """
            This premises liability case, Brown v. XYZ Construction Company, No. 2023-67890,
            was decided by Judge Robert Davis in the 14th Judicial District Court of Dallas County, Texas.
            
            Plaintiff Robert Brown alleged that defendant's failure to properly secure a construction site
            resulted in serious injuries when he fell into an unmarked excavation. Attorney Sarah Martinez
            represented the plaintiff, while defendant was represented by John Wilson of Wilson & Partners.
            
            The jury found defendant 75% liable and awarded $200,000 in compensatory damages.
            The court applied the comparative negligence standard established in Duncan v. State, 
            234 Tex. 567 (1998), and cited federal OSHA regulations 29 CFR 1926.95.
            
            Medical expert Dr. James Anderson testified regarding plaintiff's orthopedic injuries,
            while safety expert Mark Johnson explained construction industry standards.
            """
        }
    ]
    
    try:
        pipeline = EndToEndValidationPipeline()
        results = await pipeline.validate_complete_pipeline(sample_documents)
        
        print("=== END-TO-END VALIDATION RESULTS ===")
        print(f"Success Rate: {results['validation_summary']['success_rate']:.1%}")
        print(f"Average Processing Time: {results['validation_summary']['average_processing_time']:.2f}s")
        print(f"Average Quality Score: {results['validation_summary']['average_quality_score']:.2f}")
        print(f"Overall Assessment: {results['quality_assessment']['assessment']}")
        
        print("\n=== QUALITY THRESHOLDS ===")
        for check_name, check_data in results['quality_assessment']['quality_checks'].items():
            status = "✓" if check_data['meets_threshold'] else "✗"
            print(f"{status} {check_name}: {check_data['average_score']:.3f} (threshold: {check_data['threshold']})")
        
        print("\n=== RECOMMENDATIONS ===")
        for i, recommendation in enumerate(results['recommendations'], 1):
            print(f"{i}. {recommendation}")
        
        return results
        
    except Exception as e:
        logger.error(f"Validation test failed: {e}")
        logger.error(traceback.format_exc())
        return None

if __name__ == "__main__":
    # Run the validation test
    asyncio.run(run_validation_test())