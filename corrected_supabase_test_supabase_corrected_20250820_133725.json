{"test_id": "supabase_corrected_20250820_133725", "timestamp": "2025-08-20T13:37:25.806791", "overall_success": false, "production_ready": false, "connection_and_operations": {"success": true, "connection": "working", "operations": {"basic_select": {"success": true, "response_time_ms": 1699.84, "rows_returned": 3, "sample_data": [{"id": "4570055", "case_name": "Texas Case 4570055", "jurisdiction": "TX"}, {"id": "4221982", "case_name": "Texas Case 4221982", "jurisdiction": "TX"}]}, "jurisdiction_filter": {"success": true, "response_time_ms": 943.92, "rows_returned": 5}, "source_filter": {"success": true, "response_time_ms": 1122.92, "rows_returned": 3}, "range_query": {"success": true, "response_time_ms": 482.0, "rows_returned": 5}, "complex_select": {"success": true, "response_time_ms": 485.42, "rows_returned": 2, "sample_practice_areas": [[], []]}}, "url": "https://anwefmklplkjxkmzpnva.s..."}, "write_operations": {"success": false, "error": "{'code': '23502', 'details': 'Failing row contains (null, Test Case supabase_corrected_20250820_133725, Full Test Case supabase_corrected_20250820_133725, test-court, TX, 2024-08-20, Test, null, null, null, null, test_system, test_1755689850, cluster_1755689850, null, null, null, 1, 0, 100, null, null, null, null, null, 2025-08-20 11:37:32.00449+00, 2025-08-20 11:37:32.00449+00, {test,system_validation}, null, 0, null, null, null, null, null, null, null, 150, null, test, null, 0, null, voyage-3-large, null, null, test_document, null, unknown, null, null, null, null, null, null, null, 0, null, null, null, test-court, null, null, null, f, null, null, null, null, null, null, null).', 'hint': None, 'message': 'null value in column \"id\" of relation \"cases\" violates not-null constraint'}"}, "performance": {"success": false, "error": "{'code': '57014', 'details': None, 'hint': None, 'message': 'canceling statement due to statement timeout'}"}}