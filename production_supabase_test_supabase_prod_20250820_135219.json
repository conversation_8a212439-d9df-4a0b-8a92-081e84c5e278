{"test_id": "supabase_prod_20250820_135219", "timestamp": "2025-08-20T13:52:19.706951", "overall_success": false, "production_ready": false, "connection": {"success": true, "operations": {"id_lookup": {"response_time_ms": 1373.23, "success": true, "target_ms": 1000}, "basic_select": {"response_time_ms": 1084.48, "success": true, "records_found": 5}, "jurisdiction_query": {"response_time_ms": 431.97, "success": true, "tx_cases_found": 3}, "source_query": {"response_time_ms": 513.0, "success": true, "courtlistener_cases": 2}}, "average_response_ms": 850.6700000000001, "all_under_2s": true}, "write_operations": {"success": false, "error": "{'code': '23502', 'details': 'Failing row contains (null, Production Test supabase_prod_20250820_135219, Production Test Case supabase_prod_20250820_135219, prod-test, TX, 2024-08-20, Test, null, null, null, null, production_test, prod_1755690744, cluster_1755690744, null, null, null, 1, 0, 100, null, null, null, null, null, 2025-08-20 11:52:25.645169+00, 2025-08-20 11:52:25.645169+00, {production,testing}, null, 0, null, null, null, null, null, null, null, 200, null, test, null, 0, null, voyage-3-large, null, null, test_document, null, test, null, null, null, null, null, null, null, 0, null, null, null, prod-test, null, null, null, f, null, null, null, null, null, null, null).', 'hint': None, 'message': 'null value in column \"id\" of relation \"cases\" violates not-null constraint'}"}, "performance": {"success": true, "benchmarks": {"primary_key_lookup": {"response_time_ms": 651.44, "target_ms": 500, "passed": false, "found": true}, "jurisdiction_filter": {"response_time_ms": 427.01, "target_ms": 1500, "passed": true, "tx_cases": 5}, "source_filter": {"response_time_ms": 446.96, "target_ms": 1500, "passed": true, "courtlistener_cases": 5}, "word_count_filter": {"response_time_ms": 433.39, "target_ms": 2000, "passed": true, "high_word_count_cases": 5}}, "performance_score": 75.0, "passed_benchmarks": 3, "total_benchmarks": 4, "production_ready": true}}