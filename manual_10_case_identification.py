#!/usr/bin/env python3
"""
MANUAL IDENTIFICATION OF 10 TEXAS CASES
Identify specific cases from existing GCS/Supabase data for Priority 1 implementation
"""

import os
import logging
from datetime import datetime
import json

from supabase import create_client
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()

def identify_10_target_cases():
    """Manually identify 10 Texas cases for Priority 1 implementation"""
    
    print("🔍 MANUAL 10-CASE IDENTIFICATION FOR PRIORITY 1")
    print("=" * 60)
    
    supabase = create_client(
        os.getenv("SUPABASE_URL"),
        os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    )
    
    target_cases = {
        'timestamp': datetime.utcnow().isoformat(),
        'selection_criteria': 'Cases with existing data across multiple systems',
        'api_cases': [],
        'csv_cases': [],
        'gcs_cluster_cases': [],
        'final_10_cases': [],
        'data_availability': {}
    }
    
    print("\n🎯 STEP 1: CourtListener API Cases (Rich metadata + GCS files)")
    
    # Get API cases with GCS paths
    try:
        api_response = supabase.table('cases').select(
            'id, case_name, source, jurisdiction, docket_number, gcs_path, created_at'
        ).eq('source', 'courtlistener').limit(8).execute()
        
        for case in api_response.data:
            case_info = {
                'case_id': case['id'],
                'case_name': case.get('case_name', f'Case {case["id"]}'),
                'source': case['source'],
                'jurisdiction': case.get('jurisdiction', 'Unknown'),
                'docket_number': case.get('docket_number', 'None'),
                'gcs_path': case.get('gcs_path', 'None'),
                'created_at': case.get('created_at'),
                'data_availability': {
                    'supabase': True,
                    'gcs_file': bool(case.get('gcs_path')),
                    'pinecone_vectors': 'TBD',
                    'neo4j_entities': 'TBD'
                }
            }
            target_cases['api_cases'].append(case_info)
            
            print(f"   ✅ {case['id']}: {case.get('docket_number', 'No docket')} | GCS: {bool(case.get('gcs_path'))}")
            
    except Exception as e:
        print(f"   ❌ Failed to get API cases: {e}")
    
    print(f"\n🎯 STEP 2: Bulk CSV Cases (Historical data)")
    
    # Get CSV cases 
    try:
        csv_response = supabase.table('cases').select(
            'id, case_name, source, jurisdiction, created_at'
        ).eq('source', 'courtlistener_csv').limit(3).execute()
        
        for case in csv_response.data:
            case_info = {
                'case_id': case['id'], 
                'case_name': case.get('case_name', f'Texas Case {case["id"]}'),
                'source': case['source'],
                'jurisdiction': case.get('jurisdiction', 'TX'),
                'docket_number': None,
                'gcs_path': None,
                'created_at': case.get('created_at'),
                'data_availability': {
                    'supabase': True,
                    'gcs_file': False,
                    'pinecone_vectors': 'TBD', 
                    'neo4j_entities': 'TBD'
                }
            }
            target_cases['csv_cases'].append(case_info)
            
            print(f"   ✅ {case['id']}: {case.get('case_name', 'No name')}")
            
    except Exception as e:
        print(f"   ❌ Failed to get CSV cases: {e}")
    
    print(f"\n🎯 STEP 3: GCS Cluster Cases (Known file structure)")
    
    # Known GCS cluster cases from our previous analysis
    known_clusters = [
        {'cluster_id': '10646628', 'folder': 'TX'},
        {'cluster_id': '10646630', 'folder': 'TX'}, 
        {'cluster_id': '10646631', 'folder': 'TX'},
        {'cluster_id': '10646632', 'folder': 'TX'},
        {'cluster_id': '10646617', 'folder': 'FED'}
    ]
    
    for cluster in known_clusters:
        case_info = {
            'case_id': f"cluster_{cluster['cluster_id']}",
            'case_name': f"{cluster['folder']} Cluster {cluster['cluster_id']}",
            'source': 'gcs_cluster',
            'jurisdiction': cluster['folder'],
            'docket_number': None,
            'gcs_path': f"{cluster['folder']}/clusters/{cluster['cluster_id']}.json.gz",
            'created_at': None,
            'data_availability': {
                'supabase': False,
                'gcs_file': True,
                'pinecone_vectors': 'TBD',
                'neo4j_entities': 'TBD'
            }
        }
        target_cases['gcs_cluster_cases'].append(case_info)
        
        print(f"   ✅ {cluster['cluster_id']}: {cluster['folder']} cluster | GCS: ✅")
    
    print(f"\n🎯 STEP 4: Final 10-Case Selection")
    
    # Select final 10 cases prioritizing data availability
    final_selection = []
    
    # Priority 1: API cases with GCS files (best data availability)
    api_with_gcs = [c for c in target_cases['api_cases'] if c['data_availability']['gcs_file']]
    final_selection.extend(api_with_gcs[:5])
    
    # Priority 2: CSV cases (Supabase data)
    final_selection.extend(target_cases['csv_cases'][:3])
    
    # Priority 3: GCS cluster cases (known file structure)  
    final_selection.extend(target_cases['gcs_cluster_cases'][:2])
    
    target_cases['final_10_cases'] = final_selection
    
    print(f"   📊 Final Selection Summary:")
    print(f"      API cases with GCS: {len([c for c in final_selection if c['source'] == 'courtlistener'])}")
    print(f"      CSV cases: {len([c for c in final_selection if c['source'] == 'courtlistener_csv'])}")
    print(f"      GCS cluster cases: {len([c for c in final_selection if c['source'] == 'gcs_cluster'])}")
    print(f"      Total: {len(final_selection)}")
    
    print(f"\n📋 FINAL 10 CASES FOR PRIORITY 1:")
    for i, case in enumerate(final_selection, 1):
        supabase_status = "✅" if case['data_availability']['supabase'] else "❌"
        gcs_status = "✅" if case['data_availability']['gcs_file'] else "❌"
        
        print(f"   {i:2d}. {case['case_id']:<15} | {case['source']:<20} | Supabase: {supabase_status} | GCS: {gcs_status}")
        if case.get('docket_number'):
            print(f"       Docket: {case['docket_number']}")
        if case.get('gcs_path'):
            print(f"       GCS: {case['gcs_path']}")
    
    # Save results
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    filename = f"manual_10_case_identification_{timestamp}.json"
    
    with open(filename, 'w') as f:
        json.dump(target_cases, f, indent=2, default=str)
    
    print(f"\n📄 Detailed analysis saved: {filename}")
    
    return target_cases

if __name__ == "__main__":
    cases = identify_10_target_cases()
    
    total_cases = len(cases['final_10_cases'])
    cases_with_supabase = len([c for c in cases['final_10_cases'] if c['data_availability']['supabase']])
    cases_with_gcs = len([c for c in cases['final_10_cases'] if c['data_availability']['gcs_file']])
    
    print(f"\n🎯 IDENTIFICATION COMPLETE:")
    print(f"   Total Cases: {total_cases}")
    print(f"   With Supabase: {cases_with_supabase}")  
    print(f"   With GCS: {cases_with_gcs}")
    print(f"   Ready for Priority 1 implementation")