#!/usr/bin/env python3
"""
End-to-End Pipeline Test
Test GraphRAG pipeline with synthetic legal documents
"""

import asyncio
import time
from datetime import datetime

async def test_graphrag_pipeline():
    """Test GraphRAG pipeline with synthetic documents"""
    print("=== End-to-End GraphRAG Pipeline Test ===\n")
    
    try:
        # Import GraphRAG pipeline
        from setup_legal_graphrag import LegalGraphRAGPipeline
        pipeline = LegalGraphRAGPipeline()
        
        # Create test documents
        test_documents = [
            {
                "id": "test_simple_case",
                "case_name": "<PERSON> v. Jones Auto Repair",
                "practice_area": "personal_injury", 
                "court_name": "Travis County District Court",
                "plain_text": """
                <PERSON> v. Jones Auto Repair, plaintiff <PERSON> filed suit against Jones Auto Repair 
                for negligent vehicle maintenance. Judge <PERSON> presided over the case. 
                Attorney <PERSON> represented the plaintiff. The jury awarded $35,000 in damages.
                """
            },
            {
                "id": "test_complex_case",
                "case_name": "<PERSON> v. Metro Hospital System",
                "practice_area": "medical_malpractice",
                "court_name": "Harris County District Court", 
                "plain_text": """
                This medical malpractice case involves plaintiff <PERSON> against Metro Hospital System.
                Dr. <PERSON>, the attending physician, allegedly failed to diagnose plaintiff's condition.
                Judge <PERSON> presided over the proceedings. Attorney <PERSON> represented the plaintiff,
                while defense attorney <PERSON> Park represented the hospital. Expert witness Dr. Robert Kim testified
                regarding standard of care. The court awarded $125,000 in compensatory damages and $25,000 in 
                punitive damages. The case cited precedent from <PERSON> v. Central Medical, 456 S.W.3d 789 (Tex. 2021).
                """
            }
        ]
        
        results = []
        
        for i, doc in enumerate(test_documents, 1):
            print(f"{i}. Processing: {doc['case_name']}")
            start_time = time.time()
            
            try:
                # Process with GraphRAG
                result = await pipeline.process_legal_document(doc)
                processing_time = time.time() - start_time
                
                # Extract results
                entities = result.get('entities', [])
                relationships = result.get('relationships', [])
                
                print(f"   ✅ Completed in {processing_time:.2f}s")
                print(f"   📊 Entities extracted: {len(entities)}")
                print(f"   🔗 Relationships extracted: {len(relationships)}")
                
                # Show entities by type
                entity_types = {}
                for entity in entities:
                    entity_type = entity.get('type', 'Unknown')
                    entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
                
                if entity_types:
                    print(f"   🏷️  Entity types: {entity_types}")
                
                # Show sample entities
                if entities:
                    print(f"   📝 Sample entities:")
                    for entity in entities[:5]:
                        print(f"      - {entity.get('type', 'Unknown')}: {entity.get('text', 'Unknown')}")
                
                # Show sample relationships
                if relationships:
                    print(f"   🔗 Sample relationships:")
                    for rel in relationships[:3]:
                        print(f"      - {rel.get('type', 'Unknown')}: {rel.get('start_node_id', '?')} -> {rel.get('end_node_id', '?')}")
                
                results.append({
                    "document_id": doc["id"],
                    "case_name": doc["case_name"],
                    "processing_time": processing_time,
                    "entities_count": len(entities),
                    "relationships_count": len(relationships),
                    "entity_types": entity_types,
                    "success": True
                })
                
            except Exception as e:
                processing_time = time.time() - start_time
                print(f"   ❌ Failed in {processing_time:.2f}s: {e}")
                
                results.append({
                    "document_id": doc["id"],
                    "case_name": doc["case_name"],
                    "processing_time": processing_time,
                    "entities_count": 0,
                    "relationships_count": 0,
                    "entity_types": {},
                    "success": False,
                    "error": str(e)
                })
            
            print()
        
        # Summary
        print("=== Pipeline Test Summary ===")
        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]
        
        print(f"📊 Documents processed: {len(results)}")
        print(f"✅ Successful: {len(successful)}")
        print(f"❌ Failed: {len(failed)}")
        
        if successful:
            avg_time = sum(r["processing_time"] for r in successful) / len(successful)
            total_entities = sum(r["entities_count"] for r in successful)
            total_relationships = sum(r["relationships_count"] for r in successful)
            
            print(f"⏱️  Average processing time: {avg_time:.2f}s")
            print(f"🏷️  Total entities extracted: {total_entities}")
            print(f"🔗 Total relationships extracted: {total_relationships}")
        
        if failed:
            print(f"\n❌ Failed cases:")
            for result in failed:
                print(f"   - {result['case_name']}: {result.get('error', 'Unknown error')}")
        
        return results
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_enhanced_pipeline():
    """Test the enhanced pipeline (GraphRAG + complexity analysis)"""
    print("\n=== Enhanced Pipeline Test ===\n")
    
    try:
        # Import enhanced pipeline components
        from enhanced_pipeline import EnhancedLegalPipeline, DocumentComplexityClassifier
        from supabase_data_reader import LegalCase
        
        # Create test cases as LegalCase objects
        test_cases = [
            LegalCase(
                id="enhanced_test_1",
                case_name="Complex Multi-Party Litigation",
                case_name_full="Tech Corp v. Multiple Defendants",
                practice_area="commercial",
                court_id="dallas-county",
                jurisdiction="TX",
                date_filed="2023-01-15",
                gcs_path="test/path",
                word_count=2500,
                case_type="civil",
                judge_name="Judge Sarah Martinez",
                content="""
                This is a complex commercial litigation case involving Technology Corporation as plaintiff
                against five defendant entities. The case involves breach of contract, trade secret 
                misappropriation, and unfair competition claims. Judge Sarah Martinez presides over
                the proceedings in Dallas County District Court. Attorney Michael Johnson leads the
                plaintiff's legal team, while defendants are represented by Jones & Associates.
                
                The dispute centers on a $5 million software licensing agreement executed in 2022.
                Expert witnesses include Dr. Patricia Kim (technology), Dr. Robert Chen (damages),
                and Dr. Lisa Williams (industry standards). The case has generated significant
                discovery disputes and multiple motions for summary judgment.
                
                Previous rulings cite precedent from Apple v. Samsung, 567 F.3d 234 (Fed. Cir. 2020),
                and Texas Instruments v. Competitors, 345 S.W.3d 567 (Tex. 2019). The court has
                scheduled a three-week jury trial beginning March 2024.
                """,
                metadata={"source": "test", "complex": True}
            ),
            LegalCase(
                id="enhanced_test_2", 
                case_name="Simple Contract Dispute",
                case_name_full="Brown v. Local Services LLC",
                practice_area="commercial",
                court_id="austin-county",
                jurisdiction="TX",
                date_filed="2023-06-01",
                gcs_path="test/path2",
                word_count=800,
                case_type="civil",
                judge_name="Judge David Wilson",
                content="""
                Brown filed a simple breach of contract claim against Local Services LLC
                for failure to complete landscaping work. Judge David Wilson heard the case
                in Austin County. Attorney Jennifer Davis represented the plaintiff.
                The court awarded $15,000 in damages for incomplete work.
                """,
                metadata={"source": "test", "complex": False}
            )
        ]
        
        # Test complexity classifier first
        classifier = DocumentComplexityClassifier()
        
        for case in test_cases:
            print(f"Analyzing complexity: {case.case_name}")
            
            # Mock GraphRAG results for complexity analysis
            mock_graphrag = {
                "entities": [
                    {"type": "Judge", "text": case.judge_name, "confidence": 0.9},
                    {"type": "Case", "text": case.case_name, "confidence": 0.8}
                ],
                "relationships": []
            }
            
            complexity = classifier.analyze_complexity(case, mock_graphrag)
            
            print(f"   🧮 Complexity score: {complexity['complexity_score']:.2f}")
            print(f"   🎯 Needs enhancement: {complexity['needs_enhancement']}")
            print(f"   📝 Reason: {complexity['enhancement_reason']}")
            print()
        
        # Test with enhanced pipeline (but skip actual LangExtract for speed)
        print("Testing enhanced pipeline...")
        pipeline = EnhancedLegalPipeline()
        
        # Process one case
        test_case = test_cases[0]
        print(f"Processing with enhanced pipeline: {test_case.case_name}")
        
        try:
            result = await pipeline.process_document(test_case)
            
            print(f"   ✅ Processing type: {result.processing_type}")
            print(f"   ⏱️  Processing time: {result.processing_time:.2f}s")
            print(f"   📊 Final entities: {len(result.final_results.get('entities', []))}")
            print(f"   🔗 Final relationships: {len(result.final_results.get('relationships', []))}")
            
            if result.complexity_analysis:
                print(f"   🧮 Complexity: {result.complexity_analysis['complexity_score']:.2f}")
            
        except Exception as e:
            print(f"   ❌ Enhanced pipeline failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Test basic GraphRAG pipeline
    results = asyncio.run(test_graphrag_pipeline())
    
    # Test enhanced pipeline
    asyncio.run(test_enhanced_pipeline())