#!/usr/bin/env python3
"""
CourtListener API Case Extractor
Demonstrates how to access full text from API-fetched cases stored in GCS
"""

import os
import json
import gzip
from typing import Dict, List, Optional
from google.cloud import storage
from supabase import create_client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class CourtListenerAPIExtractor:
    """Extract and analyze CourtListener API-fetched cases."""
    
    def __init__(self):
        """Initialize GCS and Supabase clients."""
        self.gcs_client = storage.Client()
        self.bucket = self.gcs_client.bucket(os.getenv('GCS_BUCKET_NAME', 'texas-laws-personalinjury'))
        
        # Initialize Supabase
        self.supabase = create_client(
            os.getenv('SUPABASE_URL'),
            os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        )
    
    def get_api_case_opinion(self, case_id: str) -> Optional[Dict]:
        """Get full opinion data from API-fetched case."""
        gcs_path = f"FED/opinions/{case_id}.json.gz"
        
        try:
            blob = self.bucket.blob(gcs_path)
            if not blob.exists():
                print(f"Opinion file not found: {gcs_path}")
                return None
                
            compressed_data = blob.download_as_bytes()
            content = gzip.decompress(compressed_data).decode('utf-8')
            return json.loads(content)
            
        except Exception as e:
            print(f"Error retrieving API case {case_id}: {e}")
            return None
    
    def get_case_cluster(self, cluster_id: str) -> Optional[Dict]:
        """Get case cluster metadata."""
        gcs_path = f"FED/clusters/{cluster_id}.json.gz"
        
        try:
            blob = self.bucket.blob(gcs_path)
            if not blob.exists():
                print(f"Cluster file not found: {gcs_path}")
                return None
                
            compressed_data = blob.download_as_bytes()
            content = gzip.decompress(compressed_data).decode('utf-8')
            return json.loads(content)
            
        except Exception as e:
            print(f"Error retrieving cluster {cluster_id}: {e}")
            return None
    
    def get_full_text(self, case_id: str) -> str:
        """Extract full text from API case with field priority."""
        opinion_data = self.get_api_case_opinion(case_id)
        if not opinion_data:
            return ""
        
        # Priority order for text extraction
        text_fields = [
            'plain_text',           # Best for analysis
            'html_with_citations',  # Best for research  
            'html',                 # Basic HTML
            'html_lawbox',          # Alternative source
            'xml_harvard'           # XML format
        ]
        
        for field in text_fields:
            text = opinion_data.get(field, '')
            if text and len(text.strip()) > 100:
                print(f"✅ Text found in '{field}' field: {len(text):,} characters")
                return text.strip()
        
        print("⚠️ No substantial text found in any field")
        return ""
    
    def get_complete_case(self, case_id: str) -> Dict:
        """Get complete case information including opinion and cluster data."""
        # Get opinion data
        opinion_data = self.get_api_case_opinion(case_id)
        if not opinion_data:
            return {}
        
        # Get cluster data
        cluster_id = opinion_data.get('cluster_id')
        cluster_data = self.get_case_cluster(str(cluster_id)) if cluster_id else {}
        
        # Extract full text
        full_text = self.get_full_text(case_id)
        
        return {
            'case_id': case_id,
            'opinion_data': opinion_data,
            'cluster_data': cluster_data,
            'full_text': full_text,
            'text_length': len(full_text),
            'case_name': cluster_data.get('case_name', 'Unknown'),
            'court': cluster_data.get('court', 'Unknown'),
            'date_filed': cluster_data.get('date_filed', 'Unknown'),
            'author': opinion_data.get('author_str', 'Unknown'),
            'opinion_type': opinion_data.get('type', 'Unknown')
        }
    
    def list_api_cases(self, limit: int = 10) -> List[Dict]:
        """List API-fetched cases from database."""
        try:
            response = self.supabase.table('cases').select(
                'id, case_name, gcs_path, gcs_cluster_path, created_at, jurisdiction'
            ).eq('source', 'courtlistener').limit(limit).order('created_at', desc=True).execute()
            
            return response.data
            
        except Exception as e:
            print(f"Error querying database: {e}")
            return []
    
    def analyze_api_case_quality(self, case_id: str) -> Dict:
        """Analyze the quality of an API-fetched case."""
        complete_case = self.get_complete_case(case_id)
        if not complete_case:
            return {'valid': False, 'reason': 'Case not found'}
        
        opinion_data = complete_case['opinion_data']
        full_text = complete_case['full_text']
        
        # Analyze text fields availability
        text_fields_analysis = {}
        text_fields = ['plain_text', 'html_with_citations', 'html', 'html_lawbox']
        
        for field in text_fields:
            content = opinion_data.get(field, '')
            text_fields_analysis[field] = {
                'available': bool(content),
                'length': len(content) if content else 0
            }
        
        # Quality metrics
        word_count = len(full_text.split()) if full_text else 0
        has_metadata = bool(complete_case['cluster_data'])
        
        return {
            'valid': len(full_text) > 100,
            'case_id': case_id,
            'case_name': complete_case['case_name'],
            'text_length': len(full_text),
            'word_count': word_count,
            'has_metadata': has_metadata,
            'text_fields': text_fields_analysis,
            'court': complete_case['court'],
            'date_filed': complete_case['date_filed'],
            'author': complete_case['author']
        }
    
    def batch_extract_api_cases(self, case_ids: List[str]) -> Dict[str, Dict]:
        """Extract multiple API cases efficiently."""
        results = {}
        
        print(f"🔍 Extracting {len(case_ids)} API cases...")
        
        for i, case_id in enumerate(case_ids, 1):
            print(f"Processing case {i}/{len(case_ids)}: {case_id}")
            
            try:
                complete_case = self.get_complete_case(case_id)
                if complete_case:
                    results[case_id] = complete_case
                    print(f"  ✅ Success: {complete_case['text_length']:,} chars")
                else:
                    print(f"  ❌ Failed to extract case {case_id}")
                    
            except Exception as e:
                print(f"  ❌ Error processing {case_id}: {e}")
        
        return results
    
    def search_api_cases_by_text(self, search_term: str, limit: int = 5) -> List[Dict]:
        """Search API cases by text content."""
        # Get list of API cases
        api_cases = self.list_api_cases(limit=50)  # Get more to search through
        
        matching_cases = []
        search_term_lower = search_term.lower()
        
        print(f"🔍 Searching {len(api_cases)} API cases for '{search_term}'...")
        
        for case in api_cases:
            case_id = case['id']
            full_text = self.get_full_text(case_id)
            
            if search_term_lower in full_text.lower():
                complete_case = self.get_complete_case(case_id)
                matching_cases.append(complete_case)
                
                if len(matching_cases) >= limit:
                    break
        
        return matching_cases

# Usage examples and demonstrations
if __name__ == "__main__":
    extractor = CourtListenerAPIExtractor()
    
    print("🌐 COURTLISTENER API CASE EXTRACTOR DEMO")
    print("=" * 60)
    
    # Example 1: List available API cases
    print("\n📋 Example 1: List API-Fetched Cases")
    api_cases = extractor.list_api_cases(5)
    print(f"Found {len(api_cases)} API cases:")
    for case in api_cases:
        print(f"  {case['id']}: {case.get('case_name', 'Unknown')} ({case['created_at'][:10]})")
    
    if api_cases:
        # Example 2: Extract full case details
        sample_case_id = api_cases[0]['id']
        print(f"\n📄 Example 2: Complete Case Analysis - {sample_case_id}")
        
        complete_case = extractor.get_complete_case(sample_case_id)
        if complete_case:
            print(f"Case Name: {complete_case['case_name']}")
            print(f"Court: {complete_case['court']}")
            print(f"Date Filed: {complete_case['date_filed']}")
            print(f"Author: {complete_case['author']}")
            print(f"Full Text: {complete_case['text_length']:,} characters")
            
            # Show text preview
            if complete_case['full_text']:
                preview = complete_case['full_text'][:300].replace('\n', ' ')
                print(f"Preview: {preview}...")
        
        # Example 3: Quality analysis
        print(f"\n🔍 Example 3: Quality Analysis - {sample_case_id}")
        quality = extractor.analyze_api_case_quality(sample_case_id)
        
        print(f"Valid: {'✅' if quality['valid'] else '❌'}")
        print(f"Text Length: {quality['text_length']:,} characters")
        print(f"Word Count: {quality['word_count']:,} words")
        print(f"Has Metadata: {'✅' if quality['has_metadata'] else '❌'}")
        
        print("Text Fields Available:")
        for field, data in quality['text_fields'].items():
            status = '✅' if data['available'] else '❌'
            length = f"({data['length']:,} chars)" if data['available'] else ""
            print(f"  {field}: {status} {length}")
        
        # Example 4: Batch processing
        print(f"\n📦 Example 4: Batch Processing")
        case_ids = [case['id'] for case in api_cases[:3]]
        batch_results = extractor.batch_extract_api_cases(case_ids)
        
        print(f"Successfully processed {len(batch_results)} cases:")
        for case_id, data in batch_results.items():
            print(f"  {case_id}: {data['case_name']} ({data['text_length']:,} chars)")
    
    print("\n✅ API Case Extractor Demo Complete!")
    print("\nThe system can access full text from all 500 API-fetched cases!")
    print("Use the provided methods to integrate with your analysis pipeline.")
