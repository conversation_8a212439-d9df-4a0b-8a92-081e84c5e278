{"test_id": "simple_proof_20250820_125205", "timestamp": "2025-08-20T12:52:05.293431", "systems_tested": {"supabase": {"success": false, "error": "{'code': '57014', 'details': None, 'hint': None, 'message': 'canceling statement due to statement timeout'}"}, "neo4j": {"success": true, "node_count": 54, "connection": "working", "uri": "neo4j+s://a332fed8.databases.n..."}, "pinecone": {"success": true, "total_vectors": 408, "dimension": 1024, "connection": "working", "index_name": "texas-laws-voyage3large"}, "vertex ai": {"success": true, "model_available": true, "response_received": true, "connection": "working"}, "voyage ai": {"success": true, "embedding_dimension": 1024, "connection": "working", "model": "voyage-3"}}, "overall_success": true, "working_systems": 4, "total_systems": 5, "success_rate": 80.0}