#!/usr/bin/env python3
"""
Debug GraphRAG Extraction Issues
Identify why entities are not being extracted
"""

import asyncio
import json
import vertexai
from vertexai.generative_models import GenerativeModel, GenerationConfig
from dotenv import load_dotenv
import os

load_dotenv()

async def test_direct_vertex_extraction():
    """Test direct Vertex AI extraction similar to working test"""
    print("=== Testing Direct Vertex AI Extraction ===\n")
    
    try:
        # Initialize Vertex AI
        project_id = os.getenv("VERTEX_PROJECT_ID")
        vertexai.init(project=project_id, location="us-central1")
        
        # Test text
        test_text = """
        In <PERSON> v. Jones Auto Repair, plaintiff <PERSON> filed suit against Jones Auto Repair 
        for negligent vehicle maintenance. Judge <PERSON> presided over the case. 
        Attorney <PERSON> represented the plaintiff. The jury awarded $35,000 in damages.
        """
        
        # Schema for structured output
        response_schema = {
            "type": "object",
            "properties": {
                "nodes": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "string"},
                            "label": {"type": "string"},
                            "properties": {"type": "object"}
                        },
                        "required": ["id", "label", "properties"]
                    }
                },
                "relationships": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "start_node_id": {"type": "string"},
                            "end_node_id": {"type": "string"},
                            "properties": {"type": "object"}
                        },
                        "required": ["type", "start_node_id", "end_node_id"]
                    }
                }
            },
            "required": ["nodes", "relationships"]
        }
        
        # Create prompt
        prompt = f"""You extract entities and relationships for a knowledge graph.
Emit ONE JSON object with keys "nodes" and "relationships" and nothing else.

Graph schema:
Allowed labels: Case, Judge, Court, Attorney, Plaintiff, Defendant, Damages
Allowed relationships: PRESIDED_OVER, REPRESENTED, FILED_IN, AWARDED, OPPOSED

Input text:
{test_text}"""
        
        # Configure with structured output
        cfg = GenerationConfig(
            temperature=0.0,
            response_mime_type="application/json",
            response_schema=response_schema
        )
        
        # Generate content
        model = GenerativeModel("gemini-2.5-pro")
        resp = model.generate_content(prompt, generation_config=cfg)
        
        print(f"✅ Direct Vertex AI Response:")
        print(f"Raw response: {resp.text}")
        
        # Parse result
        data = json.loads(resp.text)
        
        print(f"\n📊 Direct Extraction Results:")
        print(f"   Nodes: {len(data['nodes'])}")
        print(f"   Relationships: {len(data['relationships'])}")
        
        # Show entities
        for node in data['nodes']:
            print(f"   🏷️  {node['label']}: {node['id']} - {node.get('properties', {})}")
        
        # Show relationships
        for rel in data['relationships']:
            print(f"   🔗 {rel['type']}: {rel['start_node_id']} -> {rel['end_node_id']}")
            
        return data
        
    except Exception as e:
        print(f"❌ Direct Vertex AI test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_graphrag_pipeline_debug():
    """Test GraphRAG pipeline with debugging"""
    print("\n=== Testing GraphRAG Pipeline with Debugging ===\n")
    
    try:
        from setup_legal_graphrag import LegalGraphRAGPipeline
        
        # Enable more detailed logging
        import logging
        logging.basicConfig(level=logging.DEBUG)
        
        pipeline = LegalGraphRAGPipeline()
        
        # Simple test document
        doc = {
            "id": "debug_test",
            "plain_text": """
            In Smith v. Jones Auto Repair, plaintiff Mary Smith filed suit against Jones Auto Repair 
            for negligent vehicle maintenance. Judge Robert Wilson presided over the case. 
            Attorney Sarah Davis represented the plaintiff. The jury awarded $35,000 in damages.
            """,
            "case_name": "Smith v. Jones Auto Repair",
            "practice_area": "personal_injury",
            "court_name": "Test County Court"
        }
        
        print(f"Processing document: {doc['case_name']}")
        print(f"Text length: {len(doc['plain_text'])}")
        
        # Process with GraphRAG
        result = await pipeline.process_legal_document(doc)
        
        print(f"\n📊 GraphRAG Results:")
        print(f"   Result keys: {list(result.keys())}")
        print(f"   Entities: {len(result.get('entities', []))}")
        print(f"   Relationships: {len(result.get('relationships', []))}")
        print(f"   Processing time: {result.get('processing_time', 'Unknown')}")
        
        # Print full result for debugging
        print(f"\n🔍 Full GraphRAG Result:")
        print(json.dumps(result, indent=2, default=str))
        
        return result
        
    except Exception as e:
        print(f"❌ GraphRAG pipeline debug failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def compare_extraction_methods():
    """Compare direct Vertex AI vs GraphRAG pipeline"""
    print("\n=== Comparing Extraction Methods ===\n")
    
    # Test both methods
    direct_result = await test_direct_vertex_extraction()
    graphrag_result = await test_graphrag_pipeline_debug()
    
    # Compare results
    print(f"\n📊 Comparison Summary:")
    
    if direct_result:
        print(f"Direct Vertex AI:")
        print(f"   ✅ Nodes: {len(direct_result['nodes'])}")
        print(f"   ✅ Relationships: {len(direct_result['relationships'])}")
    else:
        print(f"Direct Vertex AI: ❌ Failed")
    
    if graphrag_result:
        print(f"GraphRAG Pipeline:")
        print(f"   📊 Entities: {len(graphrag_result.get('entities', []))}")
        print(f"   📊 Relationships: {len(graphrag_result.get('relationships', []))}")
    else:
        print(f"GraphRAG Pipeline: ❌ Failed")
    
    # Analysis
    print(f"\n🔍 Analysis:")
    if direct_result and len(direct_result['nodes']) > 0:
        print("   ✅ Direct Vertex AI extraction is working")
    else:
        print("   ❌ Direct Vertex AI extraction has issues")
    
    if graphrag_result and len(graphrag_result.get('entities', [])) > 0:
        print("   ✅ GraphRAG pipeline extraction is working")
    else:
        print("   ❌ GraphRAG pipeline is not extracting entities")
        print("   💡 Possible issues:")
        print("      - Entity extraction component not working")
        print("      - Entity pruning removing all entities")
        print("      - Schema mismatch between extraction and storage")
        print("      - Neo4j connection or storage issues")

async def test_graphrag_components():
    """Test individual GraphRAG components"""
    print("\n=== Testing GraphRAG Components ===\n")
    
    try:
        from neo4j_graphrag.experimental.pipeline import Pipeline
        from neo4j_graphrag.llm import VertexAILLM
        from neo4j_graphrag.experimental.components.text_splitters import FixedSizeSplitter
        from neo4j_graphrag.experimental.components.kg_builder import SimpleKGBuilder
        from neo4j_graphrag.experimental.components import GraphBuilder
        
        # Test LLM component
        print("1. Testing Vertex AI LLM component...")
        try:
            llm = VertexAILLM(
                model_name="gemini-2.5-pro",
                project_id=os.getenv("VERTEX_PROJECT_ID"),
                location="us-central1"
            )
            
            test_prompt = "Extract entities from: Judge Smith presided over case."
            response = await llm.ainvoke(test_prompt)
            print(f"   ✅ LLM response: {response.content[:100]}...")
            
        except Exception as e:
            print(f"   ❌ LLM test failed: {e}")
        
        # Test text splitter
        print("\n2. Testing text splitter...")
        try:
            splitter = FixedSizeSplitter(chunk_size=200, chunk_overlap=50)
            test_text = "This is a test document. " * 20
            chunks = splitter.run(text=test_text)
            print(f"   ✅ Created {len(chunks.get('chunks', []))} chunks")
            
        except Exception as e:
            print(f"   ❌ Text splitter test failed: {e}")
            
        return True
        
    except Exception as e:
        print(f"❌ Component testing failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(compare_extraction_methods())
    asyncio.run(test_graphrag_components())