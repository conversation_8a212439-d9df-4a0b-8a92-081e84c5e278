#!/usr/bin/env python3
"""
Debug Expert Classification
Examine why <PERSON> and <PERSON> aren't being classified as experts
"""

import os
import logging
from dotenv import load_dotenv
from neo4j import GraphDatabase

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_expert_classification():
    """Debug why experts aren't being detected"""
    logger.info("🔍 DEBUGGING EXPERT CLASSIFICATION")
    logger.info("=" * 70)
    
    # Initialize Neo4j connection
    driver = GraphDatabase.driver(
        os.getenv("NEO4J_URI"),
        auth=(os.getenv("NEO4J_USERNAME", "neo4j"), os.getenv("NEO4J_PASSWORD"))
    )
    
    try:
        with driver.session() as session:
            # Get detailed information about <PERSON> and <PERSON>
            experts_query = """
            MATCH (person:Person)
            WHERE person.name IN ['<PERSON>', '<PERSON>']
            
            // Get their chunks and context
            OPTIONAL MATCH (person)<-[:FROM_CHUNK]-(chunk:Chunk)
            
            // Get their relationships
            OPTIONAL MATCH (person)-[r]-(related:__KGBuilder__)
            WHERE NOT related:Chunk AND related.name IS NOT NULL
            
            RETURN person.name as name,
                   person.legal_role as current_role,
                   person.role_confidence as confidence,
                   person.role_evidence as evidence,
                   collect(DISTINCT chunk.text)[0..2] as context_chunks,
                   collect(DISTINCT {
                       type: type(r),
                       related_name: related.name,
                       related_type: [label IN labels(related) WHERE label <> '__KGBuilder__'][0]
                   }) as relationships
            ORDER BY person.name
            """
            
            result = session.run(experts_query)
            
            for record in result:
                name = record["name"]
                current_role = record["current_role"]
                confidence = record["confidence"]
                evidence = record["evidence"]
                context_chunks = record["context_chunks"]
                relationships = record["relationships"]
                
                logger.info(f"\n👤 {name}:")
                logger.info(f"   Current role: {current_role}")
                logger.info(f"   Confidence: {confidence}")
                logger.info(f"   Evidence: {evidence}")
                logger.info(f"   Relationships: {len(relationships)}")
                
                for rel in relationships:
                    if rel["type"]:  # Skip empty relationships
                        logger.info(f"      {rel['type']}: {rel['related_name']} ({rel['related_type']})")
                
                logger.info(f"   Context chunks: {len(context_chunks)}")
                for i, chunk in enumerate(context_chunks):
                    if chunk:
                        logger.info(f"      Chunk {i+1}: {chunk[:200]}...")
                
                # Test classification patterns manually
                logger.info(f"\n🧪 Manual pattern testing for {name}:")
                
                # Combine all context
                full_context = " ".join([chunk for chunk in context_chunks if chunk])
                name_and_context = f"{name.lower()} {full_context.lower()}"
                
                logger.info(f"   Full context length: {len(full_context)} characters")
                logger.info(f"   Context preview: {full_context[:300]}...")
                
                # Test expert patterns
                expert_patterns = [
                    "dr.", "doctor", "testified", "expert witness", "specialist", 
                    "gastroenterology", "medical", "physician"
                ]
                
                found_patterns = []
                for pattern in expert_patterns:
                    if pattern in name_and_context:
                        found_patterns.append(pattern)
                
                logger.info(f"   Found expert patterns: {found_patterns}")
                
                # Test context phrases
                context_phrases = [
                    "testified that", "expert witness", "specialist from", 
                    "performed by", "standard of care"
                ]
                
                found_phrases = []
                for phrase in context_phrases:
                    if phrase in full_context.lower():
                        found_phrases.append(phrase)
                
                logger.info(f"   Found context phrases: {found_phrases}")
                
                if found_patterns or found_phrases:
                    logger.info(f"   ⚠️  Should be classified as Expert but wasn't!")
                    logger.info(f"   Patterns: {found_patterns}")
                    logger.info(f"   Phrases: {found_phrases}")
        
        # Also check what the original document text contained
        logger.info(f"\n📄 ORIGINAL DOCUMENT ANALYSIS:")
        
        # Sample text from our test document
        original_text = """
        In Anderson v. Memorial Hermann Hospital, plaintiff Sarah Anderson sued Memorial Hermann 
        Hospital for medical malpractice. The Honorable Judge Robert Wilson presided over the case 
        in the 157th Judicial District Court of Harris County, Texas.
        
        Attorney Jennifer Martinez of Martinez Law Firm represented plaintiff Anderson, while 
        Attorney Michael Davis of Healthcare Defense Group represented defendant Memorial Hermann Hospital.
        
        The case involved surgical complications during Anderson's gallbladder removal performed 
        by Dr. Lisa Chen, a specialist at Memorial Hermann Hospital, on January 15, 2022. 
        
        Expert witness Dr. James Rodriguez, a gastroenterology specialist from Houston Methodist Hospital,
        testified that the standard of care was breached during the surgical procedure.
        
        The jury awarded plaintiff $150,000 in damages. State Farm Insurance provided coverage
        for the hospital's liability under their professional liability policy.
        
        The case was heard before a jury in Harris County District Court, with Judge Wilson
        providing jury instructions on medical malpractice standards in Texas.
        """
        
        logger.info(f"✅ Text contains 'Dr. Lisa Chen': {'Dr. Lisa Chen' in original_text}")
        logger.info(f"✅ Text contains 'Dr. James Rodriguez': {'Dr. James Rodriguez' in original_text}")
        logger.info(f"✅ Text contains 'expert witness': {'expert witness' in original_text}")
        logger.info(f"✅ Text contains 'specialist': {'specialist' in original_text}")
        logger.info(f"✅ Text contains 'testified': {'testified' in original_text}")
        logger.info(f"✅ Text contains 'gastroenterology': {'gastroenterology' in original_text}")
        
    finally:
        driver.close()

if __name__ == "__main__":
    debug_expert_classification()