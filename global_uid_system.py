#!/usr/bin/env python3
"""
Global UID Tracking System
Ensures cross-system consistency across Pinecone, Supabase, and Neo4j
"""

import os
import uuid
import hashlib
import json
import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import asyncio

from dotenv import load_dotenv
import redis
from supabase import create_client, Client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

class StorageSystem(Enum):
    """Supported storage systems"""
    GCS = "gcs"
    SUPABASE = "supabase" 
    PINECONE = "pinecone"
    NEO4J = "neo4j"

@dataclass
class GlobalUIDRecord:
    """Global UID record tracking across all systems"""
    global_uid: str
    document_id: str
    chunk_id: Optional[str]
    entity_id: Optional[str]
    
    # Source information
    source_system: str
    source_path: str
    
    # Storage system tracking
    gcs_path: Optional[str] = None
    supabase_case_id: Optional[str] = None
    supabase_chunk_id: Optional[str] = None
    pinecone_vector_id: Optional[str] = None
    neo4j_node_id: Optional[str] = None
    
    # Metadata
    document_hash: Optional[str] = None
    created_at: datetime = None
    updated_at: datetime = None
    
    # Cross-references
    related_uids: List[str] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if self.related_uids is None:
            self.related_uids = []

@dataclass 
class ConsistencyReport:
    """Report on cross-system data consistency"""
    total_records: int
    consistent_records: int
    inconsistent_records: int
    missing_in_systems: Dict[str, List[str]]
    orphaned_records: Dict[str, List[str]]
    validation_errors: List[str]
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
    
    @property
    def consistency_percentage(self) -> float:
        if self.total_records == 0:
            return 100.0
        return (self.consistent_records / self.total_records) * 100.0

class GlobalUIDManager:
    """
    Manages global UIDs for cross-system consistency tracking
    """
    
    def __init__(self):
        # Initialize connections
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        self.redis_host = os.getenv("REDIS_HOST", "localhost")
        self.redis_port = int(os.getenv("REDIS_PORT", 6379))
        
        if not all([self.supabase_url, self.supabase_key]):
            raise ValueError("Missing required environment variables for GlobalUIDManager")
        
        # Initialize clients
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)
        logger.info(f"Connecting to Supabase: {self.supabase_url}")
        
        # Initialize Redis for caching (optional)
        try:
            self.redis_client = redis.Redis(
                host=self.redis_host, 
                port=self.redis_port, 
                decode_responses=True,
                socket_connect_timeout=5
            )
            # Test connection
            self.redis_client.ping()
            self.redis_available = True
            logger.info("✅ Redis connection established for UID caching")
        except Exception as e:
            logger.warning(f"Redis not available, using in-memory cache: {e}")
            self.redis_available = False
            self._memory_cache = {}
        
        # Create global UID table if it doesn't exist
        self._ensure_uid_table()
        
        logger.info("✅ Global UID Manager initialized")
    
    def _ensure_uid_table(self):
        """Verify the global UID tracking table exists in Supabase"""
        try:
            # Test table access with a simple query
            result = self.supabase.table("global_uid_tracking").select("id").limit(1).execute()
            logger.info("✅ Global UID tracking table accessible")
            
        except Exception as e:
            logger.error(f"Error accessing UID table: {e}")
            logger.info("Table should be created via Supabase migrations or dashboard")
    
    def generate_global_uid(self, 
                          document_id: str,
                          chunk_id: Optional[str] = None,
                          entity_id: Optional[str] = None,
                          source_system: str = "gcs") -> str:
        """
        Generate a deterministic global UID for cross-system tracking
        """
        # Create deterministic UID based on identifiers
        uid_components = [
            source_system,
            document_id,
            chunk_id or "",
            entity_id or ""
        ]
        
        # Create hash for deterministic UID
        uid_string = "|".join(uid_components)
        uid_hash = hashlib.sha256(uid_string.encode()).hexdigest()[:16]
        
        # Format as recognizable global UID
        global_uid = f"txlaw_{source_system}_{uid_hash}"
        
        logger.debug(f"Generated global UID: {global_uid} for {uid_string}")
        return global_uid
    
    async def register_global_uid(self, record: GlobalUIDRecord) -> bool:
        """
        Register a new global UID record for cross-system tracking
        """
        try:
            # Update timestamp
            record.updated_at = datetime.utcnow()
            
            # Convert to dict for Supabase
            record_dict = asdict(record)
            
            # Convert datetime objects to ISO strings
            record_dict['created_at'] = record.created_at.isoformat()
            record_dict['updated_at'] = record.updated_at.isoformat()
            
            # Convert related_uids list to JSON
            record_dict['related_uids'] = json.dumps(record.related_uids)
            
            # Insert or update in Supabase
            result = self.supabase.table("global_uid_tracking").upsert(
                record_dict, 
                on_conflict="global_uid"
            ).execute()
            
            if result.data:
                # Cache the record
                await self._cache_uid_record(record)
                logger.info(f"✅ Registered global UID: {record.global_uid}")
                return True
            else:
                logger.error(f"Failed to register global UID: {record.global_uid} - No data returned")
                logger.debug(f"Supabase result: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error registering global UID {record.global_uid}: {e}")
            return False
    
    async def update_storage_reference(self, 
                                     global_uid: str,
                                     storage_system: StorageSystem,
                                     storage_id: str) -> bool:
        """
        Update storage system reference for a global UID
        """
        try:
            # Determine which field to update
            field_mapping = {
                StorageSystem.GCS: "gcs_path",
                StorageSystem.SUPABASE: "supabase_case_id", 
                StorageSystem.PINECONE: "pinecone_vector_id",
                StorageSystem.NEO4J: "neo4j_node_id"
            }
            
            field_name = field_mapping.get(storage_system)
            if not field_name:
                raise ValueError(f"Unknown storage system: {storage_system}")
            
            # Update in Supabase
            update_data = {
                field_name: storage_id,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            result = self.supabase.table("global_uid_tracking").update(
                update_data
            ).eq("global_uid", global_uid).execute()
            
            if result.data:
                # Update cache
                await self._update_cache_field(global_uid, field_name, storage_id)
                logger.info(f"✅ Updated {storage_system.value} reference for UID {global_uid}")
                return True
            else:
                logger.warning(f"No record found for global UID: {global_uid}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating storage reference for {global_uid}: {e}")
            return False
    
    async def get_uid_record(self, global_uid: str) -> Optional[GlobalUIDRecord]:
        """
        Retrieve a global UID record with all cross-system references
        """
        try:
            # Try cache first
            cached_record = await self._get_cached_uid_record(global_uid)
            if cached_record:
                return cached_record
            
            # Fetch from Supabase
            result = self.supabase.table("global_uid_tracking").select("*").eq(
                "global_uid", global_uid
            ).execute()
            
            if result.data and len(result.data) > 0:
                record_data = result.data[0]
                
                # Convert back to GlobalUIDRecord
                record = GlobalUIDRecord(
                    global_uid=record_data["global_uid"],
                    document_id=record_data["document_id"],
                    chunk_id=record_data.get("chunk_id"),
                    entity_id=record_data.get("entity_id"),
                    source_system=record_data["source_system"],
                    source_path=record_data["source_path"],
                    gcs_path=record_data.get("gcs_path"),
                    supabase_case_id=record_data.get("supabase_case_id"),
                    supabase_chunk_id=record_data.get("supabase_chunk_id"),
                    pinecone_vector_id=record_data.get("pinecone_vector_id"),
                    neo4j_node_id=record_data.get("neo4j_node_id"),
                    document_hash=record_data.get("document_hash"),
                    created_at=self._parse_timestamp(record_data["created_at"]),
                    updated_at=self._parse_timestamp(record_data["updated_at"]),
                    related_uids=json.loads(record_data.get("related_uids", "[]"))
                )
                
                # Cache the record
                await self._cache_uid_record(record)
                return record
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving UID record {global_uid}: {e}")
            return None
    
    async def find_by_document_id(self, document_id: str) -> List[GlobalUIDRecord]:
        """
        Find all global UID records for a document
        """
        try:
            result = self.supabase.table("global_uid_tracking").select("*").eq(
                "document_id", document_id
            ).execute()
            
            records = []
            if result.data:
                for record_data in result.data:
                    record = GlobalUIDRecord(
                        global_uid=record_data["global_uid"],
                        document_id=record_data["document_id"],
                        chunk_id=record_data.get("chunk_id"),
                        entity_id=record_data.get("entity_id"),
                        source_system=record_data["source_system"],
                        source_path=record_data["source_path"],
                        gcs_path=record_data.get("gcs_path"),
                        supabase_case_id=record_data.get("supabase_case_id"),
                        supabase_chunk_id=record_data.get("supabase_chunk_id"),
                        pinecone_vector_id=record_data.get("pinecone_vector_id"),
                        neo4j_node_id=record_data.get("neo4j_node_id"),
                        document_hash=record_data.get("document_hash"),
                        created_at=self._parse_timestamp(record_data["created_at"]),
                        updated_at=self._parse_timestamp(record_data["updated_at"]),
                        related_uids=json.loads(record_data.get("related_uids", "[]"))
                    )
                    records.append(record)
            
            return records
            
        except Exception as e:
            logger.error(f"Error finding records for document {document_id}: {e}")
            return []
    
    async def validate_consistency(self, 
                                 document_ids: Optional[List[str]] = None,
                                 limit: int = 1000) -> ConsistencyReport:
        """
        Validate cross-system consistency for global UIDs
        """
        try:
            # Build query
            query = self.supabase.table("global_uid_tracking").select("*")
            
            if document_ids:
                query = query.in_("document_id", document_ids)
            
            # Limit results
            result = query.limit(limit).execute()
            
            total_records = len(result.data) if result.data else 0
            consistent_records = 0
            inconsistent_records = 0
            missing_in_systems = {system.value: [] for system in StorageSystem}
            orphaned_records = {system.value: [] for system in StorageSystem}
            validation_errors = []
            
            if result.data:
                for record_data in result.data:
                    global_uid = record_data["global_uid"]
                    
                    # Check each storage system
                    system_refs = {
                        "gcs": record_data.get("gcs_path"),
                        "supabase": record_data.get("supabase_case_id"),
                        "pinecone": record_data.get("pinecone_vector_id"),
                        "neo4j": record_data.get("neo4j_node_id")
                    }
                    
                    # Count non-null references
                    active_systems = sum(1 for ref in system_refs.values() if ref)
                    
                    if active_systems >= 2:  # Consider consistent if in at least 2 systems
                        consistent_records += 1
                    else:
                        inconsistent_records += 1
                        
                        # Track missing systems
                        for system, ref in system_refs.items():
                            if not ref:
                                missing_in_systems[system].append(global_uid)
            
            return ConsistencyReport(
                total_records=total_records,
                consistent_records=consistent_records,
                inconsistent_records=inconsistent_records,
                missing_in_systems=missing_in_systems,
                orphaned_records=orphaned_records,
                validation_errors=validation_errors
            )
            
        except Exception as e:
            logger.error(f"Error validating consistency: {e}")
            return ConsistencyReport(
                total_records=0,
                consistent_records=0,
                inconsistent_records=0,
                missing_in_systems={},
                orphaned_records={},
                validation_errors=[str(e)]
            )
    
    async def _cache_uid_record(self, record: GlobalUIDRecord):
        """Cache a UID record for fast retrieval"""
        try:
            if self.redis_available:
                # Cache in Redis with 1 hour TTL
                cache_key = f"uid:{record.global_uid}"
                cache_data = json.dumps(asdict(record), default=str)
                self.redis_client.setex(cache_key, 3600, cache_data)
            else:
                # Cache in memory
                self._memory_cache[record.global_uid] = record
        except Exception as e:
            logger.debug(f"Failed to cache UID record: {e}")
    
    async def _get_cached_uid_record(self, global_uid: str) -> Optional[GlobalUIDRecord]:
        """Retrieve cached UID record"""
        try:
            if self.redis_available:
                cache_key = f"uid:{global_uid}"
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    record_dict = json.loads(cached_data)
                    # Convert datetime strings back
                    record_dict['created_at'] = datetime.fromisoformat(record_dict['created_at'])
                    record_dict['updated_at'] = datetime.fromisoformat(record_dict['updated_at'])
                    return GlobalUIDRecord(**record_dict)
            else:
                # Check memory cache
                return self._memory_cache.get(global_uid)
        except Exception as e:
            logger.debug(f"Failed to retrieve cached UID record: {e}")
        
        return None
    
    async def _update_cache_field(self, global_uid: str, field_name: str, value: str):
        """Update a specific field in cached record"""
        try:
            cached_record = await self._get_cached_uid_record(global_uid)
            if cached_record:
                setattr(cached_record, field_name, value)
                cached_record.updated_at = datetime.utcnow()
                await self._cache_uid_record(cached_record)
        except Exception as e:
            logger.debug(f"Failed to update cached field: {e}")
    
    def _parse_timestamp(self, timestamp_str: str) -> datetime:
        """Parse timestamp string handling various Supabase formats"""
        try:
            # Handle Supabase timestamp format with microseconds
            if timestamp_str.endswith('+00:00'):
                # Remove timezone and handle microseconds
                clean_str = timestamp_str.replace('+00:00', '')
                if '.' in clean_str:
                    # Truncate microseconds to 6 digits if longer
                    parts = clean_str.split('.')
                    if len(parts[1]) > 6:
                        clean_str = f"{parts[0]}.{parts[1][:6]}"
                return datetime.fromisoformat(clean_str).replace(tzinfo=None)
            else:
                return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).replace(tzinfo=None)
        except Exception as e:
            logger.warning(f"Error parsing timestamp {timestamp_str}: {e}")
            return datetime.utcnow()

# Test function
async def test_global_uid_system():
    """Test the global UID tracking system"""
    
    print("=== Testing Global UID Tracking System ===\n")
    
    try:
        # Initialize UID manager
        uid_manager = GlobalUIDManager()
        
        # Test 1: Generate global UIDs
        print("1. Testing global UID generation...")
        document_id = "test_doc_001"
        chunk_id = "test_doc_001_chunk_1"
        
        global_uid_doc = uid_manager.generate_global_uid(document_id, source_system="gcs")
        global_uid_chunk = uid_manager.generate_global_uid(document_id, chunk_id, source_system="gcs")
        
        print(f"✅ Document UID: {global_uid_doc}")
        print(f"✅ Chunk UID: {global_uid_chunk}")
        
        # Test 2: Register global UID records
        print("\n2. Testing UID record registration...")
        
        # Document record
        doc_record = GlobalUIDRecord(
            global_uid=global_uid_doc,
            document_id=document_id,
            chunk_id=None,
            entity_id=None,
            source_system="gcs",
            source_path="courtlistener/opinions/2023/cluster_123/opinion_456.txt",
            gcs_path="courtlistener/opinions/2023/cluster_123/opinion_456.txt",
            document_hash="abc123def456"
        )
        
        # Chunk record
        chunk_record = GlobalUIDRecord(
            global_uid=global_uid_chunk,
            document_id=document_id,
            chunk_id=chunk_id,
            entity_id=None,
            source_system="gcs",
            source_path="courtlistener/opinions/2023/cluster_123/opinion_456.txt",
            gcs_path="courtlistener/opinions/2023/cluster_123/opinion_456.txt",
            related_uids=[global_uid_doc]
        )
        
        # Register records
        doc_success = await uid_manager.register_global_uid(doc_record)
        chunk_success = await uid_manager.register_global_uid(chunk_record)
        
        print(f"✅ Document record registered: {doc_success}")
        print(f"✅ Chunk record registered: {chunk_success}")
        
        # Test 3: Update storage references
        print("\n3. Testing storage reference updates...")
        
        # Simulate storing in different systems
        await uid_manager.update_storage_reference(
            global_uid_doc, StorageSystem.SUPABASE, "case_456"
        )
        await uid_manager.update_storage_reference(
            global_uid_chunk, StorageSystem.PINECONE, "vector_789"
        )
        await uid_manager.update_storage_reference(
            global_uid_chunk, StorageSystem.NEO4J, "node_101112"
        )
        
        print("✅ Storage references updated")
        
        # Test 4: Retrieve records
        print("\n4. Testing record retrieval...")
        
        retrieved_doc = await uid_manager.get_uid_record(global_uid_doc)
        retrieved_chunk = await uid_manager.get_uid_record(global_uid_chunk)
        
        if retrieved_doc:
            print(f"✅ Retrieved document record:")
            print(f"   Global UID: {retrieved_doc.global_uid}")
            print(f"   GCS Path: {retrieved_doc.gcs_path}")
            print(f"   Supabase ID: {retrieved_doc.supabase_case_id}")
        
        if retrieved_chunk:
            print(f"✅ Retrieved chunk record:")
            print(f"   Global UID: {retrieved_chunk.global_uid}")
            print(f"   Pinecone ID: {retrieved_chunk.pinecone_vector_id}")
            print(f"   Neo4j ID: {retrieved_chunk.neo4j_node_id}")
            print(f"   Related UIDs: {retrieved_chunk.related_uids}")
        
        # Test 5: Find by document ID
        print("\n5. Testing document ID lookup...")
        
        doc_records = await uid_manager.find_by_document_id(document_id)
        print(f"✅ Found {len(doc_records)} records for document {document_id}")
        
        # Test 6: Validate consistency
        print("\n6. Testing consistency validation...")
        
        consistency_report = await uid_manager.validate_consistency(
            document_ids=[document_id]
        )
        
        print(f"✅ Consistency Report:")
        print(f"   Total Records: {consistency_report.total_records}")
        print(f"   Consistent: {consistency_report.consistent_records}")
        print(f"   Inconsistent: {consistency_report.inconsistent_records}")
        print(f"   Consistency: {consistency_report.consistency_percentage:.1f}%")
        
        print("\n✅ Global UID System test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Global UID System test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_global_uid_system())