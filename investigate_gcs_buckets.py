#!/usr/bin/env python3
"""
Investigation script to determine which GCS bucket contains Texas legal documents
Tests both texas-laws-personalinjury and newtexaslaw-legal-docs-1755374465
"""

import os
import asyncio
from google.cloud import storage
from dotenv import load_dotenv

load_dotenv()

async def investigate_bucket(bucket_name: str) -> dict:
    """Investigate a GCS bucket to understand its contents"""
    
    print(f"\n🔍 Investigating bucket: {bucket_name}")
    result = {
        "bucket_name": bucket_name,
        "accessible": False,
        "total_files": 0,
        "sample_files": [],
        "error": None
    }
    
    try:
        # Initialize GCS client using service account from env
        client = storage.Client()
        bucket = client.bucket(bucket_name)
        
        # Test bucket access by listing first few objects
        blobs = list(bucket.list_blobs(max_results=20))
        
        result["accessible"] = True
        result["total_files"] = len(blobs)
        result["sample_files"] = [blob.name for blob in blobs[:10]]
        
        print(f"✅ Bucket {bucket_name} is accessible")
        print(f"📄 Found {len(blobs)} files (showing first 10)")
        for i, file_path in enumerate(result["sample_files"], 1):
            print(f"   {i:2d}. {file_path}")
        
        # Look for Texas-specific patterns
        texas_files = [f for f in result["sample_files"] if any(keyword in f.lower() 
                      for keyword in ['texas', 'tx', 'court', 'case', '.txt', '.pdf'])]
        
        if texas_files:
            print(f"🎯 Texas-related files found: {len(texas_files)}")
            for tx_file in texas_files[:5]:
                print(f"   - {tx_file}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error accessing bucket {bucket_name}: {e}")
        result["error"] = str(e)
        return result

async def main():
    print("=== GCS Bucket Investigation ===")
    print("🎯 Goal: Determine which bucket contains Texas legal documents")
    
    # Test both buckets
    bucket1 = "texas-laws-personalinjury"  # User's suspected bucket
    bucket2 = "newtexaslaw-legal-docs-1755374465"  # Current config
    
    # Investigate both buckets
    result1 = await investigate_bucket(bucket1)
    result2 = await investigate_bucket(bucket2)
    
    print("\n" + "="*60)
    print("📊 INVESTIGATION SUMMARY")
    print("="*60)
    
    # Compare results
    buckets = [
        (bucket1, result1),
        (bucket2, result2)
    ]
    
    for bucket_name, result in buckets:
        status = "✅ ACCESSIBLE" if result["accessible"] else "❌ INACCESSIBLE"
        print(f"\n🗂️  {bucket_name}")
        print(f"   Status: {status}")
        print(f"   Files: {result['total_files']}")
        if result["error"]:
            print(f"   Error: {result['error']}")
    
    # Determine recommendation
    print(f"\n🎯 RECOMMENDATION:")
    
    accessible_buckets = [r for r in [result1, result2] if r["accessible"]]
    
    if len(accessible_buckets) == 0:
        print("❌ Neither bucket is accessible - check authentication")
    elif len(accessible_buckets) == 1:
        recommended = accessible_buckets[0]
        print(f"✅ Use: {recommended['bucket_name']}")
        print(f"   (Only accessible bucket with {recommended['total_files']} files)")
    else:
        # Both accessible - recommend based on content
        if result1["accessible"] and result1["total_files"] > 0:
            print(f"✅ RECOMMENDED: {bucket1}")
            print(f"   Contains {result1['total_files']} files and matches your expected name")
            print(f"   UPDATE .env to: GCS_BUCKET_NAME={bucket1}")
        elif result2["accessible"] and result2["total_files"] > 0:
            print(f"✅ Current bucket {bucket2} is working")
            print(f"   Contains {result2['total_files']} files - continue using it")
        else:
            print("⚠️  Both buckets accessible but investigation needed")

if __name__ == "__main__":
    asyncio.run(main())