#!/usr/bin/env python3
"""
4-System Evidence Verification Tool
Validates concrete evidence from scale testing results to prove system functionality

This script takes scale test results and performs additional verification queries
to provide definitive proof of what's working and what's not across all systems.
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import sys

# Add processing to path
sys.path.insert(0, str(Path(__file__).parent / "courtlistener" / "processing" / "src"))

try:
    from processing.storage.supabase_connector import SupabaseConnector
    from processing.storage.neo4j_connector import Neo4jConnector
    from processing.storage.pinecone_connector import PineconeConnector
    from pinecone import Pinecone
    from neo4j import GraphDatabase
    from supabase import create_client
    IMPORTS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Import error: {e}. Some verification features may not work.")
    IMPORTS_AVAILABLE = False

from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
load_dotenv()

@dataclass
class EvidenceVerificationResult:
    """Result of evidence verification"""
    system: str
    verification_type: str
    expected_data: Any
    actual_data: Any
    verified: bool
    evidence_details: Dict[str, Any]
    verification_timestamp: str

class FourSystemEvidenceVerifier:
    """
    Verifies evidence captured during 4-system scale testing
    Provides concrete proof of system functionality
    """
    
    def __init__(self):
        """Initialize evidence verifier"""
        self.verification_id = f"evidence_verify_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"🔍 Initializing 4-System Evidence Verifier: {self.verification_id}")
        
        # Initialize connections to all systems
        self._initialize_system_connections()
        
        self.verification_results = []
    
    def _initialize_system_connections(self):
        """Initialize connections to all storage systems for verification"""
        try:
            # Supabase connection
            if os.getenv("SUPABASE_URL") and os.getenv("SUPABASE_SERVICE_ROLE_KEY"):
                self.supabase = create_client(
                    os.getenv("SUPABASE_URL"),
                    os.getenv("SUPABASE_SERVICE_ROLE_KEY")
                )
                logger.info("✅ Supabase connection initialized")
            else:
                self.supabase = None
                logger.warning("⚠️ Supabase connection not available")
            
            # Neo4j connection
            if os.getenv("NEO4J_URI") and os.getenv("NEO4J_PASSWORD"):
                self.neo4j_driver = GraphDatabase.driver(
                    os.getenv("NEO4J_URI"),
                    auth=(os.getenv("NEO4J_USERNAME", "neo4j"), os.getenv("NEO4J_PASSWORD"))
                )
                logger.info("✅ Neo4j connection initialized")
            else:
                self.neo4j_driver = None
                logger.warning("⚠️ Neo4j connection not available")
            
            # Pinecone connection
            if os.getenv("PINECONE_API_KEY"):
                self.pinecone = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
                self.pinecone_index = self.pinecone.Index(
                    os.getenv("PINECONE_INDEX_NAME", "legal-documents")
                )
                logger.info("✅ Pinecone connection initialized")
            else:
                self.pinecone = None
                self.pinecone_index = None
                logger.warning("⚠️ Pinecone connection not available")
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize system connections: {e}")
            raise
    
    async def verify_scale_test_evidence(self, test_report_path: str) -> List[EvidenceVerificationResult]:
        """
        Verify evidence from a scale test report
        """
        logger.info(f"🔍 Starting evidence verification for: {test_report_path}")
        
        # Load test report
        with open(test_report_path, 'r') as f:
            test_report = json.load(f)
        
        logger.info(f"📊 Loaded test report: {test_report['test_id']}")
        logger.info(f"   Documents processed: {test_report['total_documents']}")
        logger.info(f"   Success rate: {test_report['overall_success_rate']:.1f}%")
        
        # Verify evidence for each successful document
        for doc_result in test_report['document_results']:
            if doc_result['overall_success']:
                await self._verify_document_evidence(doc_result)
        
        # Verify aggregate evidence
        await self._verify_aggregate_evidence(test_report)
        
        # Generate verification summary
        self._generate_verification_summary()
        
        return self.verification_results
    
    async def _verify_document_evidence(self, doc_result: Dict[str, Any]):
        """Verify evidence for a single document across all systems"""
        doc_id = doc_result['document_id']
        global_uid = doc_result['storage_evidence']['global_uid']
        
        logger.info(f"🔍 Verifying evidence for document: {doc_id}")
        
        # Verify Supabase evidence
        await self._verify_supabase_evidence(doc_id, global_uid, doc_result)
        
        # Verify Neo4j evidence
        await self._verify_neo4j_evidence(doc_id, global_uid, doc_result)
        
        # Verify Pinecone evidence
        await self._verify_pinecone_evidence(doc_id, global_uid, doc_result)
        
        # Verify cross-system consistency
        await self._verify_cross_system_evidence(doc_id, global_uid, doc_result)
    
    async def _verify_supabase_evidence(self, doc_id: str, global_uid: str, doc_result: Dict[str, Any]):
        """Verify Supabase storage evidence"""
        if not self.supabase:
            return
        
        try:
            supabase_evidence = doc_result['storage_evidence']['supabase_storage']
            
            # Verify case record exists
            case_query = self.supabase.table('cases').select('*').eq('global_uid', global_uid).execute()
            
            verification = EvidenceVerificationResult(
                system="supabase",
                verification_type="case_record_existence",
                expected_data={"global_uid": global_uid, "case_created": True},
                actual_data={"records_found": len(case_query.data), "global_uid": global_uid},
                verified=len(case_query.data) > 0,
                evidence_details={
                    "query": f"SELECT * FROM cases WHERE global_uid = '{global_uid}'",
                    "result_count": len(case_query.data),
                    "expected_chunks": supabase_evidence.get('chunk_records_created', 0),
                    "expected_entities": supabase_evidence.get('entity_records_created', 0)
                },
                verification_timestamp=datetime.now().isoformat()
            )
            
            self.verification_results.append(verification)
            
            if verification.verified:
                logger.info(f"✅ Supabase verification passed for {doc_id}")
            else:
                logger.warning(f"⚠️ Supabase verification failed for {doc_id}")
                
            # Verify chunk records
            if len(case_query.data) > 0:
                case_id = case_query.data[0]['id']
                chunk_query = self.supabase.table('chunks').select('*').eq('case_id', case_id).execute()
                
                chunk_verification = EvidenceVerificationResult(
                    system="supabase",
                    verification_type="chunk_records",
                    expected_data={"chunk_count": supabase_evidence.get('chunk_records_created', 0)},
                    actual_data={"chunk_count": len(chunk_query.data)},
                    verified=len(chunk_query.data) > 0,
                    evidence_details={
                        "query": f"SELECT * FROM chunks WHERE case_id = '{case_id}'",
                        "chunks_found": len(chunk_query.data),
                        "sample_chunk_ids": [chunk['id'] for chunk in chunk_query.data[:3]]
                    },
                    verification_timestamp=datetime.now().isoformat()
                )
                
                self.verification_results.append(chunk_verification)
        
        except Exception as e:
            logger.error(f"❌ Supabase verification error for {doc_id}: {e}")
            
            error_verification = EvidenceVerificationResult(
                system="supabase",
                verification_type="error",
                expected_data={"success": True},
                actual_data={"error": str(e)},
                verified=False,
                evidence_details={"error_message": str(e)},
                verification_timestamp=datetime.now().isoformat()
            )
            
            self.verification_results.append(error_verification)
    
    async def _verify_neo4j_evidence(self, doc_id: str, global_uid: str, doc_result: Dict[str, Any]):
        """Verify Neo4j graph evidence"""
        if not self.neo4j_driver:
            return
        
        try:
            neo4j_evidence = doc_result['graphrag_evidence']
            
            with self.neo4j_driver.session() as session:
                # Verify entities exist
                entity_query = """
                MATCH (n:__KGBuilder__)
                WHERE n.global_uid = $global_uid
                AND NOT n:Chunk
                RETURN count(n) as entity_count, collect(n.name) as entity_names
                """
                
                entity_result = session.run(entity_query, global_uid=global_uid)
                entity_record = entity_result.single()
                
                entity_verification = EvidenceVerificationResult(
                    system="neo4j",
                    verification_type="entity_nodes",
                    expected_data={"entity_count": len(neo4j_evidence.get('entities_extracted', []))},
                    actual_data={
                        "entity_count": entity_record['entity_count'] if entity_record else 0,
                        "entity_names": entity_record['entity_names'] if entity_record else []
                    },
                    verified=(entity_record and entity_record['entity_count'] > 0),
                    evidence_details={
                        "query": entity_query,
                        "expected_entities": [e['name'] for e in neo4j_evidence.get('entities_extracted', [])],
                        "found_entities": entity_record['entity_names'] if entity_record else []
                    },
                    verification_timestamp=datetime.now().isoformat()
                )
                
                self.verification_results.append(entity_verification)
                
                # Verify relationships exist
                relationship_query = """
                MATCH (a:__KGBuilder__)-[r]->(b:__KGBuilder__)
                WHERE a.global_uid = $global_uid OR b.global_uid = $global_uid
                RETURN count(r) as relationship_count, collect(type(r)) as relationship_types
                """
                
                rel_result = session.run(relationship_query, global_uid=global_uid)
                rel_record = rel_result.single()
                
                relationship_verification = EvidenceVerificationResult(
                    system="neo4j",
                    verification_type="relationships",
                    expected_data={"relationship_count": len(neo4j_evidence.get('relationships_extracted', []))},
                    actual_data={
                        "relationship_count": rel_record['relationship_count'] if rel_record else 0,
                        "relationship_types": rel_record['relationship_types'] if rel_record else []
                    },
                    verified=(rel_record and rel_record['relationship_count'] > 0),
                    evidence_details={
                        "query": relationship_query,
                        "expected_relationships": [r['relation'] for r in neo4j_evidence.get('relationships_extracted', [])],
                        "found_relationship_types": rel_record['relationship_types'] if rel_record else []
                    },
                    verification_timestamp=datetime.now().isoformat()
                )
                
                self.verification_results.append(relationship_verification)
                
                if entity_verification.verified and relationship_verification.verified:
                    logger.info(f"✅ Neo4j verification passed for {doc_id}")
                else:
                    logger.warning(f"⚠️ Neo4j verification failed for {doc_id}")
        
        except Exception as e:
            logger.error(f"❌ Neo4j verification error for {doc_id}: {e}")
            
            error_verification = EvidenceVerificationResult(
                system="neo4j",
                verification_type="error",
                expected_data={"success": True},
                actual_data={"error": str(e)},
                verified=False,
                evidence_details={"error_message": str(e)},
                verification_timestamp=datetime.now().isoformat()
            )
            
            self.verification_results.append(error_verification)
    
    async def _verify_pinecone_evidence(self, doc_id: str, global_uid: str, doc_result: Dict[str, Any]):
        """Verify Pinecone vector evidence"""
        if not self.pinecone_index:
            return
        
        try:
            pinecone_evidence = doc_result['storage_evidence']['pinecone_storage']
            
            # Query vectors by metadata filter
            query_vector = [0.0] * 1024  # Dummy query vector
            
            query_response = self.pinecone_index.query(
                vector=query_vector,
                filter={"global_uid": global_uid},
                top_k=50,
                namespace="tx",
                include_metadata=True
            )
            
            vector_verification = EvidenceVerificationResult(
                system="pinecone",
                verification_type="vector_storage",
                expected_data={"vectors_upserted": pinecone_evidence.get('vectors_upserted', 0)},
                actual_data={
                    "vectors_found": len(query_response.matches),
                    "sample_vector_ids": [match.id for match in query_response.matches[:3]]
                },
                verified=len(query_response.matches) > 0,
                evidence_details={
                    "query_filter": {"global_uid": global_uid},
                    "namespace": "tx",
                    "vectors_found": len(query_response.matches),
                    "sample_metadata": [match.metadata for match in query_response.matches[:2]]
                },
                verification_timestamp=datetime.now().isoformat()
            )
            
            self.verification_results.append(vector_verification)
            
            if vector_verification.verified:
                logger.info(f"✅ Pinecone verification passed for {doc_id}")
                
                # Test semantic search functionality
                search_verification = await self._test_pinecone_search(doc_id, global_uid)
                self.verification_results.append(search_verification)
            else:
                logger.warning(f"⚠️ Pinecone verification failed for {doc_id}")
        
        except Exception as e:
            logger.error(f"❌ Pinecone verification error for {doc_id}: {e}")
            
            error_verification = EvidenceVerificationResult(
                system="pinecone",
                verification_type="error",
                expected_data={"success": True},
                actual_data={"error": str(e)},
                verified=False,
                evidence_details={"error_message": str(e)},
                verification_timestamp=datetime.now().isoformat()
            )
            
            self.verification_results.append(error_verification)
    
    async def _test_pinecone_search(self, doc_id: str, global_uid: str) -> EvidenceVerificationResult:
        """Test Pinecone semantic search functionality"""
        try:
            # Create a test query vector (mock legal query)
            # In real implementation, this would use actual Voyage embedding
            test_query_vector = [0.1] * 1024  # Mock query for "personal injury case"
            
            search_response = self.pinecone_index.query(
                vector=test_query_vector,
                top_k=10,
                namespace="tx",
                include_metadata=True
            )
            
            # Check if our document appears in search results
            found_in_search = any(
                match.metadata and match.metadata.get('global_uid') == global_uid 
                for match in search_response.matches
            )
            
            return EvidenceVerificationResult(
                system="pinecone",
                verification_type="semantic_search",
                expected_data={"document_searchable": True},
                actual_data={
                    "found_in_search": found_in_search,
                    "total_results": len(search_response.matches),
                    "top_similarity_score": search_response.matches[0].score if search_response.matches else 0
                },
                verified=found_in_search,
                evidence_details={
                    "test_query": "mock legal query",
                    "search_results_count": len(search_response.matches),
                    "target_global_uid": global_uid,
                    "found_in_results": found_in_search
                },
                verification_timestamp=datetime.now().isoformat()
            )
        
        except Exception as e:
            return EvidenceVerificationResult(
                system="pinecone",
                verification_type="semantic_search_error",
                expected_data={"success": True},
                actual_data={"error": str(e)},
                verified=False,
                evidence_details={"error_message": str(e)},
                verification_timestamp=datetime.now().isoformat()
            )
    
    async def _verify_cross_system_evidence(self, doc_id: str, global_uid: str, doc_result: Dict[str, Any]):
        """Verify cross-system consistency evidence"""
        
        # Count systems where global_uid was found
        systems_with_uid = []
        
        for result in self.verification_results:
            if (result.system in ["supabase", "neo4j", "pinecone"] and 
                "global_uid" in str(result.evidence_details) and 
                result.verified):
                if result.system not in systems_with_uid:
                    systems_with_uid.append(result.system)
        
        cross_system_verification = EvidenceVerificationResult(
            system="cross_system",
            verification_type="global_uid_consistency",
            expected_data={"systems_with_uid": ["supabase", "neo4j", "pinecone"]},
            actual_data={"systems_with_uid": systems_with_uid},
            verified=len(systems_with_uid) >= 2,  # At least 2 systems should have the UID
            evidence_details={
                "global_uid": global_uid,
                "systems_verified": systems_with_uid,
                "consistency_score": len(systems_with_uid) / 3,  # Out of 3 systems
                "document_id": doc_id
            },
            verification_timestamp=datetime.now().isoformat()
        )
        
        self.verification_results.append(cross_system_verification)
        
        if cross_system_verification.verified:
            logger.info(f"✅ Cross-system consistency verified for {doc_id}")
        else:
            logger.warning(f"⚠️ Cross-system consistency issues for {doc_id}")
    
    async def _verify_aggregate_evidence(self, test_report: Dict[str, Any]):
        """Verify aggregate evidence from the test report"""
        logger.info("🔍 Verifying aggregate evidence from test report")
        
        # Verify system success rates make sense
        success_rates = {
            "gcs": test_report.get('gcs_success_rate', 0),
            "graphrag": test_report.get('graphrag_success_rate', 0),
            "embedding": test_report.get('embedding_success_rate', 0),
            "storage": test_report.get('storage_success_rate', 0)
        }
        
        success_rate_verification = EvidenceVerificationResult(
            system="aggregate",
            verification_type="success_rates",
            expected_data={"all_rates_positive": True, "reasonable_rates": True},
            actual_data=success_rates,
            verified=all(rate > 0 for rate in success_rates.values()),
            evidence_details={
                "success_rates": success_rates,
                "overall_success_rate": test_report.get('overall_success_rate', 0),
                "total_documents": test_report.get('total_documents', 0)
            },
            verification_timestamp=datetime.now().isoformat()
        )
        
        self.verification_results.append(success_rate_verification)
        
        # Verify cost and performance metrics
        total_costs = test_report.get('total_costs', {})
        
        cost_verification = EvidenceVerificationResult(
            system="aggregate",
            verification_type="cost_metrics",
            expected_data={"costs_tracked": True, "reasonable_costs": True},
            actual_data=total_costs,
            verified=sum(total_costs.values()) > 0,
            evidence_details={
                "total_costs": total_costs,
                "average_processing_time": test_report.get('average_processing_time_per_doc', 0),
                "total_entities": test_report.get('total_entities_extracted', 0),
                "total_chunks": test_report.get('total_chunks_created', 0)
            },
            verification_timestamp=datetime.now().isoformat()
        )
        
        self.verification_results.append(cost_verification)
    
    def _generate_verification_summary(self):
        """Generate summary of verification results"""
        if not self.verification_results:
            return
        
        # Group results by system
        by_system = {}
        for result in self.verification_results:
            if result.system not in by_system:
                by_system[result.system] = []
            by_system[result.system].append(result)
        
        logger.info("\n📊 EVIDENCE VERIFICATION SUMMARY")
        logger.info("=" * 60)
        
        for system, results in by_system.items():
            verified_count = len([r for r in results if r.verified])
            total_count = len(results)
            success_rate = (verified_count / total_count) * 100 if total_count > 0 else 0
            
            status = "✅" if success_rate >= 80 else "⚠️" if success_rate >= 50 else "❌"
            
            logger.info(f"{status} {system.upper()}: {verified_count}/{total_count} verified ({success_rate:.1f}%)")
            
            # Show verification details
            for result in results:
                status_icon = "✅" if result.verified else "❌"
                logger.info(f"   {status_icon} {result.verification_type}: {result.verified}")
        
        # Overall verification rate
        total_verified = len([r for r in self.verification_results if r.verified])
        total_tests = len(self.verification_results)
        overall_rate = (total_verified / total_tests) * 100 if total_tests > 0 else 0
        
        logger.info(f"\n🎯 OVERALL VERIFICATION RATE: {total_verified}/{total_tests} ({overall_rate:.1f}%)")
        
        if overall_rate >= 80:
            logger.info("✅ EVIDENCE VERIFICATION SUCCESSFUL - System functionality proven!")
        elif overall_rate >= 60:
            logger.info("⚠️ PARTIAL EVIDENCE VERIFICATION - Some issues found")
        else:
            logger.info("❌ EVIDENCE VERIFICATION FAILED - Significant issues detected")
    
    def save_verification_report(self, filename: Optional[str] = None) -> str:
        """Save verification results to JSON file"""
        if not filename:
            filename = f"evidence_verification_report_{self.verification_id}.json"
        
        report_data = {
            "verification_id": self.verification_id,
            "verification_timestamp": datetime.now().isoformat(),
            "total_verifications": len(self.verification_results),
            "verified_count": len([r for r in self.verification_results if r.verified]),
            "verification_results": [
                {
                    "system": r.system,
                    "verification_type": r.verification_type,
                    "verified": r.verified,
                    "expected_data": r.expected_data,
                    "actual_data": r.actual_data,
                    "evidence_details": r.evidence_details,
                    "verification_timestamp": r.verification_timestamp
                }
                for r in self.verification_results
            ]
        }
        
        filepath = Path(filename)
        with open(filepath, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        logger.info(f"📄 Verification report saved to: {filepath.absolute()}")
        return str(filepath)

async def main():
    """Main verification function"""
    if len(sys.argv) < 2:
        print("Usage: python verify_4system_evidence.py <test_report.json>")
        sys.exit(1)
    
    test_report_path = sys.argv[1]
    
    if not Path(test_report_path).exists():
        print(f"❌ Test report file not found: {test_report_path}")
        sys.exit(1)
    
    logger.info("🔍 Starting 4-System Evidence Verification")
    
    try:
        verifier = FourSystemEvidenceVerifier()
        
        # Verify evidence from test report
        results = await verifier.verify_scale_test_evidence(test_report_path)
        
        # Save verification report
        report_path = verifier.save_verification_report()
        
        print(f"\n🎉 EVIDENCE VERIFICATION COMPLETE")
        print(f"📊 Total Verifications: {len(results)}")
        print(f"✅ Successful: {len([r for r in results if r.verified])}")
        print(f"❌ Failed: {len([r for r in results if not r.verified])}")
        print(f"📁 Verification Report: {report_path}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(main())
    if result:
        sys.exit(0)
    else:
        sys.exit(1)