#!/usr/bin/env python3
"""
Comprehensive GCS Bucket Analysis
Analyze ALL folders and data structures in texas-laws-personalinjury bucket
"""

import os
import json
import gzip
from collections import defaultdict
from google.cloud import storage
from dotenv import load_dotenv

load_dotenv()

def analyze_bucket_structure():
    print("=== COMPREHENSIVE GCS BUCKET ANALYSIS ===\n")
    
    try:
        # Initialize GCS client
        client = storage.Client()
        bucket = client.bucket("texas-laws-personalinjury")
        
        # Get ALL blobs to understand complete structure
        print("🔍 Scanning complete bucket structure...")
        all_blobs = list(bucket.list_blobs())
        
        print(f"📊 TOTAL FILES: {len(all_blobs):,}")
        
        # Analyze folder structure
        folder_stats = defaultdict(lambda: {"count": 0, "size": 0, "extensions": set(), "samples": []})
        
        for blob in all_blobs:
            path_parts = blob.name.split('/')
            
            # Determine top-level folder
            top_folder = path_parts[0] if len(path_parts) > 1 else "root"
            
            # Get file extension
            ext = "." + blob.name.split('.')[-1] if '.' in blob.name else "no_ext"
            
            # Update stats
            folder_stats[top_folder]["count"] += 1
            folder_stats[top_folder]["size"] += blob.size or 0
            folder_stats[top_folder]["extensions"].add(ext)
            
            # Keep samples
            if len(folder_stats[top_folder]["samples"]) < 5:
                folder_stats[top_folder]["samples"].append(blob.name)
        
        # Display folder analysis
        print(f"\n📁 FOLDER STRUCTURE ANALYSIS:")
        print("=" * 80)
        
        for folder, stats in sorted(folder_stats.items(), key=lambda x: x[1]["count"], reverse=True):
            size_mb = stats["size"] / (1024 * 1024)
            print(f"\n📂 {folder}/")
            print(f"   📄 Files: {stats['count']:,}")
            print(f"   💾 Size: {size_mb:.1f} MB")
            print(f"   🏷️  Extensions: {sorted(stats['extensions'])}")
            print(f"   📋 Samples:")
            for sample in stats["samples"]:
                print(f"      - {sample}")
        
        # Focus on TX/ folder analysis
        print(f"\n" + "=" * 80)
        print(f"🎯 DETAILED TX/ FOLDER ANALYSIS")
        print("=" * 80)
        
        tx_blobs = [blob for blob in all_blobs if blob.name.startswith("TX/")]
        
        if tx_blobs:
            print(f"📊 TX/ contains {len(tx_blobs):,} files")
            
            # Analyze TX/ subfolder structure
            tx_subfolders = defaultdict(lambda: {"count": 0, "samples": []})
            
            for blob in tx_blobs:
                path_parts = blob.name.split('/')
                if len(path_parts) >= 3:  # TX/subfolder/file
                    subfolder = path_parts[1]
                    tx_subfolders[subfolder]["count"] += 1
                    if len(tx_subfolders[subfolder]["samples"]) < 3:
                        tx_subfolders[subfolder]["samples"].append(blob.name)
            
            print(f"\n📁 TX/ Subfolders:")
            for subfolder, stats in sorted(tx_subfolders.items(), key=lambda x: x[1]["count"], reverse=True):
                print(f"   📂 TX/{subfolder}/ - {stats['count']:,} files")
                for sample in stats["samples"]:
                    print(f"      - {sample}")
            
            # Sample a few TX files to understand content
            print(f"\n🔍 SAMPLING TX/ FILE CONTENT:")
            sample_tx_files = tx_blobs[:3]  # First 3 files
            
            for blob in sample_tx_files:
                try:
                    print(f"\n📄 File: {blob.name}")
                    print(f"   Size: {blob.size:,} bytes")
                    
                    content = blob.download_as_bytes()
                    
                    # Handle gzipped content
                    if blob.name.endswith('.gz'):
                        try:
                            content = gzip.decompress(content)
                            print("   ✅ Decompressed gzipped content")
                        except:
                            print("   ⚠️  Failed to decompress - might not be gzipped")
                    
                    # Try to parse as JSON
                    try:
                        data = json.loads(content.decode('utf-8'))
                        print(f"   📊 JSON Structure:")
                        if isinstance(data, dict):
                            keys = list(data.keys())[:10]  # First 10 keys
                            print(f"      Keys: {keys}")
                            
                            # Look for case-related fields
                            case_fields = ['case_name', 'docket_id', 'court', 'date_filed', 'plain_text', 'html', 'opinions']
                            found_fields = [k for k in case_fields if k in data]
                            if found_fields:
                                print(f"      Legal fields found: {found_fields}")
                                
                                # Preview content fields
                                for field in ['plain_text', 'html'][:1]:  # Just first content field
                                    if field in data and isinstance(data[field], str):
                                        preview = data[field][:200].replace('\n', ' ')
                                        print(f"      {field} preview: {preview}...")
                        
                        elif isinstance(data, list):
                            print(f"      List with {len(data)} items")
                            if data and isinstance(data[0], dict):
                                print(f"      First item keys: {list(data[0].keys())[:10]}")
                    
                    except json.JSONDecodeError:
                        # Not JSON - try as plain text
                        try:
                            text_content = content.decode('utf-8')
                            preview = text_content[:300].replace('\n', ' ')
                            print(f"   📝 Text preview: {preview}...")
                        except:
                            print(f"   ⚠️  Binary content - cannot preview")
                
                except Exception as e:
                    print(f"   ❌ Error sampling file: {e}")
        
        else:
            print("❌ No files found in TX/ folder")
        
        # Summary recommendations
        print(f"\n" + "=" * 80)
        print(f"🎯 RECOMMENDATIONS FOR ENHANCED GCS CLIENT")
        print("=" * 80)
        
        if tx_blobs:
            print(f"✅ Use TX/ folder as primary data source ({len(tx_blobs):,} files)")
            print(f"✅ Update enhanced_gcs_client.py to handle TX/ structure")
            print(f"✅ Implement JSON parsing for cluster data")
            if any('.gz' in blob.name for blob in tx_blobs[:10]):
                print(f"✅ Add gzip decompression support")
        
        if folder_stats.get("FED", {}).get("count", 0) > 0:
            print(f"⚠️  FED/ folder has {folder_stats['FED']['count']} files - consider for federal cases")
        
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. Update enhanced_gcs_client.py find_texas_cases() to use TX/ prefix")
        print(f"   2. Add JSON parsing and gzip decompression")  
        print(f"   3. Extract case metadata from JSON structure")
        print(f"   4. Update documentation with complete data architecture")
        
    except Exception as e:
        print(f"❌ Bucket analysis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_bucket_structure()