#!/bin/bash
# View running background processes

echo "🔍 BACKGROUND PROCESSES STATUS"
echo "================================"

echo "📺 Screen Sessions:"
screen -list

echo ""
echo "🎯 Quick Commands:"
echo "   View bulk loader:        screen -r bulk_loader"
echo "   View classification:     screen -r classification_worker" 
echo "   View monitor:           screen -r progress_monitor"
echo "   Detach from session:    Ctrl+A, then D"
echo "   Quick status:           python quick_status.py"
echo ""
echo "📊 Current Progress:"
python quick_status.py
