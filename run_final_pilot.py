#!/usr/bin/env python3
"""
Final Pilot Processing with Fixed GraphRAG Pipeline
Process the 10 identified cases with working entity extraction
"""

import asyncio
import os
import logging
from datetime import datetime
from typing import Dict, List
import json
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def run_final_pilot_processing():
    """Execute final pilot processing with the 10 identified cases"""
    logger.info("🚀 Starting Final Pilot Processing with Fixed GraphRAG")
    
    try:
        # Import the fixed pipeline
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import EnhancedGraphRAGPipeline
        from courtlistener.processing.src.processing.cost_monitor import CostMonitor
        from supabase import create_client
        
        # Initialize cost monitor
        cost_monitor = CostMonitor()
        
        # Initialize Supabase client
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_KEY")  # Use SUPABASE_KEY not SUPABASE_ANON_KEY
        supabase = create_client(supabase_url, supabase_key)
        
        # The 10 identified cases from previous analysis
        case_ids = [
            "11113702",  # 46 chunks confirmed
            "11113451",  # Major case with substantial content
            "11113438",  # Well-documented opinion
            "11113701",  # Harris County case
            "11113700",  # Appellate case
            "4697111",   # Texas Case 4697111
            "4697113",   # Texas Case 4697113 
            "4697115",   # Texas Case 4697115
            "cluster_10646628",  # Cluster case 1
            "cluster_10646630"   # Cluster case 2
        ]
        
        # Initialize fixed GraphRAG pipeline
        pipeline = EnhancedGraphRAGPipeline(
            cost_monitor=cost_monitor,
            neo4j_uri=os.getenv("NEO4J_URI"),
            neo4j_user=os.getenv("NEO4J_USERNAME", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD"),
            gemini_api_key=os.getenv("GEMINI_API_KEY", "unused"),  # Not needed for Vertex
            voyage_api_key=os.getenv("VOYAGE_API_KEY"),
            practice_area="personal_injury"
        )
        
        logger.info("✅ Fixed GraphRAG pipeline initialized successfully")
        
        # Fetch and process all 10 cases
        documents = []
        for case_id in case_ids:
            logger.info(f"Fetching case {case_id}...")
            try:
                # Query Supabase for the case
                result = supabase.table("courtlistener_opinions").select("*").eq("id", case_id).execute()
                
                if result.data:
                    document = result.data[0]
                    documents.append(document)
                    logger.info(f"✅ Found case {case_id}: {document.get('case_name', 'Unknown')}")
                else:
                    logger.warning(f"❌ Case {case_id} not found in Supabase")
                    
            except Exception as e:
                logger.error(f"❌ Error fetching case {case_id}: {e}")
        
        logger.info(f"📋 Processing {len(documents)} documents through fixed GraphRAG pipeline...")
        
        # Process all documents through the pipeline
        start_time = datetime.utcnow()
        results = await pipeline.process_documents(documents, batch_size=3)
        end_time = datetime.utcnow()
        
        # Create comprehensive results report
        report = {
            "execution_id": f"fixed_graphrag_pilot_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            "timestamp": datetime.utcnow().isoformat(),
            "total_cases": len(case_ids),
            "successful_cases": results["processed"],
            "failed_cases": results["failed"],
            "entities_extracted": results["entities_extracted"],
            "relationships_extracted": results["relationships_extracted"],
            "total_time": (end_time - start_time).total_seconds(),
            "average_entities_per_case": results["entities_extracted"] / max(results["processed"], 1),
            "costs": results["costs"],
            "errors": results.get("errors", []),
            "success_rate": results["processed"] / len(documents) if documents else 0,
            "entity_extraction_working": results["entities_extracted"] > 0
        }
        
        # Save detailed report
        report_filename = f"fixed_graphrag_pilot_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print comprehensive results
        logger.info("=" * 60)
        logger.info("🎯 FINAL PILOT PROCESSING RESULTS")
        logger.info("=" * 60)
        logger.info(f"Execution ID: {report['execution_id']}")
        logger.info(f"Total Cases Attempted: {report['total_cases']}")
        logger.info(f"Successfully Processed: {report['successful_cases']}")
        logger.info(f"Failed Cases: {report['failed_cases']}")
        logger.info(f"Success Rate: {report['success_rate']:.1%}")
        logger.info("")
        logger.info("🧠 ENTITY EXTRACTION RESULTS:")
        logger.info(f"   Total Entities Extracted: {report['entities_extracted']}")
        logger.info(f"   Total Relationships Extracted: {report['relationships_extracted']}")
        logger.info(f"   Average Entities per Case: {report['average_entities_per_case']:.1f}")
        logger.info(f"   Entity Extraction Working: {'✅ YES' if report['entity_extraction_working'] else '❌ NO'}")
        logger.info("")
        logger.info("⏱️  PERFORMANCE METRICS:")
        logger.info(f"   Total Processing Time: {report['total_time']:.1f} seconds")
        logger.info(f"   Average Time per Case: {report['total_time'] / max(report['successful_cases'], 1):.1f} seconds")
        logger.info("")
        logger.info("💰 COST ANALYSIS:")
        logger.info(f"   Total Cost: ${report['costs']['total']:.4f}")
        logger.info(f"   Gemini Cost: ${report['costs'].get('gemini', 0):.4f}")
        logger.info(f"   Voyage Cost: ${report['costs'].get('voyage', 0):.4f}")
        logger.info("")
        logger.info(f"📊 Full report saved to: {report_filename}")
        
        if report["entity_extraction_working"]:
            logger.info("")
            logger.info("🎉 SUCCESS: GraphRAG Entity Extraction is Now WORKING!")
            logger.info("   The LLM JSON formatting issue has been resolved.")
            logger.info("   The pipeline can now extract legal entities and relationships.")
            logger.info("   Ready to scale to larger document sets.")
        else:
            logger.warning("")
            logger.warning("⚠️  WARNING: Entity extraction still not working properly")
            logger.warning("   Further debugging may be needed")
        
        # Clean up
        pipeline.close()
        
        return report
        
    except Exception as e:
        logger.error(f"❌ Final pilot processing failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(run_final_pilot_processing())
    
    if result and result.get("entity_extraction_working"):
        print("\n🏆 FINAL PILOT PROCESSING SUCCESSFUL!")
        print("   GraphRAG pipeline is working and ready for production scaling")
    else:
        print("\n❌ Final pilot processing encountered issues")
        print("   Review the logs and report for details")