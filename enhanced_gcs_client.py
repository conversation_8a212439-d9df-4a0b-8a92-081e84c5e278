#!/usr/bin/env python3
"""
Enhanced GCS Client for Legal Document Processing
Optimized for voyage-context-3 and production workloads
"""

import os
import asyncio
import logging
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import json
import gzip

from google.cloud import storage
from google.auth.exceptions import DefaultCredentialsError
import aiofiles
import aiohttp
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

@dataclass
class LegalDocument:
    """Enhanced legal document with metadata for voyage-context-3"""
    id: str
    case_name: str
    gcs_path: str
    content: str
    metadata: Dict[str, Any]
    word_count: int
    file_format: str
    processing_timestamp: datetime
    
    @property
    def is_processable(self) -> bool:
        """Check if document has sufficient content for processing"""
        # Lower threshold for CourtListener metadata documents
        return len(self.content.strip()) >= 50 and self.word_count >= 20

@dataclass
class GCSProcessingReport:
    """Report for batch GCS processing"""
    total_files: int
    successful_reads: int
    failed_reads: int
    total_content_length: int
    processing_errors: List[Dict[str, str]]
    execution_time: float

class EnhancedGCSClient:
    """
    Production-ready GCS client for legal document processing
    Optimized for large-scale document ingestion with error handling
    """
    
    def __init__(self, bucket_name: Optional[str] = None):
        self.bucket_name = bucket_name or os.getenv("GCS_BUCKET_NAME")
        self.credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        
        # Initialize GCS client
        try:
            self.storage_client = storage.Client()
            self.bucket = self.storage_client.bucket(self.bucket_name)
            logger.info(f"✅ GCS client initialized for bucket: {self.bucket_name}")
        except DefaultCredentialsError as e:
            logger.error(f"❌ GCS authentication failed: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ GCS client initialization failed: {e}")
            raise
    
    async def test_connection(self) -> bool:
        """Test GCS connection and permissions"""
        try:
            # Skip bucket.exists() check - just test list permissions
            # bucket.exists() requires storage.buckets.get which we don't need
            blobs = list(self.bucket.list_blobs(max_results=1))
            logger.info(f"✅ GCS connection successful. Found {len(blobs)} blob(s)")
            return True
            
        except Exception as e:
            logger.error(f"❌ GCS connection test failed: {e}")
            return False
    
    async def list_legal_documents(self, 
                                 prefix: str = "TX/",
                                 max_results: Optional[int] = None,
                                 file_extensions: List[str] = [".json", ".json.gz", ".txt", ".pdf", ".html"]) -> List[str]:
        """
        List legal documents in GCS with filtering
        """
        try:
            logger.info(f"🔍 Listing documents with prefix: {prefix}")
            
            blobs = self.bucket.list_blobs(prefix=prefix, max_results=max_results)
            document_paths = []
            
            for blob in blobs:
                # Filter by file extensions
                if any(blob.name.endswith(ext) for ext in file_extensions):
                    document_paths.append(blob.name)
            
            logger.info(f"✅ Found {len(document_paths)} legal documents")
            return document_paths
            
        except Exception as e:
            logger.error(f"❌ Failed to list documents: {e}")
            return []
    
    async def read_document(self, gcs_path: str) -> Optional[LegalDocument]:
        """
        Read a single legal document from GCS with JSON.gz support and metadata extraction
        """
        try:
            blob = self.bucket.blob(gcs_path)
            
            # Check if blob exists
            if not blob.exists():
                logger.warning(f"⚠️  Document not found: {gcs_path}")
                return None
            
            # Download content as bytes for compression handling
            content_bytes = blob.download_as_bytes()
            
            # Handle gzip compression
            if gcs_path.endswith('.gz'):
                try:
                    content_bytes = gzip.decompress(content_bytes)
                    logger.debug(f"📦 Decompressed {gcs_path}")
                except Exception as e:
                    logger.warning(f"⚠️  Failed to decompress {gcs_path}: {e}")
                    return None
            
            # Parse content based on file type
            content = ""
            case_name = "Unknown Case"
            metadata = {}
            
            if gcs_path.endswith('.json') or gcs_path.endswith('.json.gz'):
                try:
                    # Parse JSON content (CourtListener cluster format)
                    data = json.loads(content_bytes.decode('utf-8'))
                    
                    # Debug: Check data type
                    logger.debug(f"📊 Data type: {type(data)}")
                    
                    if not isinstance(data, dict):
                        logger.warning(f"⚠️  Expected dict but got {type(data)}")
                        return None
                    
                    # Extract case name (it's at the top level)
                    case_name = data.get('case_name', 'Unknown Case')
                    
                    # Extract metadata from top-level fields
                    metadata.update({
                        'cluster_id': data.get('id'),
                        'docket_id': data.get('docket_id'),
                        'court': data.get('court', 'Unknown Court'),
                        'date_filed': data.get('date_filed'),
                        'case_name_short': data.get('case_name_short'),
                        'case_name_full': data.get('case_name_full'),
                        'jurisdiction': 'TX' if gcs_path.startswith('TX/') else 'FED',
                        'precedential_status': data.get('precedential_status'),
                        'source': data.get('source'),
                        'citation_count': data.get('citation_count')
                    })
                    
                    # Extract case content from sub_opinions 
                    if 'sub_opinions' in data and data['sub_opinions']:
                        # sub_opinions contains the actual opinion text
                        opinion = data['sub_opinions'][0]  # Use primary opinion
                        
                        # Check if opinion is a dict before calling .get()
                        if isinstance(opinion, dict):
                            content = opinion.get('plain_text', '') or opinion.get('html', '')
                            
                            # Clean HTML if necessary
                            if opinion.get('html') and not opinion.get('plain_text'):
                                import re
                                content = re.sub(r'<[^>]+>', '', content)  # Basic HTML stripping
                        else:
                            # Opinion is a URL string, not actual content - skip for now
                            logger.warning(f"⚠️  Opinion is URL reference, not content: {opinion}")
                            content = ""  # Reset to empty so we try other fields
                    
                    # If no content in sub_opinions (they're URLs), try other text fields
                    if not content:
                        text_fields = ['summary', 'syllabus', 'headnotes', 'disposition', 'procedural_history', 'posture', 'attorneys']
                        content_parts = []
                        
                        for field in text_fields:
                            field_content = data.get(field)
                            if field_content and isinstance(field_content, str) and len(field_content.strip()) > 10:
                                content_parts.append(f"{field.upper()}:\n{field_content}")
                        
                        if content_parts:
                            content = "\n\n".join(content_parts)
                    
                    # If still no content, create a meaningful summary from metadata
                    if not content:
                        # Create content from case metadata for processing
                        metadata_content = []
                        
                        # Case identification
                        if data.get('case_name'):
                            metadata_content.append(f"CASE: {data['case_name']}")
                        
                        # Court and date information
                        if data.get('court'):
                            metadata_content.append(f"COURT: {data['court']}")
                        
                        if data.get('date_filed'):
                            metadata_content.append(f"DATE FILED: {data['date_filed']}")
                        
                        # Procedural information
                        if data.get('docket'):
                            docket_info = data['docket']
                            if isinstance(docket_info, str):
                                metadata_content.append(f"DOCKET: {docket_info}")
                        
                        if data.get('judges'):
                            judges = data['judges']
                            if isinstance(judges, str) and judges.strip():
                                metadata_content.append(f"JUDGES: {judges}")
                        
                        # Nature of suit
                        if data.get('nature_of_suit'):
                            metadata_content.append(f"NATURE OF SUIT: {data['nature_of_suit']}")
                        
                        # Create content from metadata
                        if metadata_content:
                            content = "\n\n".join(metadata_content)
                            content += f"\n\n[This is a CourtListener cluster record. Full opinion text available at: {data.get('sub_opinions', [''])[0] if data.get('sub_opinions') else 'N/A'}]"
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️  JSON parse error for {gcs_path}: {e}")
                    return None
            else:
                # Handle plain text files
                content = content_bytes.decode('utf-8')
            
            # Ensure we have meaningful content
            if not content or len(content.strip()) < 50:
                logger.warning(f"⚠️  Insufficient content in {gcs_path}")
                return None
            
            # Extract additional metadata from path and content
            path_metadata = await self._extract_document_metadata(gcs_path, content, blob)
            if isinstance(path_metadata, dict):
                metadata.update(path_metadata)
            else:
                logger.warning(f"⚠️  path_metadata is not a dict: {type(path_metadata)}")
            
            # Create legal document object
            document = LegalDocument(
                id=self._generate_document_id(gcs_path),
                case_name=case_name,
                gcs_path=gcs_path,
                content=content,
                metadata=metadata,
                word_count=len(content.split()),
                file_format=self._detect_file_format(gcs_path, content),
                processing_timestamp=datetime.utcnow()
            )
            
            logger.info(f"✅ Read document: {document.case_name} ({document.word_count} words)")
            return document
            
        except UnicodeDecodeError:
            logger.warning(f"⚠️  Unicode decode error for: {gcs_path}")
            return None
        except Exception as e:
            logger.error(f"❌ Failed to read document {gcs_path}: {e}")
            return None
    
    async def read_document_batch(self, 
                                gcs_paths: List[str],
                                max_concurrent: int = 10) -> GCSProcessingReport:
        """
        Read multiple documents concurrently with error handling
        """
        start_time = datetime.utcnow()
        documents = []
        errors = []
        
        # Process in batches to avoid overwhelming GCS
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def read_with_semaphore(path: str) -> Optional[LegalDocument]:
            async with semaphore:
                try:
                    return await self.read_document(path)
                except Exception as e:
                    errors.append({"path": path, "error": str(e)})
                    return None
        
        # Execute concurrent reads
        logger.info(f"📖 Reading {len(gcs_paths)} documents with {max_concurrent} concurrent workers")
        tasks = [read_with_semaphore(path) for path in gcs_paths]
        results = await asyncio.gather(*tasks)
        
        # Filter successful results
        documents = [doc for doc in results if doc is not None]
        
        # Calculate metrics
        total_content_length = sum(len(doc.content) for doc in documents)
        execution_time = (datetime.utcnow() - start_time).total_seconds()
        
        report = GCSProcessingReport(
            total_files=len(gcs_paths),
            successful_reads=len(documents),
            failed_reads=len(errors),
            total_content_length=total_content_length,
            processing_errors=errors,
            execution_time=execution_time
        )
        
        logger.info(f"📊 Batch processing complete: {report.successful_reads}/{report.total_files} successful")
        return report, documents
    
    async def find_texas_cases(self, 
                             max_results: int = 1000,
                             year_filter: Optional[int] = None) -> List[str]:
        """
        Find Texas legal cases from TX/ folder with JSON.gz files
        """
        try:
            # Build prefix for Texas cases from actual data structure
            base_prefix = "TX/"
            if year_filter:
                # Note: Year filtering may need adjustment based on actual folder structure
                logger.info(f"Year filtering requested but may need folder structure analysis")
            
            all_paths = await self.list_legal_documents(
                prefix=base_prefix,
                max_results=max_results
            )
            
            # TX/ folder contains Texas cases by definition
            texas_paths = all_paths
            
            logger.info(f"🏛️  Found {len(texas_paths)} Texas legal documents from TX/ folder")
            return texas_paths
            
        except Exception as e:
            logger.error(f"❌ Failed to find Texas cases: {e}")
            return []
    
    async def _extract_document_metadata(self, 
                                       gcs_path: str, 
                                       content: str, 
                                       blob: storage.Blob) -> Dict[str, Any]:
        """Extract metadata from document path and content for TX/FED structure"""
        
        metadata = {
            "gcs_path": gcs_path,
            "file_size": blob.size,
            "created_time": blob.time_created,
            "updated_time": blob.updated,
            "content_type": blob.content_type,
        }
        
        # Extract case information from path
        # Format: TX/clusters/12345.json.gz or FED/clusters/12345.json.gz
        path_parts = gcs_path.split('/')
        
        if len(path_parts) >= 3:
            jurisdiction = path_parts[0]  # TX or FED
            data_type = path_parts[1]     # clusters, opinions, etc.
            filename = path_parts[-1]     # 12345.json.gz
            
            metadata["jurisdiction"] = jurisdiction
            metadata["data_type"] = data_type
            metadata["data_source"] = "courtlistener"
            
            # Extract cluster/document ID from filename
            base_filename = filename.split('.')[0]  # Remove .json.gz
            if base_filename.isdigit():
                metadata["cluster_id"] = base_filename
                metadata["document_id"] = f"{jurisdiction}_{base_filename}"
        
        # Extract case name from content (look for typical case patterns)
        if content:
            lines = content.split('\n')[:15]  # Check more lines for JSON content
            for line in lines:
                line = line.strip()
                # Look for case name patterns
                if len(line) > 10 and (' v. ' in line or ' v ' in line):
                    # Avoid all-caps headers
                    if not line.isupper() and not line.startswith('IN '):
                        metadata["case_name"] = line
                        break
                # Also check for "In re" cases
                elif line.startswith('In re ') and len(line) > 10:
                    metadata["case_name"] = line
                    break
        
        # Detect court information from content
        if content:
            content_upper = content.upper()
            court_indicators = [
                "COURT OF APPEALS", 
                "SUPREME COURT", 
                "DISTRICT COURT", 
                "COUNTY COURT",
                "BANKRUPTCY COURT",
                "FEDERAL COURT"
            ]
            for indicator in court_indicators:
                if indicator in content_upper:
                    metadata["court_type"] = indicator
                    break
            
            # Detect Texas-specific indicators
            texas_indicators = ["TEXAS", "TEX.", "STATE OF TEXAS"]
            for indicator in texas_indicators:
                if indicator in content_upper:
                    metadata["state"] = "Texas"
                    break
        
        return metadata
    
    def _generate_document_id(self, gcs_path: str) -> str:
        """Generate unique document ID from GCS path for TX/FED structure"""
        # Extract cluster ID from path: TX/clusters/12345.json.gz
        path_parts = gcs_path.split('/')
        
        if len(path_parts) >= 3:
            jurisdiction = path_parts[0]  # TX or FED
            filename = path_parts[-1]    # 12345.json.gz
            base_filename = filename.split('.')[0]  # 12345
            
            if base_filename.isdigit():
                return f"{jurisdiction.lower()}_cluster_{base_filename}"
        
        # Fallback to path hash for non-standard formats
        import hashlib
        path_hash = hashlib.md5(gcs_path.encode()).hexdigest()[:12]
        return f"gcs_doc_{path_hash}"
    
    def _detect_file_format(self, gcs_path: str, content: str) -> str:
        """Detect document format from path and content for TX/FED structure"""
        
        # Handle compressed JSON format (our primary format)
        if gcs_path.endswith('.json.gz'):
            return "json_compressed"
        elif gcs_path.endswith('.json'):
            return "json"
        elif gcs_path.endswith('.pdf'):
            return "pdf"
        elif gcs_path.endswith('.html') or gcs_path.endswith('.htm'):
            return "html"
        elif gcs_path.endswith('.xml'):
            return "xml"
        elif gcs_path.endswith('.txt'):
            return "text"
        
        # Content-based detection for edge cases
        if content.strip().startswith('{') and content.strip().endswith('}'):
            return "json"
        elif content.strip().startswith('<?xml'):
            return "xml"
        elif content.strip().startswith('<!DOCTYPE html') or '<html' in content[:200]:
            return "html"
        else:
            return "text"

# Test function
async def test_enhanced_gcs_client():
    """Test the enhanced GCS client"""
    
    print("=== Testing Enhanced GCS Client ===\n")
    
    try:
        # Initialize client
        client = EnhancedGCSClient()
        
        # Test connection
        print("1. Testing GCS connection...")
        connection_ok = await client.test_connection()
        if not connection_ok:
            print("❌ GCS connection failed")
            return
        
        # List some documents
        print("\n2. Listing sample documents...")
        sample_paths = await client.list_legal_documents(max_results=5)
        
        if not sample_paths:
            print("⚠️  No documents found")
            return
        
        print(f"Found {len(sample_paths)} sample documents:")
        for path in sample_paths:
            print(f"  - {path}")
        
        # Read a single document
        print(f"\n3. Reading document: {sample_paths[0]}...")
        document = await client.read_document(sample_paths[0])
        
        if document:
            print(f"✅ Successfully read document:")
            print(f"   ID: {document.id}")
            print(f"   Case Name: {document.case_name}")
            print(f"   Word Count: {document.word_count}")
            print(f"   Format: {document.file_format}")
            print(f"   Processable: {document.is_processable}")
            print(f"   Content preview: {document.content[:200]}...")
        else:
            print("❌ Failed to read document")
        
        # Test batch reading
        print(f"\n4. Testing batch read of {min(3, len(sample_paths))} documents...")
        test_paths = sample_paths[:3]
        report, documents = await client.read_document_batch(test_paths)
        
        print(f"📊 Batch Report:")
        print(f"   Total files: {report.total_files}")
        print(f"   Successful: {report.successful_reads}")
        print(f"   Failed: {report.failed_reads}")
        print(f"   Total content: {report.total_content_length:,} chars")
        print(f"   Execution time: {report.execution_time:.2f}s")
        
        if report.processing_errors:
            print(f"   Errors: {len(report.processing_errors)}")
            for error in report.processing_errors:
                print(f"     - {error['path']}: {error['error']}")
        
        print("\n✅ Enhanced GCS Client test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced GCS Client test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_enhanced_gcs_client())