#!/usr/bin/env python3
"""
Debug LLM Response
Direct test of LLM entity extraction to see what <PERSON> is actually returning
"""

import asyncio
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_llm_response():
    """Debug what the LLM is actually returning for entity extraction"""
    logger.info("🔍 DEBUGGING LLM ENTITY EXTRACTION RESPONSE")
    logger.info("=" * 60)
    
    try:
        import vertexai
        from vertexai.generative_models import GenerationConfig
        from neo4j_graphrag.llm import VertexAILLM
        from neo4j_graphrag.experimental.components.entity_relation_extractor import (
            LLMEntityRelationExtractor,
            OnError
        )
        from neo4j_graphrag.experimental.components.schema import GraphSchema, NodeType, RelationshipType
        
        # Initialize Vertex AI
        vertex_project_id = os.getenv("VERTEX_PROJECT_ID")
        vertex_location = os.getenv("VERTEX_LOCATION", "us-central1")
        
        if vertex_project_id:
            vertexai.init(project=vertex_project_id, location=vertex_location)
            logger.info(f"Initialized Vertex AI with project: {vertex_project_id}")
        
        # Test different LLM configurations
        configs_to_test = [
            {
                "name": "Current Configuration",
                "config": {
                    "generation_config": GenerationConfig(
                        temperature=0.0,
                        response_mime_type="application/json"
                    ),
                    "model_params": {
                        "max_tokens": 2000,
                        "temperature": 0,
                        "response_format": {"type": "json_object"}
                    }
                }
            },
            {
                "name": "Minimal Configuration",
                "config": {
                    "model_params": {
                        "temperature": 0,
                        "response_format": {"type": "json_object"}
                    }
                }
            },
            {
                "name": "Without JSON Response Format",
                "config": {
                    "model_params": {
                        "temperature": 0
                    }
                }
            }
        ]
        
        # Simple test text
        test_text = """
        In Anderson v. Memorial Hermann Hospital, plaintiff Sarah Anderson sued Memorial Hermann 
        Hospital for medical malpractice. Judge Robert Wilson presided over the case.
        Attorney Jennifer Martinez represented plaintiff Anderson, while 
        Attorney Michael Davis represented Memorial Hermann Hospital.
        The jury awarded plaintiff $150,000 in damages.
        """
        
        # Simple schema
        node_types = [
            NodeType(label="Person", description="People involved in legal cases"),
            NodeType(label="Organization", description="Organizations and institutions"),
            NodeType(label="Case", description="Legal cases and proceedings"),
        ]
        
        relationship_types = [
            RelationshipType(label="REPRESENTED", description="Attorney represented party"),
            RelationshipType(label="SUED", description="Party sued another party"),
            RelationshipType(label="PRESIDED_OVER", description="Judge presided over case"),
        ]
        
        schema = GraphSchema(
            node_types=tuple(node_types),
            relationship_types=tuple(relationship_types),
            patterns=()
        )
        
        for config_test in configs_to_test:
            logger.info(f"🧪 Testing {config_test['name']}:")
            
            try:
                # Create LLM with this configuration
                llm_config = config_test['config']
                llm = VertexAILLM(
                    model_name="gemini-2.0-flash-exp",
                    **llm_config
                )
                
                # Create entity extractor
                extractor = LLMEntityRelationExtractor(
                    llm=llm,
                    create_lexical_graph=False,
                    on_error=OnError.RAISE
                )
                
                logger.info("   ✅ LLM and extractor created successfully")
                
                # Try direct extraction
                from neo4j_graphrag.experimental.components.types import TextChunk, TextChunks
                
                chunk = TextChunk(
                    text=test_text,
                    index=0,
                    chunk_id="debug_chunk_1"
                )
                
                chunks = TextChunks(chunks=[chunk])
                
                logger.info(f"   📝 Extracting from text: {test_text[:100]}...")
                
                # This is the key test - what does the LLM actually return?
                result = await extractor.run(chunks, schema=schema)
                
                logger.info(f"   📊 Extraction result type: {type(result)}")
                logger.info(f"   📊 Extraction result: {result}")
                
                if hasattr(result, 'data'):
                    logger.info(f"   📊 Result data: {result.data}")
                
                if hasattr(result, 'nodes'):
                    logger.info(f"   📊 Nodes found: {len(result.nodes) if result.nodes else 0}")
                    if result.nodes:
                        for i, node in enumerate(result.nodes[:3]):
                            logger.info(f"      Node {i+1}: {node}")
                
                if hasattr(result, 'relationships'):
                    logger.info(f"   📊 Relationships found: {len(result.relationships) if result.relationships else 0}")
                    if result.relationships:
                        for i, rel in enumerate(result.relationships[:3]):
                            logger.info(f"      Relationship {i+1}: {rel}")
                
                logger.info("")
                
            except Exception as e:
                logger.error(f"   ❌ {config_test['name']} failed: {e}")
                logger.error(f"   Error type: {type(e)}")
                import traceback
                logger.error(f"   Traceback: {traceback.format_exc()}")
                logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM debugging failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(debug_llm_response())
    
    if result:
        print(f"\n🔍 LLM DEBUG COMPLETE")
        print("   Check the detailed logs above to see what the LLM is actually returning")
    else:
        print("\n❌ LLM debugging failed")
        print("   Check the error logs above")