#!/usr/bin/env python3
"""
Test PostgreSQL-Compatible CSV Reading
Uses exact CSV parameters that PostgreSQL writes to read CourtListener opinions data.
"""

import bz2
import csv
import itertools
import sys

def test_postgresql_csv_reading():
    """Test reading with PostgreSQL-compatible CSV parameters."""
    print("🔧 Testing PostgreSQL-Compatible CSV Reading")
    print("=" * 60)
    
    # Set maximum field size to 1GB (PostgreSQL can handle very large fields)
    csv.field_size_limit(10**9)
    print(f"✅ CSV field size limit set to: {csv.field_size_limit():,} bytes (1GB)")
    
    try:
        with bz2.open('bulk_csv/opinions-2025-07-02.csv.bz2',
                     'rt', encoding='utf-8', newline='') as f:
            
            # Use PostgreSQL-compatible CSV reader parameters
            reader = csv.reader(
                f,
                delimiter=',',
                quotechar='"',
                escapechar='\\',
                doublequote=False,
                strict=True
            )
            
            print("📋 Reading header...")
            header = next(reader)
            print(f'✅ Columns: {len(header)}')
            print(f'📝 Header fields: {header[:10]}...')
            
            if len(header) >= 21:
                print(f'📍 Key fields found:')
                if 'id' in header:
                    print(f'   - id: position {header.index("id")}')
                if 'cluster_id' in header:
                    print(f'   - cluster_id: position {header.index("cluster_id")}')
                if 'plain_text' in header:
                    print(f'   - plain_text: position {header.index("plain_text")}')
                if 'type' in header:
                    print(f'   - type: position {header.index("type")}')
            
            print(f"\n📊 Reading first data row...")
            row = next(reader)
            print(f'✅ Sample row fields: {len(row)}')
            
            # Check cluster_id field
            if 'cluster_id' in header:
                cluster_id_idx = header.index('cluster_id')
                cluster_id = row[cluster_id_idx] if cluster_id_idx < len(row) else 'N/A'
                print(f'🔍 cluster_id field: "{cluster_id[:20]}..."')
                print(f'📊 cluster_id is numeric: {cluster_id.isdigit()}')
            else:
                print('❌ cluster_id field not found in header')
            
            # Check plain_text field
            if 'plain_text' in header:
                plain_text_idx = header.index('plain_text')
                plain_text = row[plain_text_idx] if plain_text_idx < len(row) else 'N/A'
                print(f'📄 plain_text field (first 100 chars):')
                print(f'   "{plain_text[:100]}..."')
                
                # Check for escaped newlines
                if '\\n' in plain_text[:200]:
                    print(f'✅ Escaped newlines found in plain_text')
                else:
                    print(f'⚠️  No escaped newlines found in first 200 chars')
            else:
                print('❌ plain_text field not found in header')
            
            # Check id field
            if 'id' in header:
                id_idx = header.index('id')
                opinion_id = row[id_idx] if id_idx < len(row) else 'N/A'
                print(f'🆔 id field: "{opinion_id}"')
                print(f'📊 id is numeric: {opinion_id.isdigit()}')
            else:
                print('❌ id field not found in header')
            
            print(f"\n✅ SUCCESS: PostgreSQL CSV parameters work!")
            print(f"📋 Summary:")
            print(f"   - Column count: {len(header)}")
            print(f"   - Row field count: {len(row)}")
            print(f"   - Fields match: {len(header) == len(row)}")
            
            return True
            
    except Exception as e:
        print(f"❌ ERROR: Failed to read CSV with PostgreSQL parameters")
        print(f"   Exception type: {type(e).__name__}")
        print(f"   Exception message: {str(e)}")
        
        # Try to get line number information
        if hasattr(e, 'line_num'):
            print(f"   Line number: {e.line_num}")
        
        print(f"\n🔍 Exception details for debugging:")
        import traceback
        traceback.print_exc()
        
        return False

def main():
    """Main test function."""
    success = test_postgresql_csv_reading()
    
    if success:
        print(f"\n🎯 NEXT STEP:")
        print(f"   Update s3_bulk_loader.py DictReader with PostgreSQL parameters")
        print(f"   Then run 100K-row dry-run test")
        return 0
    else:
        print(f"\n🔧 DEBUGGING NEEDED:")
        print(f"   Inspect raw bytes at failure point with xxd")
        print(f"   Check for binary data or encoding issues")
        return 1

if __name__ == "__main__":
    exit(main())
