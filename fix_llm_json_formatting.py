#!/usr/bin/env python3
"""
Fix VertexAI LLM JSON Response Formatting for GraphRAG Pipeline
Diagnoses and resolves the JSON formatting issue preventing entity extraction
"""

import os
import asyncio
import json
import logging
from datetime import datetime
from dotenv import load_dotenv
import vertexai
from vertexai.generative_models import GenerativeModel, GenerationConfig

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

load_dotenv()

# Define the expected GraphRAG response schema
GRAPHRAG_RESPONSE_SCHEMA = {
    "type": "object",
    "properties": {
        "nodes": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "label": {"type": "string"}, 
                    "properties": {"type": "object"}
                },
                "required": ["id", "label", "properties"]
            }
        },
        "relationships": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "type": {"type": "string"},
                    "start_node_id": {"type": "string"},
                    "end_node_id": {"type": "string"},
                    "properties": {"type": "object"}
                },
                "required": ["type", "start_node_id", "end_node_id"]
            }
        }
    },
    "required": ["nodes", "relationships"]
}

async def test_llm_json_formatting():
    """Test and fix LLM JSON response formatting issues"""
    logger.info("🔧 Testing VertexAI LLM JSON Response Formatting")
    
    # Sample legal text for testing
    test_text = """
    Case: Smith v. Jones Auto Repair
    Court: Harris County District Court
    Date: 2023-03-15
    
    This is a personal injury case where Plaintiff John Smith sued Defendant Mary Jones 
    for damages resulting from a motor vehicle accident. Judge William Brown presided over 
    the case. The jury awarded $50,000 in actual damages and $25,000 in punitive damages. 
    Attorney Sarah Wilson represented the plaintiff, while Attorney Robert Davis represented the defendant.
    The case was decided in the 55th Judicial District Court of Harris County, Texas.
    """
    
    try:
        # Initialize Vertex AI
        vertex_project_id = os.getenv("VERTEX_PROJECT_ID")
        vertex_location = os.getenv("VERTEX_LOCATION", "us-central1")
        
        logger.info(f"Initializing Vertex AI with project: {vertex_project_id}")
        vertexai.init(project=vertex_project_id, location=vertex_location)
        
        # Test 1: Basic Gemini model without structured output
        logger.info("Test 1: Basic Gemini model")
        basic_model = GenerativeModel("gemini-2.0-flash-exp")
        
        basic_prompt = """
        Extract entities and relationships from this legal text and return ONLY a valid JSON object.
        
        Required format:
        {
          "nodes": [
            {"id": "unique_id", "label": "EntityType", "properties": {"name": "Entity Name", "type": "additional_info"}}
          ],
          "relationships": [
            {"type": "RELATIONSHIP_TYPE", "start_node_id": "id1", "end_node_id": "id2", "properties": {}}
          ]
        }
        
        Entity types: Case, Judge, Court, Attorney, Plaintiff, Defendant, Damages
        Relationship types: PRESIDED_OVER, REPRESENTED, FILED_IN, AWARDED, OPPOSED
        
        Text to analyze:
        """ + test_text
        
        basic_response = await basic_model.generate_content_async(basic_prompt)
        logger.info(f"Basic response: {basic_response.text}")
        
        # Try to parse as JSON
        try:
            basic_json = json.loads(basic_response.text)
            logger.info("✅ Basic model returned valid JSON")
            logger.info(f"   Nodes: {len(basic_json.get('nodes', []))}")
            logger.info(f"   Relationships: {len(basic_json.get('relationships', []))}")
        except json.JSONDecodeError as e:
            logger.error(f"❌ Basic model returned invalid JSON: {e}")
            logger.error(f"   Raw response: {basic_response.text[:200]}...")
        
        # Test 2: Gemini with structured output generation config
        logger.info("\nTest 2: Gemini with structured output")
        
        generation_config = GenerationConfig(
            temperature=0.0,
            response_mime_type="application/json",
            response_schema=GRAPHRAG_RESPONSE_SCHEMA
        )
        
        structured_model = GenerativeModel(
            "gemini-2.0-flash-exp",
            generation_config=generation_config
        )
        
        structured_prompt = """
        You are a legal knowledge graph extraction expert. Extract entities and relationships from the legal text.
        
        Extract these entity types:
        - Case: Legal cases and proceedings
        - Judge: Presiding judges
        - Court: Courts and jurisdictions  
        - Attorney: Legal representatives
        - Plaintiff: Plaintiffs and petitioners
        - Defendant: Defendants and respondents
        - Damages: Monetary awards and damages
        
        Extract these relationship types:
        - PRESIDED_OVER: Judge presided over case
        - REPRESENTED: Attorney represented party
        - FILED_IN: Case filed in court
        - AWARDED: Damages awarded
        - OPPOSED: Parties in opposition
        
        Text to analyze:
        """ + test_text
        
        structured_response = await structured_model.generate_content_async(structured_prompt)
        logger.info(f"Structured response: {structured_response.text}")
        
        # Try to parse structured response
        try:
            structured_json = json.loads(structured_response.text)
            logger.info("✅ Structured model returned valid JSON")
            logger.info(f"   Nodes: {len(structured_json.get('nodes', []))}")
            logger.info(f"   Relationships: {len(structured_json.get('relationships', []))}")
            
            # Show extracted entities
            for node in structured_json.get('nodes', []):
                logger.info(f"   Node: {node.get('label')} - {node.get('properties', {}).get('name', 'N/A')}")
                
            for rel in structured_json.get('relationships', []):
                logger.info(f"   Relationship: {rel.get('start_node_id')} --{rel.get('type')}--> {rel.get('end_node_id')}")
                
            return structured_json
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ Structured model returned invalid JSON: {e}")
            logger.error(f"   Raw response: {structured_response.text[:200]}...")
        
        # Test 3: Alternative model and prompt strategies
        logger.info("\nTest 3: Alternative prompt strategy")
        
        alternative_prompt = """
        TASK: Extract legal entities and relationships as JSON
        
        INPUT TEXT:
        """ + test_text + """
        
        OUTPUT: Return only valid JSON with this exact structure:
        {
          "nodes": [{"id": "person_1", "label": "Person", "properties": {"name": "John Smith", "role": "plaintiff"}}],
          "relationships": [{"type": "REPRESENTED", "start_node_id": "attorney_1", "end_node_id": "person_1", "properties": {}}]
        }
        
        Extract all people, organizations, and legal concepts. Create unique IDs for each entity.
        """
        
        alternative_response = await basic_model.generate_content_async(alternative_prompt)
        logger.info(f"Alternative response: {alternative_response.text}")
        
        try:
            alternative_json = json.loads(alternative_response.text)
            logger.info("✅ Alternative prompt returned valid JSON")
            return alternative_json
        except json.JSONDecodeError as e:
            logger.error(f"❌ Alternative prompt returned invalid JSON: {e}")
        
        # Test 4: Test with neo4j-graphrag VertexAI LLM wrapper
        logger.info("\nTest 4: Neo4j GraphRAG VertexAI LLM wrapper")
        
        from neo4j_graphrag.llm import VertexAILLM
        
        # Test different configurations
        configs = [
            {
                "model_name": "gemini-2.0-flash-exp",
                "model_params": {"temperature": 0.0}
            },
            {
                "model_name": "gemini-2.5-pro",
                "model_params": {"temperature": 0.0, "max_output_tokens": 4096}
            }
        ]
        
        for i, config in enumerate(configs):
            logger.info(f"Testing config {i+1}: {config}")
            try:
                neo4j_llm = VertexAILLM(**config)
                
                neo4j_prompt = """Extract entities and relationships from legal text. Return valid JSON only.

Format:
{"nodes": [{"id": "case_1", "label": "Case", "properties": {"name": "Smith v. Jones"}}], "relationships": [{"type": "PRESIDED_OVER", "start_node_id": "judge_1", "end_node_id": "case_1", "properties": {}}]}

Text: """ + test_text
                
                neo4j_response = await neo4j_llm.ainvoke(neo4j_prompt)
                logger.info(f"Neo4j LLM response: {neo4j_response}")
                
                try:
                    neo4j_json = json.loads(neo4j_response)
                    logger.info(f"✅ Neo4j LLM config {i+1} returned valid JSON")
                    logger.info(f"   Nodes: {len(neo4j_json.get('nodes', []))}")
                    logger.info(f"   Relationships: {len(neo4j_json.get('relationships', []))}")
                    
                    return {"config": config, "result": neo4j_json}
                    
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Neo4j LLM config {i+1} returned invalid JSON: {e}")
                    
            except Exception as e:
                logger.error(f"❌ Neo4j LLM config {i+1} failed: {e}")
        
        return None
        
    except Exception as e:
        logger.error(f"❌ LLM JSON formatting test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def create_fixed_llm_configuration():
    """Create a working LLM configuration for the GraphRAG pipeline"""
    logger.info("🔧 Creating Fixed LLM Configuration")
    
    # Test the working configuration
    working_config = await test_llm_json_formatting()
    
    if working_config:
        logger.info("✅ Found working configuration")
        
        # Create updated GraphRAG pipeline configuration
        fixed_config = f"""
# Fixed VertexAI LLM Configuration for GraphRAG Pipeline
# Generated: {datetime.utcnow().isoformat()}

WORKING_CONFIGURATION = {{
    "model_name": "gemini-2.0-flash-exp",
    "model_params": {{
        "temperature": 0.0,
        "max_output_tokens": 4096
    }},
    "generation_config": {{
        "temperature": 0.0,
        "response_mime_type": "application/json",
        "response_schema": {json.dumps(GRAPHRAG_RESPONSE_SCHEMA, indent=8)}
    }}
}}

WORKING_PROMPT_TEMPLATE = '''
You are a legal knowledge graph extraction expert. Extract entities and relationships from the legal text.

Extract these entity types with unique IDs:
- Case: Legal cases and proceedings  
- Judge: Presiding judges
- Court: Courts and jurisdictions
- Attorney: Legal representatives
- Plaintiff: Plaintiffs and petitioners
- Defendant: Defendants and respondents
- Damages: Monetary awards and damages

Extract these relationship types:
- PRESIDED_OVER: Judge presided over case
- REPRESENTED: Attorney represented party
- FILED_IN: Case filed in court
- AWARDED: Damages awarded
- OPPOSED: Parties in opposition

Return only valid JSON in this exact format:
{{"nodes": [{{"id": "unique_id", "label": "EntityType", "properties": {{"name": "Entity Name"}}}}], "relationships": [{{"type": "RELATIONSHIP_TYPE", "start_node_id": "id1", "end_node_id": "id2", "properties": {{}}}}]}}

Text to analyze:
{{text}}
'''
"""
        
        # Save the configuration
        with open("fixed_llm_config.py", "w") as f:
            f.write(fixed_config)
        
        logger.info("✅ Saved fixed LLM configuration to fixed_llm_config.py")
        
        return working_config
    
    else:
        logger.error("❌ Could not find working LLM configuration")
        return None

if __name__ == "__main__":
    asyncio.run(create_fixed_llm_configuration())