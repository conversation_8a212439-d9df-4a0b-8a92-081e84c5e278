{"execution_id": "streamlined_pilot_20250819_103604", "timestamp": "2025-08-19T10:36:04.069205", "total_cases": 10, "successful_cases": 8, "failed_cases": 2, "four_system_coverage_count": 0, "coverage_rate": 0.0, "total_time": 48.796771, "average_entities": 0.0, "results": [{"case_id": "11113702", "case_name": "", "status": "success", "processing_time": 5.642218, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": []}, {"case_id": "11113451", "case_name": "", "status": "success", "processing_time": 10.541421, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": []}, {"case_id": "11113438", "case_name": "", "status": "success", "processing_time": 7.75156, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": []}, {"case_id": "11113701", "case_name": "", "status": "success", "processing_time": 3.378066, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": []}, {"case_id": "11113700", "case_name": "", "status": "success", "processing_time": 3.431022, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": []}, {"case_id": "4697111", "case_name": "Texas Case 4697111", "status": "success", "processing_time": 9.952766, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": []}, {"case_id": "4697113", "case_name": "Texas Case 4697113", "status": "success", "processing_time": 3.473969, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": []}, {"case_id": "4697115", "case_name": "Texas Case 4697115", "status": "success", "processing_time": 3.217073, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": []}, {"case_id": "cluster_10646628", "case_name": "Not Found", "status": "error", "processing_time": 0.0, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": ["Case not found in Supabase"]}, {"case_id": "cluster_10646630", "case_name": "Not Found", "status": "error", "processing_time": 0.0, "entities_extracted": 0, "relationships_extracted": 0, "four_system_coverage": false, "errors": ["Case not found in Supabase"]}]}