{"test_id": "4system_scale_20250820_124807", "test_start_time": "2025-08-20T10:48:07.929623+00:00", "test_end_time": "2025-08-20T10:48:07.931008+00:00", "total_test_time_seconds": 0.0013861656188964844, "total_documents": 10, "successful_documents": 0, "failed_documents": 10, "gcs_success_rate": 100.0, "graphrag_success_rate": 0.0, "embedding_success_rate": 0.0, "storage_success_rate": 0.0, "overall_success_rate": 0.0, "total_entities_extracted": 0, "total_relationships_extracted": 0, "total_chunks_created": 0, "total_vectors_stored": 0, "total_costs": {"graphrag": 0.0, "voyage": 0.0, "storage": 0.0}, "average_processing_time_per_doc": 0.0001295, "document_results": [{"document_id": "cl_case_12345_tx", "case_name": "Test Case 1 v. Defendant 1", "processing_start_time": "2025-08-20T10:48:07.929638+00:00", "processing_end_time": "2025-08-20T10:48:07.930216+00:00", "total_processing_time_seconds": 0.000578, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12345_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.929656+00:00", "document_size_bytes": 1292, "content_hash": "fb0a098b895a7ac2b22c0542e87f5b84c8147a55df9b6e92d90cd1404c90ff1f", "retrieval_time_ms": 0.052928924560546875, "metadata_extracted": {"court": "Harris County", "case_name": "Test Case 1 v. Defendant 1", "case_type": "car_accident"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}, {"document_id": "cl_case_12346_tx", "case_name": "Test Case 2 v. Defendant 2", "processing_start_time": "2025-08-20T10:48:07.930228+00:00", "processing_end_time": "2025-08-20T10:48:07.930327+00:00", "total_processing_time_seconds": 9.9e-05, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12346_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.930238+00:00", "document_size_bytes": 1144, "content_hash": "2882280dfdc9389b6bb0083b02bda5f540e43a15809180a743db42aeb920cb7b", "retrieval_time_ms": 0.007152557373046875, "metadata_extracted": {"court": "Dallas County", "case_name": "Test Case 2 v. Defendant 2", "case_type": "medical_malpractice"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}, {"document_id": "cl_case_12347_tx", "case_name": "Test Case 3 v. Defendant 3", "processing_start_time": "2025-08-20T10:48:07.930333+00:00", "processing_end_time": "2025-08-20T10:48:07.930413+00:00", "total_processing_time_seconds": 8e-05, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12347_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.930342+00:00", "document_size_bytes": 1123, "content_hash": "c9525704ebccb231df788f5502b12ce6b80a38270f6042ac61d66f12d7525ff1", "retrieval_time_ms": 0.0050067901611328125, "metadata_extracted": {"court": "Travis County", "case_name": "Test Case 3 v. Defendant 3", "case_type": "premises_liability"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}, {"document_id": "cl_case_12348_tx", "case_name": "Test Case 4 v. Defendant 4", "processing_start_time": "2025-08-20T10:48:07.930419+00:00", "processing_end_time": "2025-08-20T10:48:07.930482+00:00", "total_processing_time_seconds": 6.3e-05, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12348_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.930426+00:00", "document_size_bytes": 1122, "content_hash": "d932fdf7d845640733e2fb30305340764398a6abe33068e376a5933e83092729", "retrieval_time_ms": 0.003814697265625, "metadata_extracted": {"court": "Collin County", "case_name": "Test Case 4 v. Defendant 4", "case_type": "product_liability"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}, {"document_id": "cl_case_12349_tx", "case_name": "Test Case 5 v. Defendant 5", "processing_start_time": "2025-08-20T10:48:07.930488+00:00", "processing_end_time": "2025-08-20T10:48:07.930615+00:00", "total_processing_time_seconds": 0.000127, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12349_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.930494+00:00", "document_size_bytes": 1292, "content_hash": "97d952afc074cacaa41437e38b89c5e51d2fab9fc660039e1342ecc6d2b134ff", "retrieval_time_ms": 0.0040531158447265625, "metadata_extracted": {"court": "Harris County", "case_name": "Test Case 5 v. Defendant 5", "case_type": "car_accident"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}, {"document_id": "cl_case_12350_tx", "case_name": "Test Case 6 v. Defendant 6", "processing_start_time": "2025-08-20T10:48:07.930620+00:00", "processing_end_time": "2025-08-20T10:48:07.930712+00:00", "total_processing_time_seconds": 9.2e-05, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12350_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.930627+00:00", "document_size_bytes": 1144, "content_hash": "f73e16fd5d708e152c901c2ac9c791e34c4c6d2accdb70dcb2be12e0e25d24f1", "retrieval_time_ms": 0.003814697265625, "metadata_extracted": {"court": "Dallas County", "case_name": "Test Case 6 v. Defendant 6", "case_type": "medical_malpractice"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}, {"document_id": "cl_case_12351_tx", "case_name": "Test Case 7 v. Defendant 7", "processing_start_time": "2025-08-20T10:48:07.930720+00:00", "processing_end_time": "2025-08-20T10:48:07.930789+00:00", "total_processing_time_seconds": 6.9e-05, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12351_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.930729+00:00", "document_size_bytes": 1123, "content_hash": "d8001fc208af9263f1a0ad5bbf6b8691d639d3337526aa8419bf8b64ff7a9b5e", "retrieval_time_ms": 0.0059604644775390625, "metadata_extracted": {"court": "Travis County", "case_name": "Test Case 7 v. Defendant 7", "case_type": "premises_liability"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}, {"document_id": "cl_case_12352_tx", "case_name": "Test Case 8 v. Defendant 8", "processing_start_time": "2025-08-20T10:48:07.930795+00:00", "processing_end_time": "2025-08-20T10:48:07.930858+00:00", "total_processing_time_seconds": 6.3e-05, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12352_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.930802+00:00", "document_size_bytes": 1122, "content_hash": "c44495bbdc750f5bc59d93cadba645eb11cf1ac9e0aa0ffa08586d4d57b6da22", "retrieval_time_ms": 0.0040531158447265625, "metadata_extracted": {"court": "Collin County", "case_name": "Test Case 8 v. Defendant 8", "case_type": "product_liability"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}, {"document_id": "cl_case_12353_tx", "case_name": "Test Case 9 v. Defendant 9", "processing_start_time": "2025-08-20T10:48:07.930863+00:00", "processing_end_time": "2025-08-20T10:48:07.930930+00:00", "total_processing_time_seconds": 6.7e-05, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12353_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.930870+00:00", "document_size_bytes": 1292, "content_hash": "41d648f9dee4ed8b3e1898ba5f548c9b84f8678c9f6e6151e4ebb9f30607dac0", "retrieval_time_ms": 0.0040531158447265625, "metadata_extracted": {"court": "Harris County", "case_name": "Test Case 9 v. Defendant 9", "case_type": "car_accident"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}, {"document_id": "cl_case_12354_tx", "case_name": "Test Case 10 v. Defendant 10", "processing_start_time": "2025-08-20T10:48:07.930935+00:00", "processing_end_time": "2025-08-20T10:48:07.930992+00:00", "total_processing_time_seconds": 5.7e-05, "document_evidence": {"gcs_path": "gs://texas-laws-personalinjury/TX/courtlistener/cl_case_12354_tx.json.gz", "retrieval_timestamp": "2025-08-20T10:48:07.930942+00:00", "document_size_bytes": 1146, "content_hash": "6a3635401a87bd8e75f6e555865b80ffbbd2841d1a6ceb1d78cd15163a7e2aea", "retrieval_time_ms": 0.0030994415283203125, "metadata_extracted": {"court": "Dallas County", "case_name": "Test Case 10 v. Defendant 10", "case_type": "medical_malpractice"}, "gcs_success": true, "gcs_error": null}, "graphrag_evidence": {"entities_extracted": [], "relationships_extracted": [], "processing_time_seconds": 0.0, "llm_tokens_used": {}, "cost_usd": 0.0, "neo4j_nodes_created": 0, "neo4j_relationships_created": 0, "classification_results": [], "graphrag_success": false, "graphrag_error": "'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"}, "embedding_evidence": {"chunks_created": 0, "chunk_details": [], "total_embedding_tokens": 0, "voyage_api_cost": 0.0, "embedding_generation_time": 0.0, "legal_indicators_detected": {}, "embedding_success": false, "embedding_error": null}, "storage_evidence": {"global_uid": "", "supabase_storage": {}, "pinecone_storage": {}, "neo4j_storage": {}, "cross_system_consistency": {}, "storage_success": false, "storage_error": null}, "overall_success": false, "systems_succeeded": ["gcs"], "systems_failed": ["graphrag", "general_error: 'EnhancedGraphRAGPipeline' object has no attribute 'process_documents'"], "entity_extraction_quality": {}, "cross_system_validation": {}}], "evidence_samples": {}, "scalability_analysis": {"current_performance": {"avg_time_per_doc_seconds": 0.0001295, "avg_cost_per_doc_usd": 0.0, "success_rate": 0.0}, "projected_100_docs": {"estimated_time_hours": 3.597222222222222e-06, "estimated_cost_usd": 0.0, "bottleneck_system": "voyage"}, "projected_1000_docs": {"estimated_time_hours": 3.5972222222222225e-05, "estimated_cost_usd": 0.0, "infrastructure_recommendations": ["Implement parallel processing", "Add batching for Voyage embeddings", "Optimize Neo4j batch insertions"]}}}