#!/usr/bin/env python3
"""
CSV Line Fixer for CourtListener Opinions
Creates a clean CSV with only properly formatted rows that have the correct number of fields.
"""

import bz2
import csv
import time
import re
from typing import Optional

def fix_csv_file(input_file: str, output_file: str, expected_fields: int = 21, 
                 max_rows: Optional[int] = None) -> tuple:
    """
    Fix CSV file by extracting only properly formatted rows.
    
    Args:
        input_file: Path to malformed CSV file
        output_file: Path to output clean CSV file
        expected_fields: Expected number of CSV fields
        max_rows: Maximum rows to process (for testing)
        
    Returns:
        Tuple of (valid_rows, invalid_rows, total_rows)
    """
    print(f"🔧 Fixing CSV file: {input_file}")
    print(f"📝 Output file: {output_file}")
    print(f"🎯 Expected fields: {expected_fields}")
    
    valid_rows = 0
    invalid_rows = 0
    total_rows = 0
    
    start_time = time.time()
    
    try:
        with bz2.open(input_file, 'rt', encoding='utf-8') as infile:
            # Read and write header
            header = infile.readline().strip()
            header_fields = header.split(',')
            
            print(f"📋 Header fields: {len(header_fields)}")
            print(f"📋 Header: {header[:100]}...")
            
            with open(output_file, 'w', encoding='utf-8') as outfile:
                # Write header
                outfile.write(header + '\n')
                
                for line_num, line in enumerate(infile, 1):
                    total_rows += 1
                    line = line.strip()
                    
                    if max_rows and total_rows > max_rows:
                        break
                    
                    # Check if line looks like a proper CSV row
                    if not line or not line.startswith('"'):
                        invalid_rows += 1
                        continue
                    
                    # Count fields by splitting on commas (simple approach)
                    # This won't handle embedded commas perfectly, but should work for most cases
                    field_count = line.count(',') + 1
                    
                    # More sophisticated field counting for quoted CSV
                    try:
                        # Try to parse as CSV to get accurate field count
                        reader = csv.reader([line])
                        row = next(reader)
                        actual_fields = len(row)
                        
                        # Check if row has correct number of fields
                        if actual_fields == expected_fields:
                            # Additional validation: check if first and last fields look reasonable
                            opinion_id = row[0].strip('"')
                            cluster_id = row[-1].strip('"')
                            
                            if opinion_id.isdigit() and cluster_id.isdigit():
                                outfile.write(line + '\n')
                                valid_rows += 1
                            else:
                                invalid_rows += 1
                        else:
                            invalid_rows += 1
                            
                    except Exception:
                        # If CSV parsing fails, it's definitely invalid
                        invalid_rows += 1
                    
                    # Progress update every 100K rows
                    if total_rows % 100000 == 0:
                        elapsed = time.time() - start_time
                        valid_pct = (valid_rows / total_rows) * 100 if total_rows > 0 else 0
                        print(f"📊 Progress: {total_rows:,} rows, {valid_rows:,} valid ({valid_pct:.1f}%), {elapsed:.1f}s")
        
        elapsed = time.time() - start_time
        valid_pct = (valid_rows / total_rows) * 100 if total_rows > 0 else 0
        
        print(f"\n✅ CSV Fixing Complete:")
        print(f"   Total rows processed: {total_rows:,}")
        print(f"   Valid rows: {valid_rows:,} ({valid_pct:.1f}%)")
        print(f"   Invalid rows: {invalid_rows:,}")
        print(f"   Processing time: {elapsed:.1f}s")
        print(f"   Output file: {output_file}")
        
        return valid_rows, invalid_rows, total_rows
        
    except Exception as e:
        print(f"❌ Error fixing CSV file: {e}")
        return 0, 0, 0

def test_fixed_csv(csv_file: str, sample_size: int = 10) -> bool:
    """Test that the fixed CSV file is properly formatted."""
    print(f"\n🧪 Testing fixed CSV file: {csv_file}")
    
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            headers = reader.fieldnames
            
            print(f"📋 Headers ({len(headers)}): {headers}")
            
            sample_rows = []
            for i, row in enumerate(reader):
                if i >= sample_size:
                    break
                sample_rows.append(row)
            
            print(f"📝 Sample rows:")
            for i, row in enumerate(sample_rows[:3]):
                opinion_id = row.get('id', 'N/A')
                cluster_id = row.get('cluster_id', 'N/A')
                doc_type = row.get('type', 'N/A')
                print(f"   Row {i+1}: ID={opinion_id}, cluster_id={cluster_id}, type={doc_type}")
            
            print(f"✅ Fixed CSV appears to be properly formatted")
            return True
            
    except Exception as e:
        print(f"❌ Error testing fixed CSV: {e}")
        return False

def main():
    """Main CSV fixer function."""
    print("🔧 CSV Line Fixer for CourtListener Opinions")
    print("=" * 60)
    
    input_file = "bulk_csv/opinions-2025-07-02.csv.bz2"
    output_file = "bulk_csv/opinions-2025-07-02-fixed.csv"
    
    # Test with first 1M rows to see the data quality
    print("🧪 Testing with first 1M rows...")
    valid, invalid, total = fix_csv_file(
        input_file=input_file,
        output_file=output_file,
        expected_fields=21,
        max_rows=1000000
    )
    
    if valid > 0:
        print(f"\n🎯 Found {valid:,} valid rows in first 1M rows ({(valid/total)*100:.1f}%)")
        
        # Test the output
        if test_fixed_csv(output_file):
            print(f"\n✅ SUCCESS: Fixed CSV is ready for processing")
            print(f"📋 Next steps:")
            print(f"   1. Use the fixed CSV: {output_file}")
            print(f"   2. Run loader with: --csv-file {output_file}")
            print(f"   3. Expected processing rate: {(valid/total)*100:.1f}% of rows")
            return 0
        else:
            print(f"❌ Fixed CSV failed validation")
            return 1
    else:
        print(f"❌ No valid rows found in first 1M rows")
        print(f"💡 The CSV file may have severe formatting issues")
        return 1

if __name__ == "__main__":
    exit(main())
