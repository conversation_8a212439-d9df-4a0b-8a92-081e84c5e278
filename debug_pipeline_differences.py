#!/usr/bin/env python3
"""
Debug Pipeline Configuration Differences
Find the exact differences between working debug script and failing enhanced pipeline
"""

import asyncio
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_pipeline_differences():
    """Compare working debug script configuration vs enhanced pipeline configuration"""
    logger.info("🔍 DEBUGGING PIPELINE CONFIGURATION DIFFERENCES")
    logger.info("=" * 70)
    
    try:
        import vertexai
        from vertexai.generative_models import GenerationConfig
        from neo4j_graphrag.llm import VertexAILLM
        from neo4j_graphrag.experimental.pipeline.kg_builder import SimpleKGPipeline
        from neo4j_graphrag.experimental.components.text_splitters.langchain import LangChainTextSplitterAdapter
        from langchain_text_splitters import RecursiveCharacterTextSplitter
        from neo4j_graphrag.experimental.components.schema import GraphSchema, NodeType, RelationshipType
        from neo4j import GraphDatabase
        
        # Initialize Vertex AI
        vertex_project_id = os.getenv("VERTEX_PROJECT_ID")
        vertex_location = os.getenv("VERTEX_LOCATION", "us-central1")
        
        if vertex_project_id:
            vertexai.init(project=vertex_project_id, location=vertex_location)
        
        # Test text
        test_text = """
        Case: Anderson v. Memorial Hermann Hospital
        Court: Harris County District Court
        Date: 2023-11-01
        Docket: 2023-QUALITY-001

        In Anderson v. Memorial Hermann Hospital, plaintiff Sarah Anderson sued Memorial Hermann 
        Hospital for medical malpractice. Judge Robert Wilson presided over the case in the 
        157th Judicial District Court of Harris County, Texas.
        
        Attorney Jennifer Martinez of Martinez Law Firm represented plaintiff Anderson, while 
        Attorney Michael Davis of Healthcare Defense Group represented Memorial Hermann Hospital.
        
        The case involved surgical complications during Anderson's gallbladder removal performed 
        by Dr. Lisa Chen at Memorial Hermann Hospital on January 15, 2022. The jury awarded 
        plaintiff $150,000 in damages.
        """
        
        # Initialize Neo4j connection
        driver = GraphDatabase.driver(
            os.getenv("NEO4J_URI"),
            auth=(os.getenv("NEO4J_USERNAME", "neo4j"), os.getenv("NEO4J_PASSWORD"))
        )
        
        # Schema - exactly matching working debug script
        node_types = [
            NodeType(label="Person", description="People involved in legal cases"),
            NodeType(label="Organization", description="Organizations and institutions"),
            NodeType(label="Case", description="Legal cases and proceedings"),
        ]
        
        relationship_types = [
            RelationshipType(label="REPRESENTED", description="Attorney represented party"),
            RelationshipType(label="SUED", description="Party sued another party"),
            RelationshipType(label="PRESIDED_OVER", description="Judge presided over case"),
        ]
        
        schema = GraphSchema(
            node_types=tuple(node_types),
            relationship_types=tuple(relationship_types),
            patterns=()
        )
        
        # VoyageAI embedder
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import VoyageAIEmbeddings
        
        embedder = VoyageAIEmbeddings(
            model="voyage-3-large",
            api_key=os.getenv("VOYAGE_API_KEY"),
            output_dimension=1024
        )
        
        # Test configurations - difference 1: LLM configuration
        llm_configs = {
            "working_debug": {
                "name": "Working Debug Script LLM",
                "llm": VertexAILLM(
                    model_name="gemini-2.0-flash-exp",
                    model_params={
                        "temperature": 0,
                        "response_format": {"type": "json_object"}
                    }
                )
            },
            "enhanced_pipeline": {
                "name": "Enhanced Pipeline LLM",  
                "llm": VertexAILLM(
                    model_name="gemini-2.0-flash-exp",
                    generation_config=GenerationConfig(
                        temperature=0.0,
                        response_mime_type="application/json"
                    ),
                    model_params={
                        "max_tokens": 2000,
                        "temperature": 0,
                        "response_format": {"type": "json_object"}
                    }
                )
            }
        }
        
        # Test configurations - difference 2: Text splitter configuration
        splitter_configs = {
            "working_debug": {
                "name": "Working Debug Splitter",
                "splitter": LangChainTextSplitterAdapter(
                    text_splitter=RecursiveCharacterTextSplitter(
                        chunk_size=2000,
                        chunk_overlap=200,
                        length_function=len
                    )
                )
            },
            "enhanced_pipeline": {
                "name": "Enhanced Pipeline Splitter",
                "splitter": LangChainTextSplitterAdapter(
                    text_splitter=RecursiveCharacterTextSplitter(
                        chunk_size=2000,
                        chunk_overlap=200,
                        length_function=len,
                        separators=[
                            "\\n\\n\\n",  # Major section breaks
                            "\\n\\n",    # Paragraph breaks
                            "\\n",      # Line breaks
                            ". ",      # Sentence endings
                            "; ",      # Clause breaks
                            ", ",      # Comma breaks
                            " ",       # Word breaks
                            ""         # Character breaks
                        ],
                        keep_separator=True
                    )
                )
            }
        }
        
        # Test all combinations
        results = {}
        
        for llm_key, llm_config in llm_configs.items():
            for splitter_key, splitter_config in splitter_configs.items():
                config_name = f"{llm_key}_{splitter_key}"
                
                logger.info(f"🧪 Testing Configuration: {config_name}")
                logger.info(f"   LLM: {llm_config['name']}")
                logger.info(f"   Splitter: {splitter_config['name']}")
                
                try:
                    # Clean database
                    with driver.session() as session:
                        session.run("MATCH (n:__KGBuilder__) DETACH DELETE n")
                    
                    # Create pipeline
                    kg_pipeline = SimpleKGPipeline(
                        llm=llm_config["llm"],
                        embedder=embedder,
                        driver=driver,
                        text_splitter=splitter_config["splitter"],
                        from_pdf=False,
                        schema=schema,
                        on_error=None  # Test if this makes a difference
                    )
                    
                    # Process
                    result = await kg_pipeline.run_async(text=test_text)
                    
                    # Check what was stored
                    with driver.session() as session:
                        # Count entities
                        node_result = session.run("""
                            MATCH (n:__KGBuilder__)
                            WHERE NOT n:Chunk AND n.name IS NOT NULL
                            RETURN count(n) as entity_count,
                                   collect(DISTINCT n.name)[0..3] as sample_names
                        """)
                        node_record = node_result.single()
                    
                    entity_count = node_record['entity_count']
                    sample_names = node_record['sample_names']
                    
                    logger.info(f"   ✅ Success: {entity_count} entities")
                    if entity_count > 0:
                        logger.info(f"      Sample: {sample_names}")
                    
                    results[config_name] = {
                        "success": True,
                        "entity_count": entity_count,
                        "sample_names": sample_names
                    }
                    
                except Exception as e:
                    logger.error(f"   ❌ Failed: {e}")
                    results[config_name] = {
                        "success": False,
                        "error": str(e)
                    }
                
                logger.info("")
        
        # Analysis
        logger.info("🎯 CONFIGURATION ANALYSIS:")
        
        working_configs = []
        failing_configs = []
        
        for config_name, result in results.items():
            if result["success"] and result["entity_count"] > 0:
                working_configs.append(config_name)
                logger.info(f"   ✅ {config_name}: {result['entity_count']} entities")
            else:
                failing_configs.append(config_name)
                error_msg = result.get("error", "0 entities extracted")
                logger.info(f"   ❌ {config_name}: {error_msg}")
        
        logger.info("")
        logger.info(f"Working configurations: {len(working_configs)}")
        logger.info(f"Failing configurations: {len(failing_configs)}")
        
        # Identify the specific problematic component
        if len(working_configs) > 0 and len(failing_configs) > 0:
            logger.info("")
            logger.info("🔧 ROOT CAUSE ANALYSIS:")
            
            # Check if LLM config is the issue
            llm_issue = any("enhanced_pipeline" in config for config in failing_configs)
            splitter_issue = any("enhanced_pipeline" in config for config in failing_configs)
            
            if "working_debug_working_debug" in working_configs:
                logger.info("   ✅ Working debug script configuration works")
            
            if "enhanced_pipeline_enhanced_pipeline" in failing_configs:
                logger.info("   ❌ Enhanced pipeline configuration fails")
            
            # Mixed results would indicate which component is problematic
            mixed_results = []
            for config in results:
                if "_" in config:
                    parts = config.split("_", 1)
                    if len(parts) == 2:
                        mixed_results.append((parts[0], parts[1], results[config]["success"]))
            
            logger.info(f"   Mixed test results: {mixed_results}")
        
        driver.close()
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Configuration debugging failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(debug_pipeline_differences())
    
    if result:
        print(f"\n🔍 CONFIGURATION DEBUGGING COMPLETE")
        
        working = [k for k, v in result.items() if v.get("success") and v.get("entity_count", 0) > 0]
        failing = [k for k, v in result.items() if not v.get("success") or v.get("entity_count", 0) == 0]
        
        print(f"Working: {working}")
        print(f"Failing: {failing}")
        
        if working and failing:
            print(f"\n🚨 Found configuration differences causing the issue!")
        else:
            print(f"\n⚠️  All configurations behave similarly")
    else:
        print(f"\n❌ Configuration debugging failed")