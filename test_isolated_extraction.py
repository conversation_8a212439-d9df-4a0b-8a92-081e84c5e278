#!/usr/bin/env python3
"""
Isolated Entity Extraction Test
Clean room test of GraphRAG entity extraction with proper run isolation
"""

import asyncio
import os
import logging
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_isolated_extraction():
    """Test entity extraction with clean database and run isolation"""
    logger.info("🧪 ISOLATED ENTITY EXTRACTION TEST")
    logger.info("=" * 60)
    
    try:
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import EnhancedGraphRAGPipeline
        from courtlistener.processing.src.processing.cost_monitor import CostMonitor
        
        # Initialize cost monitor
        cost_monitor = CostMonitor()
        
        # Create controlled test document with known entities
        test_document = {
            "id": "isolated_test_001",
            "case_name": "<PERSON> v. Memorial Hermann Hospital", 
            "court": {"name": "Harris County District Court"},
            "date_filed": "2023-11-01",
            "docket_number": "2023-ISOLATED-001",
            "plain_text": """
            In Anderson v. Memorial Hermann Hospital, plaintiff <PERSON> <PERSON> sued Memorial Hermann 
            Hospital for medical malpractice. Judge Robert <PERSON> presided over the case in the 
            157th Judicial District Court of Harris County, Texas.
            
            Attorney Jennifer Martinez of Martinez Law Firm represented plaintiff Anderson, while 
            Attorney Michael Davis of Healthcare Defense Group represented Memorial Hermann Hospital.
            
            The case involved surgical complications during Anderson's gallbladder removal performed 
            by Dr. Lisa Chen at Memorial Hermann Hospital on January 15, 2022. The jury awarded 
            plaintiff $150,000 in damages.
            """
        }
        
        # Expected entities for validation
        expected_entities = {
            "Sarah Anderson": "Plaintiff",
            "Memorial Hermann Hospital": "Defendant", 
            "Robert Wilson": "Judge",
            "Jennifer Martinez": "Attorney",
            "Michael Davis": "Attorney",
            "Lisa Chen": "Doctor",
            "Harris County District Court": "Court",
            "157th Judicial District Court": "Court",
            "Martinez Law Firm": "LawFirm",
            "Healthcare Defense Group": "LawFirm",
            "$150,000": "Damages"
        }
        
        logger.info("📋 Test Document:")
        logger.info(f"   Case: {test_document['case_name']}")
        logger.info(f"   Expected Entities: {len(expected_entities)}")
        logger.info("")
        
        # Initialize pipeline with clean database
        logger.info("🔧 Initializing pipeline with database cleanup...")
        pipeline = EnhancedGraphRAGPipeline(
            cost_monitor=cost_monitor,
            neo4j_uri=os.getenv("NEO4J_URI"),
            neo4j_user=os.getenv("NEO4J_USERNAME", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD"),
            gemini_api_key=os.getenv("GEMINI_API_KEY", "unused"),
            voyage_api_key=os.getenv("VOYAGE_API_KEY"),
            practice_area="personal_injury",
            clean_database=True  # This will clean database before processing
        )
        
        logger.info("✅ Pipeline initialized with clean database")
        logger.info(f"🆔 Run ID: {pipeline.run_id}")
        logger.info("")
        
        # Process the test document
        logger.info("🔄 Processing test document...")
        start_time = datetime.utcnow()
        result = await pipeline.process_documents([test_document], batch_size=1)
        end_time = datetime.utcnow()
        
        # Get detailed run statistics
        run_stats = pipeline.get_run_statistics()
        
        # Log comprehensive results
        logger.info("=" * 60)
        logger.info("🎯 ISOLATED EXTRACTION TEST RESULTS")
        logger.info("=" * 60)
        logger.info(f"Processing Time: {(end_time - start_time).total_seconds():.1f} seconds")
        logger.info("")
        
        logger.info("📊 EXTRACTION STATISTICS:")
        logger.info(f"   Documents Processed: {result['processed']}")
        logger.info(f"   Documents Failed: {result['failed']}")
        logger.info(f"   Entities Extracted: {result['entities_extracted']}")
        logger.info(f"   Relationships Extracted: {result['relationships_extracted']}")
        logger.info("")
        
        logger.info("🏷️  ENTITY TYPE BREAKDOWN:")
        if run_stats['entity_types']:
            for entity_type in run_stats['entity_types']:
                logger.info(f"   {entity_type['type']}: {entity_type['count']} entities")
        else:
            logger.info("   No entities found!")
        logger.info("")
        
        logger.info("📋 SAMPLE EXTRACTED ENTITIES:")
        if run_stats['sample_entities']:
            for entity in run_stats['sample_entities']:
                logger.info(f"   {entity['type']}: {entity['name']}")
        else:
            logger.info("   No sample entities available!")
        logger.info("")
        
        # Validate against expected entities
        logger.info("🎯 ENTITY VALIDATION:")
        found_expected = 0
        total_expected = len(expected_entities)
        
        for expected_name, expected_type in expected_entities.items():
            found = False
            for sample_entity in run_stats['sample_entities']:
                if expected_name.lower() in sample_entity['name'].lower():
                    found = True
                    found_expected += 1
                    break
            
            status = "✅" if found else "❌"
            logger.info(f"   {status} {expected_name} ({expected_type})")
        
        validation_score = (found_expected / total_expected) * 100 if total_expected > 0 else 0
        logger.info(f"   Expected Entity Coverage: {validation_score:.1f}% ({found_expected}/{total_expected})")
        logger.info("")
        
        # Overall assessment
        logger.info("🏆 OVERALL ASSESSMENT:")
        success_criteria = {
            "documents_processed": result['processed'] > 0,
            "entities_extracted": result['entities_extracted'] > 0,
            "reasonable_entity_count": 5 <= result['entities_extracted'] <= 50,
            "entity_validation": validation_score >= 30.0,  # At least 30% of expected entities
            "no_failures": result['failed'] == 0
        }
        
        passed_criteria = sum(success_criteria.values())
        total_criteria = len(success_criteria)
        
        for criterion, passed in success_criteria.items():
            status = "✅" if passed else "❌"
            logger.info(f"   {status} {criterion.replace('_', ' ').title()}: {passed}")
        
        overall_success = passed_criteria >= 4  # At least 4/5 criteria must pass
        
        if overall_success:
            logger.info("")
            logger.info("🎉 SUCCESS: Isolated entity extraction working correctly!")
            logger.info("   ✅ Database isolation functional")
            logger.info("   ✅ Run-specific tracking operational")
            logger.info("   ✅ Entity extraction producing reasonable results")
            logger.info("   ✅ Text processing working correctly")
        else:
            logger.warning("")
            logger.warning("⚠️  Partial success: Some issues remain")
            logger.warning(f"   Passed {passed_criteria}/{total_criteria} criteria")
        
        pipeline.close()
        
        return {
            "success": overall_success,
            "run_id": pipeline.run_id,
            "entities_extracted": result['entities_extracted'],
            "relationships_extracted": result['relationships_extracted'],
            "validation_score": validation_score,
            "criteria_passed": passed_criteria,
            "criteria_total": total_criteria,
            "entity_types": run_stats['entity_types'],
            "sample_entities": run_stats['sample_entities'][:5]  # Top 5 samples
        }
        
    except Exception as e:
        logger.error(f"❌ Isolated extraction test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_isolated_extraction())
    
    if result and result.get("success"):
        print(f"\n🏆 ISOLATED EXTRACTION TEST SUCCESSFUL!")
        print(f"   Extracted {result['entities_extracted']} entities and {result['relationships_extracted']} relationships")
        print(f"   Validation score: {result['validation_score']:.1f}%")
        print(f"   Passed {result['criteria_passed']}/{result['criteria_total']} success criteria")
        print("   GraphRAG entity extraction is now working correctly with proper isolation!")
    else:
        print("\n❌ Isolated extraction test failed or had issues")
        print("   Check the detailed logs above for analysis")