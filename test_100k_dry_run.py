#!/usr/bin/env python3
"""
100K Row Dry-Run Test with PostgreSQL CSV Parameters
Tests the updated CSV reading approach with Texas cluster filtering.
"""

import bz2
import csv
import time
from typing import Set, Dict

# Set CSV field size limit for large legal documents
csv.field_size_limit(10**9)

def load_texas_cluster_sample(clusters_file: str, sample_size: int = 50000) -> Set[str]:
    """Load a sample of Texas cluster IDs for fast lookup."""
    print(f"🔍 Loading Texas cluster sample from {clusters_file}")
    texas_clusters = set()
    
    try:
        with bz2.open(clusters_file, 'rt', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for i, row in enumerate(reader):
                if i >= sample_size:
                    break
                
                # Check for Texas indicators in case name
                case_name = (row.get('case_name') or '').lower()
                case_name_full = (row.get('case_name_full') or '').lower()
                
                texas_indicators = [
                    'texas', 'tex.', 'state of texas', 'tex app', 'tex sup',
                    'dallas', 'houston', 'austin', 'san antonio', 'fort worth',
                    'tex. app.', 'tex. sup.', 'tex. crim.', 'tex. ct.',
                    'court of appeals of texas', 'supreme court of texas'
                ]
                
                text_to_check = f"{case_name} {case_name_full}"
                if any(indicator in text_to_check for indicator in texas_indicators):
                    cluster_id = row.get('id')
                    if cluster_id and cluster_id.isdigit():
                        texas_clusters.add(cluster_id)
        
        print(f"✅ Loaded {len(texas_clusters)} Texas cluster IDs for matching")
        return texas_clusters
        
    except Exception as e:
        print(f"❌ Error loading Texas clusters: {e}")
        return set()

def classify_practice_area(case_name: str, content: str) -> Dict:
    """Simple practice area classification."""
    content_lower = content.lower()
    case_name_lower = case_name.lower()
    
    if any(keyword in content_lower or keyword in case_name_lower 
           for keyword in ["medical malpractice", "doctor", "hospital", "surgery", "physician"]):
        return {
            "practice_areas": ["Medical Malpractice", "Personal Injury"],
            "primary_practice_area": "Medical Malpractice",
            "confidence": 0.85
        }
    elif any(keyword in content_lower or keyword in case_name_lower 
             for keyword in ["personal injury", "accident", "negligence", "injury", "damages", "tort"]):
        return {
            "practice_areas": ["Personal Injury"],
            "primary_practice_area": "Personal Injury", 
            "confidence": 0.80
        }
    else:
        return {
            "practice_areas": ["Other"],
            "primary_practice_area": "Other",
            "confidence": 0.50
        }

def test_100k_opinions(opinions_file: str, texas_clusters: Set[str], limit: int = 100000) -> Dict:
    """Test processing 100K opinion rows with PostgreSQL CSV parameters."""
    print(f"🔍 Testing 100K opinions from {opinions_file}")
    
    stats = {
        'rows_read': 0,
        'texas_matches': 0,
        'pi_mm_cases': 0,
        'other_cases': 0,
        'errors': 0,
        'sample_cases': []
    }
    
    start_time = time.time()
    
    try:
        with bz2.open(opinions_file, 'rt', encoding='utf-8', newline='') as f:
            # Use PostgreSQL-compatible CSV parameters
            reader = csv.DictReader(f, delimiter=',', quotechar='"',
                                  escapechar='\\', doublequote=False,
                                  strict=True)
            
            for row in reader:
                stats['rows_read'] += 1
                
                if stats['rows_read'] > limit:
                    break
                
                try:
                    # Extract key fields
                    opinion_id = row.get('id', '')
                    cluster_id = row.get('cluster_id', '')
                    plain_text = row.get('plain_text', '')
                    doc_type = row.get('type', '')
                    
                    # Check if this is a Texas case
                    if cluster_id in texas_clusters:
                        stats['texas_matches'] += 1
                        
                        # Classify practice area
                        classification = classify_practice_area('', plain_text)
                        primary_area = classification.get('primary_practice_area', 'Other')
                        
                        if primary_area in ['Personal Injury', 'Medical Malpractice']:
                            stats['pi_mm_cases'] += 1
                            
                            # Collect sample
                            if len(stats['sample_cases']) < 5:
                                stats['sample_cases'].append({
                                    'opinion_id': opinion_id,
                                    'cluster_id': cluster_id,
                                    'practice_area': primary_area,
                                    'confidence': classification.get('confidence', 0.0),
                                    'doc_type': doc_type,
                                    'text_preview': plain_text[:100]
                                })
                        else:
                            stats['other_cases'] += 1
                
                except Exception as e:
                    stats['errors'] += 1
                    if stats['errors'] <= 5:  # Log first few errors
                        print(f"⚠️  Row {stats['rows_read']} error: {e}")
                
                # Progress update every 10K rows
                if stats['rows_read'] % 10000 == 0:
                    elapsed = time.time() - start_time
                    texas_rate = (stats['texas_matches'] / stats['rows_read']) * 100
                    pi_rate = (stats['pi_mm_cases'] / stats['rows_read']) * 100
                    print(f"📊 Progress: {stats['rows_read']:,} rows, {stats['texas_matches']:,} TX ({texas_rate:.2f}%), {stats['pi_mm_cases']:,} PI/MM ({pi_rate:.2f}%), {elapsed:.1f}s")
        
        elapsed = time.time() - start_time
        stats['elapsed'] = elapsed
        
        return stats
        
    except Exception as e:
        print(f"❌ Error processing opinions: {e}")
        import traceback
        traceback.print_exc()
        return stats

def main():
    """Main test function."""
    print("🧪 100K Row Dry-Run Test with PostgreSQL CSV Parameters")
    print("=" * 70)
    
    clusters_file = "bulk_csv/opinion-clusters-2025-07-02.csv.bz2"
    opinions_file = "bulk_csv/opinions-2025-07-02.csv.bz2"
    
    # Step 1: Load Texas clusters (much larger sample)
    texas_clusters = load_texas_cluster_sample(clusters_file, sample_size=500000)
    if not texas_clusters:
        print("❌ Failed to load Texas clusters")
        return 1
    
    # Step 2: Test 100K opinions
    stats = test_100k_opinions(opinions_file, texas_clusters, limit=100000)
    
    # Step 3: Report results
    print(f"\n📊 DRY-RUN RESULTS:")
    print(f"   Rows scanned: {stats['rows_read']:,}")
    print(f"   Texas matches: {stats['texas_matches']:,}")
    print(f"   PI/MM cases: {stats['pi_mm_cases']:,}")
    print(f"   Other cases: {stats['other_cases']:,}")
    print(f"   Errors: {stats['errors']:,}")
    print(f"   Processing time: {stats.get('elapsed', 0):.1f}s")
    
    # Calculate rates
    if stats['rows_read'] > 0:
        texas_rate = (stats['texas_matches'] / stats['rows_read']) * 100
        pi_rate = (stats['pi_mm_cases'] / stats['rows_read']) * 100
        print(f"   Texas match rate: {texas_rate:.3f}%")
        print(f"   PI/MM ingest rate: {pi_rate:.3f}%")
        
        # Goal check
        if pi_rate >= 1.0:
            print(f"✅ SUCCESS: Achieved ≥1% PI/MM ingest rate!")
        elif stats['pi_mm_cases'] >= 1000:
            print(f"✅ SUCCESS: Found ≥1,000 PI/MM cases!")
        else:
            print(f"⚠️  Below target: Need ≥1% rate or ≥1,000 cases")
    
    # Show sample cases
    if stats['sample_cases']:
        print(f"\n📝 SAMPLE PI/MM CASES:")
        for i, case in enumerate(stats['sample_cases'], 1):
            print(f"   {i}. ID={case['opinion_id']}, cluster={case['cluster_id']}")
            print(f"      Practice area: {case['practice_area']} ({case['confidence']:.2f})")
            print(f"      Text preview: {case['text_preview']}...")
            print()
    
    # Success criteria
    success = stats['pi_mm_cases'] >= 1000 or (stats['rows_read'] > 0 and (stats['pi_mm_cases'] / stats['rows_read']) >= 0.01)
    
    if success:
        print(f"🎯 READY FOR FULL IMPORT!")
        print(f"   Remove --dry-run and let full import run overnight")
        return 0
    else:
        print(f"🔧 NEEDS INVESTIGATION:")
        print(f"   Check data quality and filtering logic")
        return 1

if __name__ == "__main__":
    exit(main())
