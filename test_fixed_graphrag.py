#!/usr/bin/env python3
"""
Test the Fixed GraphRAG Pipeline
Quick verification that the JSON formatting fix works
"""

import asyncio
import os
import logging
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_fixed_pipeline():
    """Test the fixed GraphRAG pipeline with a sample case"""
    logger.info("🧪 Testing Fixed GraphRAG Pipeline")
    
    try:
        # Import the fixed pipeline
        from courtlistener.processing.src.processing.enhanced_graphrag_pipeline import EnhancedGraphRAGPipeline
        from courtlistener.processing.src.processing.cost_monitor import CostMonitor
        
        # Initialize cost monitor
        cost_monitor = CostMonitor()
        
        # Create sample document (similar to our real cases)
        sample_document = {
            "id": "test_case_001",
            "case_name": "Test v. Fixed Pipeline",
            "court": {"name": "Harris County District Court"},
            "date_filed": "2023-03-15",
            "docket_number": "2023-12345",
            "plain_text": """
            This is a personal injury case where <PERSON><PERSON><PERSON> sued Defendant <PERSON> 
            for damages resulting from a motor vehicle accident. Judge <PERSON> presided over 
            the case in Harris County District Court. The jury awarded $50,000 in actual damages 
            and $25,000 in punitive damages. Attorney <PERSON> <PERSON> represented the plaintiff, 
            while Attorney Robert <PERSON> represented the defendant.
            """
        }
        
        # Initialize pipeline with environment credentials
        pipeline = EnhancedGraphRAGPipeline(
            cost_monitor=cost_monitor,
            neo4j_uri=os.getenv("NEO4J_URI"),
            neo4j_user=os.getenv("NEO4J_USERNAME", "neo4j"),
            neo4j_password=os.getenv("NEO4J_PASSWORD"),
            gemini_api_key=os.getenv("GEMINI_API_KEY", "unused"),  # Not needed for Vertex
            voyage_api_key=os.getenv("VOYAGE_API_KEY"),
            practice_area="personal_injury"
        )
        
        logger.info("✅ Pipeline initialized successfully")
        
        # Test processing a single document
        logger.info("Processing sample document...")
        result = await pipeline.process_documents([sample_document])
        
        # Check results
        logger.info(f"✅ Processing completed:")
        logger.info(f"   Processed: {result['processed']}")
        logger.info(f"   Failed: {result['failed']}")
        logger.info(f"   Entities extracted: {result['entities_extracted']}")
        logger.info(f"   Relationships extracted: {result['relationships_extracted']}")
        logger.info(f"   Total cost: ${result['costs']['total']:.4f}")
        
        if result['entities_extracted'] > 0:
            logger.info("🎉 SUCCESS: Entity extraction is now working!")
            return True
        else:
            logger.warning("⚠️  No entities extracted - may need further debugging")
            if result['errors']:
                logger.error(f"Errors: {result['errors']}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        if 'pipeline' in locals():
            pipeline.close()

if __name__ == "__main__":
    success = asyncio.run(test_fixed_pipeline())
    if success:
        print("\n🎉 GraphRAG Pipeline Fix Successful!")
    else:
        print("\n❌ GraphRAG Pipeline Still Needs Work")