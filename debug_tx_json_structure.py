#!/usr/bin/env python3
"""
Debug script to understand the actual JSON structure of TX/ files
"""

import json
import gzip
from google.cloud import storage
from dotenv import load_dotenv

load_dotenv()

def debug_tx_json_structure():
    try:
        # Initialize GCS client
        client = storage.Client()
        bucket = client.bucket("texas-laws-personalinjury")
        
        # Get one TX file to analyze
        blobs = list(bucket.list_blobs(prefix="TX/clusters/", max_results=1))
        
        if not blobs:
            print("❌ No TX files found")
            return
        
        blob = blobs[0]
        print(f"🔍 Analyzing: {blob.name}")
        
        # Download and decompress
        content_bytes = blob.download_as_bytes()
        if blob.name.endswith('.gz'):
            content_bytes = gzip.decompress(content_bytes)
        
        # Parse JSON
        data = json.loads(content_bytes.decode('utf-8'))
        
        print(f"\n📊 JSON Structure:")
        print(f"Type: {type(data)}")
        
        if isinstance(data, dict):
            print(f"Top-level keys: {list(data.keys())}")
            
            # Look for cluster information
            if 'cluster' in data:
                print(f"\n🎯 Cluster data found:")
                cluster = data['cluster']
                for key, value in cluster.items():
                    if isinstance(value, str) and len(value) < 200:
                        print(f"   {key}: {value}")
                    else:
                        print(f"   {key}: {type(value)} ({len(str(value))} chars)")
            
            # Look for sub_opinions (the actual field)
            if 'sub_opinions' in data:
                print(f"\n📄 Sub-opinions data found:")
                sub_opinions = data['sub_opinions']
                print(f"   Number of sub-opinions: {len(sub_opinions)}")
                print(f"   Sub-opinions type: {type(sub_opinions)}")
                if sub_opinions:
                    first_opinion = sub_opinions[0]
                    print(f"   First sub-opinion type: {type(first_opinion)}")
                    print(f"   First sub-opinion value: {first_opinion}")
                    
                    if isinstance(first_opinion, dict):
                        print(f"   First opinion keys: {list(first_opinion.keys())}")
                        
                        # Check for text content
                        if 'plain_text' in first_opinion:
                            text = first_opinion['plain_text']
                            print(f"   plain_text: {len(text)} chars")
                            if text:
                                print(f"   Preview: {text[:200]}...")
                        
                        if 'html' in first_opinion:
                            html = first_opinion['html']
                            print(f"   html: {len(html)} chars")
            
            # Also check for other content fields
            content_fields = ['summary', 'syllabus', 'headnotes', 'disposition', 'procedural_history']
            print(f"\n📝 Other content fields:")
            for field in content_fields:
                if field in data and data[field]:
                    content_length = len(str(data[field]))
                    print(f"   {field}: {content_length} chars")
                    if content_length < 500:
                        print(f"      Content: {data[field]}")
                    else:
                        print(f"      Preview: {str(data[field])[:200]}...")
            
            # Look for case name in various locations
            print(f"\n🔍 Searching for case name...")
            
            # Direct fields
            case_name_fields = ['case_name', 'caseName', 'title', 'caption']
            for field in case_name_fields:
                if field in data:
                    print(f"   Found {field}: {data[field]}")
                if 'cluster' in data and field in data['cluster']:
                    print(f"   Found cluster.{field}: {data['cluster'][field]}")
            
            # Check if it's the full data structure we need
            print(f"\n📋 Complete structure sample:")
            for key, value in list(data.items())[:5]:
                print(f"   {key}: {type(value)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_tx_json_structure()